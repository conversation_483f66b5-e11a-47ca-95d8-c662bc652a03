// validations/sessionLiveTracking.validation.js
const Joi = require('joi');
// Regex pour valider HH:mm ou --:--
const timeRegex = /^([0-1]\d|2[0-3]):([0-5]\d)$|^--:--$/;
const durationregex = /^\d+$/;
// Validation pour l'API /session/live
const liveEventSchema = Joi.object({
  idSession: Joi.string().required(),
  idBinome: Joi.string().required(),
  liveData: Joi.object({
    learnerHourIn: Joi.string().pattern(timeRegex).required(),
    learnerHourOut: Joi.string().pattern(timeRegex).required(),
    mentorHourIn: Joi.string().pattern(timeRegex).required(),
    mentorHourOut: Joi.string().pattern(timeRegex).required(),
    mentorStatus: Joi.string().valid('présent', 'absent').required(),
    learnerStatus: Joi.string().valid('présent', 'absent').required(),
  }).required()
}).custom((value, helpers) => {
  const parseTime = (str) => {
    const [h, m] = str.split(':').map(Number);
    return h * 60 + m;
  };

  const { mentorHourIn, mentorHourOut, learnerHourIn, learnerHourOut } = value.liveData;

  if (parseTime(mentorHourIn) >= parseTime(mentorHourOut)) {
    return helpers.message('mentorhourin doit être avant mentorhourout');
  }

  if (parseTime(learnerHourIn) >= parseTime(learnerHourOut)) {
    return helpers.message('learnerhourin doit être avant learnerhourout');
  }

  return value;
}, 'Contrôle des horaires');

// Validation pour l’API /session/close
const closeSessionSchema = Joi.object({
    idSession: Joi.string().required(),
    idBinome: Joi.string().required(),
    closeData: Joi.object({
      startTime: Joi.string().pattern(timeRegex).required(),
      endTime: Joi.string().pattern(timeRegex).required(),
      durationSession: Joi.string().pattern(durationregex).required(),
      totalConnectionTimeLearner: Joi.string().pattern(durationregex).required(),
      totalConnectionTimeMentor: Joi.string().pattern(durationregex).required(),
      statutPresenceLearner: Joi.string().valid('présent', 'absent').required(),
      statutPresenceMentor: Joi.string().valid('présent', 'absent').required(),
      seancestatut: Joi.string().valid('terminée').required()
    }).required()
  }).custom((value, helpers) => {
    const parseTime = (time) => {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    };
  
    const { startTime, endTime } = value.closeData;
  
    if (parseTime(startTime) >= parseTime(endTime)) {
      return helpers.message('startTime doit être avant endTime');
    }
  
    return value;
  }, 'Contrôle de cohérence des horaires');

module.exports = {
  liveEventSchema,
  closeSessionSchema
};
