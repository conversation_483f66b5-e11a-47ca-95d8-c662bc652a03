const mongoose = require("mongoose");

const userRole = require("../utils/constants/userRolesConstants.js");
const tutorConstants = require("../utils/constants/tutor.constants.js");

const userSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      index: {
        unique: true,
        sparse: false,
      },
    },
    firebaseIds: [
      {
        identifier: String,
        firebaseUserId: String,
        provider: String,
      },
    ],
    //  profilePic: String,
    status: {
      type: String,
      default: tutorConstants.statusOfTutors.TUTOR_ON_HOLD,
    },
    contactDetails: {
      firstName: String,
      lastName: String,
      phoneNumber: String,
      email: String,
    },
    address: {
      addressLine1: String,
      city: String,
      country: String,
      zipCode: String,
    },
    dateOfBirth: Date,
    gender: String,
    userRole: {
      type: String,
      default: userRole.ROLE_STANDARD_USER,
    },
    createdAt: {
      type: Date,
      immutable: true,
      default: () => Date.now(),
    },
    updatedAt: {
      type: Date,
    },
    iAgreeToReceiveInformationAndUpdates: {
      type: Boolean,
      default: false,
    },
    iAcceptTermsAndPolicyOfConfidentiality: {
      type: Boolean,
      default: false,
    },
    iAcceptTheCommitmentLetter: {
      type: Boolean,
      default: false,
    },
    is_suzali: {
      type: Boolean,
      default: false,
    },
    hasChangedPassword: {
      type: Boolean,
      default: false,
    },
    iAcceptToBeContactedForParticipatingInAStudy: {
      type: Boolean,
      default: false,
    },
    createdBy: String,
    lastLogin: Date,
  },
  {
    versionKey: false,
  }
);

module.exports = mongoose.model("User", userSchema);
