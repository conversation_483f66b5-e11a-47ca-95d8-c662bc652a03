const mongoose = require("mongoose");

const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const crScReportsSchema = new mongoose.Schema({
  typeOfReport: String, // Either "CR" or "SC Report"
  session_date: Date, // For CRs only
  creation_date: Date, // For SC Reports only
  entry_date: Date,
  // For CRs only
  sessionId: String,
  sessionType: String,
  tutors: [
    {
      fullName: String,
      userId: String,
      absence: {
        type: Boolean,
        default: false,
      },
      appreciation: { type: String, default: "" },
      rating: { type: Number, default: 0 },
    },
  ],
  students: [
    {
      userId: String,
      fullName: String,
      absence: {
        type: Boolean,
        default: false,
      },
      note: { type: String, default: "" },
      appreciation: { type: String, default: "" },
      rating: { type: Number, default: 0 },
    },
  ],
  grade: Number, // For CRs only
  status: String, // For CRs and SC Reports
  postponed_hours: Number, // For SC Reports only
  vsc: [
    {
      // For SC Reports only
      fullName: String,
      userId: String,
      absence: {
        type: Boolean,
        default: false,
      },
    },
  ],
  coordinator: {
    // For SC Reports only
    coordinatorFullName: String,
    userId: String,
  },
  proof_delivered: Boolean, // For SC Reports only
  contentOfSession: String,
  appreciation: String,
  createdAt: {
    type: Date,
    default: dateTimeHelper.getCurrentDateTimeInParisZone(),
  },
});

// Create Students indexes
crScReportsSchema.index({ 'students.userId': 1 });
crScReportsSchema.index({ 'students.absence': 1 });
crScReportsSchema.index({ 'students.userId': 1, 'students.absence': 1 });
console.log('Students indexes created');

// Create Tutors indexes
crScReportsSchema.index({ 'tutors.userId': 1 });
crScReportsSchema.index({ 'tutors.absence': 1 });
crScReportsSchema.index({ 'tutors.userId': 1, 'tutors.absence': 1 });
console.log('Tutors indexes created');
module.exports = mongoose.model("crscReports", crScReportsSchema);
