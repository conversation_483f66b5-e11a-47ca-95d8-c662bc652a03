const mongoose = require("mongoose");
const tutorConstants = require("../utils/constants/tutor.constants");

const parentPreferencesSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    index: {
      unique: true,
      sparse: false,
    },
  },
  isPartner: {
    type: Boolean,
    default: false,
  },
  partner: {
    type: String,
  },
  howDidYouHearAboutUs: String,
  invitation: {
    invitationCode: String,
    invitationAccepted: Boolean,
    invitationDate: {
      type: Date,
    },
    invitationAcceptedDate: {
      type: Date,
    },
  },
  comments: String,
  department: {
    departmentName: String,
    departmentId: String,
  },
  socioProfessionalCategory: {
    type: String,
  },
  program: {
    programId: String,
    programName: String,
  },
  mentor: {
    type: String,
  },
  club: {
    type: String,
  },
  onBoardingStep: {
    type: String,
    default: tutorConstants.onBoardingStep.ON_BOARDING_STEP_1,
  },
  quizScore: {
    type: Number,
    default: 0,
  },
  attempts: {
    type: Number,
    default: 0,
  },
  currentVideo: {
    type: Number,
    default: Number,
  },
  testHardware: {
    isWorkingVideoDevice: Boolean,
    isWorkingAudioDevice: Boolean,
  },
});

module.exports = mongoose.model("parentPreferences", parentPreferencesSchema);
