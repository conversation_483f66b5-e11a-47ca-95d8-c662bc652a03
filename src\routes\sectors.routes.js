const express = require("express");

const router = express.Router();

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("publicHolidays.routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import Sector Model
const Sector = require("../models/Sectors.Model.js");
const GovernmentSector = require("../models/GovernmentSectors.Model.js");
//import School Year Model
const ScholarYear = require("../models/ScholarYear.Model.js");

const establishmentModel = require("../models/Establishment.Model.js");

//import  School Zone Model
const SchoolZone = require("../models/SchoolZones.Model.js");

//import get.data.filter.order.js from path src/controllers/mongoDb/get.Data.Filter.Order.js
const getDataWithPaginationAndFilterAndOrder = require("../controllers/mongoDb/get.Data.Filter.Order.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const studentHelper = require("../controllers/students/student.helpers.js");

const tutorHelper = require("../controllers/tutor/tutor.helper.js");

const establishmentHelper = require("../controllers/establishments/establishments.helper.js");

const sectorHelper = require("../controllers/sectors/sectors.helper.js");

const userAdministrationHelper = require("../controllers/user/user.administration.helper.js");
const EducationAnnuaireModel = require("../models/EducationAnnuaire.Model.js");
const SectorsModel = require("../models/Sectors.Model.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  SUPER_ADMINISTRATOR,
  VSC,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");

// router.use((req, res, next) => {
//   verifyUserRole.checkUserRoleAndAccessLevel(
//     req,
//     res,
//     next,
//     mongodbModelConstants.modelName.PUBLIC_HOLIDAYS
//   );
// });

//sector dashboard
/**
 * @swagger
 * /api/v1/sector/dashboard:
 *   get:
 *     summary: Get sector dashboard data
 *     tags: [Sector]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for sectors
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search keyword
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: Number of sectors per page
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *     responses:
 *       '200':
 *         description: Successfully retrieved the sector dashboard
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of sectors and their dashboard data
 *                   items:
 *                     type: object
 *                     properties:
 *                       sectorId:
 *                         type: string
 *                         description: The ID of the sector
 *                       name:
 *                         type: string
 *                         description: The name of the sector
 *                       higherInstitutionCount:
 *                         type: integer
 *                         description: The number of higher institutions in the sector
 *                       collegesCount:
 *                         type: integer
 *                         description: The number of colleges in the sector
 *                       structuresCount:
 *                         type: integer
 *                         description: The number of structures in the sector
 *                       tutorsCount:
 *                         type: integer
 *                         description: The number of tutors in the sector
 *                       studentsCount:
 *                         type: integer
 *                         description: The number of students in the sector
 *                       coordinators:
 *                         type: array
 *                         description: List of coordinators in the sector
 *                         items:
 *                           type: string
 *                           description: The name of the coordinator
 *                 page:
 *                   type: integer
 *                   description: The current page number
 *                 pageSize:
 *                   type: integer
 *                   description: The number of sectors per page
 *                 total:
 *                   type: integer
 *                   description: The total number of sectors
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/sector/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const search = req.query.search;
      const pageSize = req.query.pageSize;
      const page = req.query.page;
      const sectorsData =
        await getDataWithPaginationAndFilterAndOrder.getDataWithPaginationAndFilterAndOrder(
          {
            mongooseModel: Sector,
            filter,
            sortBy,
            search: null,
            pageSize,
            page,
          }
        );

      if (!sectorsData.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get sector dashboard`,
          sectorsData.message
        );
        // logger
        //   .newLog(logger.getLoggerTypeConstants().admin)
        //   .error(response + sectorsData.message);
        return res.status(200).json(response);
      }

      // workhere

      let sectorDashboard = [];

      const listOfSectors = sectorsData.data.listOfData;
      for (let i = 0; i < listOfSectors.length; i++) {
        const sector = listOfSectors[i];
        const sectorId = sector._id;

        const studentsCount = await studentHelper.getNumberOfStudentsPerSector(
          sectorId
        );

        const tutorsCount = await tutorHelper.getNumberOfTutorsPerDepartment(
          sectorId
        );

        const higherInstitutionCount =
          await establishmentHelper.getNumberOfHigherInstitutionsPerSector(
            sectorId
          );
        const collegesCount = await establishmentHelper.getNumberOfCollege(
          sectorId
        );
        const structuresCount = await establishmentHelper.getNumberOfStructure(
          sectorId
        );
        const coordinatorData = sector.coordinators;
        let listOfCoordinators = [];
        for (let j = 0; j < coordinatorData.length; j++) {
          const coordinator = coordinatorData[j];
          const coordinatorUserId = coordinator.userId;
          const coordinatorProfile =
            await userAdministrationHelper.getAdminProfile(coordinatorUserId);
          if (coordinatorProfile.status) {
            listOfCoordinators.push(coordinatorProfile.data);
          }
        }

        //convert listOfCoordinators to array of strings
        if (listOfCoordinators.length > 0) {
          listOfCoordinators = listOfCoordinators.map((coordinator) => {
            return coordinator.fullName;
          });
        }

        let dashboardItem = {
          sectorId: sector._id,
          name: sector.name,
          higherInstitutionCount: higherInstitutionCount.data
            ? higherInstitutionCount.data
            : "0",
          collegesCount: collegesCount ?? "0",
          structuresCount: structuresCount ?? "0",
          tutorsCount: tutorsCount.data ? tutorsCount.data : "0",
          studentsCount: studentsCount.data ? studentsCount.data : "0",
          coordinators: listOfCoordinators,
        };

        sectorDashboard.push(dashboardItem);
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Sector dashboard`,
        sectorDashboard,
        sectorsData.data.page,
        sectorsData.data.pageSize,
        sectorsData.data.totalCount
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Failed to get sector dashboard`,
        error
      );
      // logger
      //   .newLog(logger.getLoggerTypeConstants().admin)
      //   .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//Get all sectors for export data to CSV
/**
 * @swagger
 * /api/v1/sector/dashboard/csv:
 *   get:
 *     summary: Get sector dashboard data in CSV format
 *     tags: [Sector]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for sectors
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Search keyword
 *     responses:
 *       '200':
 *         description: Successfully retrieved the sector dashboard in CSV format
 *         content:
 *           text/csv:
 *             schema:
 *               type: string
 *               format: binary
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/sector/dashboard/csv",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const search = req.query.search;
      const pageSize = 1000000;
      const page = 1;

      const sectorsData =
        await getDataWithPaginationAndFilterAndOrder.getDataWithPaginationAndFilterAndOrder(
          {
            mongooseModel: Sector,
            filter,
            sortBy,
            search: null,
            pageSize,
            page,
          }
        );

      if (!sectorsData.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get sector dashboard`,
          sectorsData.message
        );
        logger
          .newLog(logger.getLoggerTypeConstants().admin)
          .error(response + sectorsData.message);
        return res.status(200).json(response);
      }

      let sectorDashboard = [];

      const listOfSectors = sectorsData.data.listOfData;
      for (let i = 0; i < listOfSectors.length; i++) {
        const sector = listOfSectors[i];
        const sectorId = sector._id;

        const studentsCount = await studentHelper.getNumberOfStudentsPerSector(
          sectorId
        );

        const tutorsCount = await tutorHelper.getNumberOfTutorsPerDepartment(
          sectorId
        );

        const higherInstitutionCount =
          await establishmentHelper.getNumberOfHigherInstitutionsPerSector(
            sectorId
          );

        const coordinatorData = sector.coordinators;
        let listOfCoordinators = [];
        for (let j = 0; j < coordinatorData.length; j++) {
          const coordinator = coordinatorData[j];
          const coordinatorUserId = coordinator.userId;
          const coordinatorProfile =
            await userAdministrationHelper.getAdminProfile(coordinatorUserId);
          if (coordinatorProfile.status) {
            listOfCoordinators.push(coordinatorProfile.data);
          }
        }

        //convert listOfCoordinators to array of strings
        if (listOfCoordinators.length > 0) {
          listOfCoordinators = listOfCoordinators.map((coordinator) => {
            return coordinator.fullName;
          });
        }

        let dashboardItem = {
          sectorId: sector._id,
          name: sector.name,
          higherInstitutionCount: higherInstitutionCount.data
            ? higherInstitutionCount.data
            : "0",
          collegesCount: "0",
          tutorsCount: tutorsCount.data ? tutorsCount.data : "0",
          studentsCount: studentsCount.data ? studentsCount.data : "0",
          coordinators: listOfCoordinators,
        };

        sectorDashboard.push(dashboardItem);
      }
      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Sector dashboard`,
        sectorDashboard,
        sectorsData.data.page,
        sectorsData.data.pageSize,
        sectorsData.data.totalCount
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Failed to get sector dashboard`,
        error
      );
      // logger
      //   .newLog(logger.getLoggerTypeConstants().admin)
      //   .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//save new Sector
/**
 * @swagger
 * /api/v1/sector:
 *   post:
 *     summary: Create a new sector
 *     tags: [Sector]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               sector:
 *                 type: string
 *                 description: JSON string representing the sector data
 *     responses:
 *       '200':
 *         description: Successfully created a new sector
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 sector:
 *                   $ref: '#/components/schemas/Sector'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.post(
  "/sector",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const sector = JSON.parse(fields.sector);

        console.log(sector);

        //check if sector is provided
        if (!sector) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Sector is required to perform this action. Please provide a valid sector.`
          );
          return res.status(400).json(response);
        }

        //save new sector
        const newSector = new Sector({
          name: sector.name,
          program: sector.program,
          comments: sector.comments,
          schoolYear: sector.schoolYear,
          schoolZone: sector.schoolZone,
          coordinators: sector.coordinators,
          departments: sector.departments,
        });

        let sectorSaved = await newSector.save();

        if (!sectorSaved) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Failed to save new Sector`,
            `Please try again later.`
          );
          return res.status(400).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "New Sector saved successfully",
            newSector
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "Failed to save new Sector",
          error
        );
        logger
          .newLog(logger.getLoggerTypeConstants().admin)
          .error(response + error);
        return res.status(500).json(response);
      }
    });
  }
);

//get sector by sectorId
/**
 * @swagger
 * /api/v1/sector:
 *   get:
 *     summary: Get sector details by sectorId
 *     tags: [Sector]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the sector to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved sector details
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 sector:
 *                   $ref: '#/components/schemas/Sector'
 *                 schoolZones:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: ID of the school zone
 *                       name:
 *                         type: string
 *                         description: Name of the school zone
 *                 schoolYears:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/ScholarYear'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/sector", async (req, res) => {
  try {
    const sectorId = req.query.sectorId;

    //check if sectorId is provided
    if (!sectorId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `SectorId is required to get Sector. Please provide a valid sectorId.`
      );
      return res.status(400).json(response);
    }

    //get sector by sectorId
    const sector = await Sector.findOne({ _id: sectorId });

    if (!sector) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No Sector found with sectorId: ${sectorId}`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    }

    //get all school zones
    const schoolZonesDocument = await SchoolZone.find({});

    //remove schoolZoneHolidays from schoolZones
    let schoolZones = schoolZonesDocument.map((schoolZone) => {
      return { _id: schoolZone._id, name: schoolZone.zoneName };
    });

    //get all school years
    const schoolYears = await ScholarYear.find({});

    //combine sector with school zones and school years
    let sectorWithSchoolZonesAndSchoolYears = {
      sector: sector,
      schoolZones: schoolZones,
      schoolYears: schoolYears,
    };

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Sector found successfully",
      sectorWithSchoolZonesAndSchoolYears
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sector",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});
//get all government sectors
/**
 * @swagger
 * /api/v1/gov/sectors:
 *   get:
 *     summary: Get all government sectors
 *     tags: [Government Sectors]
 *     responses:
 *       '200':
 *         description: Successfully retrieved government sectors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 sectors:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/GovernmentSector'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/gov/sectors", async (req, res) => {
  try {
    //get all sectors
    const sectors = await GovernmentSector.find({});

    if (!sectors) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No Sectors found`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Sectors found successfully",
      sectors
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});

//get all sectors
/**
 * @swagger
 * /api/v1/sectors:
 *   get:
 *     summary: Get all sectors
 *     tags: [Sectors]
 *     responses:
 *       '200':
 *         description: Successfully retrieved sectors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 sectors:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Sector'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/sectors", async (req, res) => {
  try {
    //get all sectors
    const sectors = await Sector.find({});

    if (!sectors) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No Sectors found`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Sectors found successfully",
      sectors
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});

//update sector
/**
 * @swagger
 * /api/v1/sector:
 *   put:
 *     summary: Update a sector
 *     tags: [Sectors]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               sector:
 *                 type: string
 *                 description: JSON string representing the sector object to be updated
 *     responses:
 *       '200':
 *         description: Successfully updated sector
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 sector:
 *                   $ref: '#/components/schemas/Sector'
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.put(
  "/sector",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      const sector = JSON.parse(fields.sector);

      try {
        //check if sector is provided
        if (!sector) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Sector is required to perform this action. Please provide a valid sector.`
          );
          return res.status(400).json(response);
        }

        //check if sectorId is provided
        if (!sector._id) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `SectorId is required to update Sector. Please provide a valid sectorId.`
          );
          return res.status(400).json(response);
        }

        //update sector
        const updatedSector = await Sector.findOneAndUpdate(
          { _id: sector._id },
          sector,
          { new: true }
        );
        //update establishment based on sector
        const filter = {
          "generalInformation.sector.sectorId": sector._id,
        };

        const update = {
          $set: {
            "generalInformation.sector.sectorName": sector.name,
          },
        };

        const options = {
          arrayFilters: [{ "element.sectorId": sector._id }],
          new: true,
        };

        await EducationAnnuaireModel.findOneAndUpdate(filter, update, options);

        if (!updatedSector) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Failed to update Sector`,
            `Please try again later.`
          );
          return res.status(400).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "Sector updated successfully",
            updatedSector
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "Failed to update Sector",
          error
        );
        logger
          .newLog(logger.getLoggerTypeConstants().admin)
          .error(response + error);
        return res.status(500).json(response);
      }
    });
  }
);

//delete sector
/**
 * @swagger
 * /api/v1/sector:
 *   delete:
 *     summary: Delete a sector
 *     tags: [Sectors]
 *     parameters:
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the sector to be deleted
 *     responses:
 *       '200':
 *         description: Successfully deleted sector
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.delete(
  "/sector",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const sectorId = req.query.sectorId;

      //check if sectorId is provided
      if (!sectorId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `SectorId is required to delete Sector. Please provide a valid sectorId.`
        );
        return res.status(400).json(response);
      }

      //delete sector
      const deletedSector = await Sector.findOneAndDelete({ _id: sectorId });

      if (!deletedSector) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Failed to delete Sector`,
          `Please send a valid sectorId.`
        );
        return res.status(400).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "Sector deleted successfully",
          `All data related to Sector with sectorId: ${sectorId} has been deleted.`
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to delete Sector",
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().admin)
        .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//get all names of sectors or coordinators.coordinatorName for dropdown list autocomplete
/**
 * @swagger
 * /api/v1/sector/autoComplete:
 *   get:
 *     summary: Get sectors or coordinators for auto-complete
 *     tags: [Sectors]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: object
 *         required: false
 *         description: The filter object containing either `name` or `coordinatorName` field for auto-completion
 *     responses:
 *       '200':
 *         description: Successfully retrieved sectors or coordinators for auto-completion
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       sectorId:
 *                         type: string
 *                         description: The ID of the sector
 *                       name:
 *                         type: string
 *                         description: The name of the sector
 *       '400':
 *         description: Bad request or no sectors or coordinators found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/sector/autoComplete", async (req, res) => {
  try {
    let applyFilter;
    const filter = req.query.filter;
    if (filter) {
      try {
        applyFilter = JSON.parse(filter);
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invalid filter. Please provide a valid filter.`
        );
        return res.status(400).json(response);
      }
    } else {
      applyFilter = {};
    }

    let sectorsNames = [];
    let query = {};
    if (applyFilter.name || applyFilter.name.length == 0) {
      if (applyFilter.name) {
        query = { name: { $regex: applyFilter.name, $options: "i" } };
      }

      const sectors = await Sector.find(query).exec();
      if (!sectors) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No Sectors found`,
          `Please try again later.`
        );
        return res.status(200).json(response);
      }

      sectors.forEach((sector) => {
        let newSector = {
          sectorId: sector._id,
          name: sector.name,
        };
        sectorsNames.push(newSector);
      });
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Sectors found successfully",
        sectorsNames
      );
      return res.status(200).json(response);
    } else if (applyFilter.coordinatorName) {
      if (applyFilter.coordinatorName) {
        query = {
          "coordinators.coordinatorName": {
            $regex: applyFilter.coordinatorName,
            $options: "i",
          },
        };
      } else {
        query = {};
      }
      const sectors = await Sector.find(query).select("coordinators").exec();
      if (!sectors) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No Coordinators found`,
          `Please try again later.`
        );
        return res.status(400).json(response);
      }
      //return only coordinators.coordinatorName coordinators array
      let coordinators = [];
      sectors.forEach((sector) => {
        sector.coordinators.forEach((coordinator) => {
          coordinators.push(coordinator.coordinatorName);
        });
      });

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Coordinators found successfully",
        coordinators
      );
      return res.status(200).json(response);
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invalid filter. Please provide a valid filter.`
      );
      return res.status(400).json(response);
    }
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});

//Pre-filled with the name of the sector that belongs to the establishment(s)
/**
 * @swagger
 * /api/v1/sector/baseOnEstablishments:
 *   get:
 *     summary: Get sectors based on establishment IDs
 *     tags: [Sectors]
 *     parameters:
 *       - in: query
 *         name: establishmentIds
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         required: true
 *         description: An array of establishment IDs
 *     responses:
 *       '200':
 *         description: Successfully retrieved sectors based on establishment IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: The name of the sector
 *       '400':
 *         description: Bad request or no sectors found based on the provided establishment IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/sector/baseOnEstablishments", async (req, res) => {
  try {
    const establishmentIds = JSON.parse(req.query.establishmentIds);
    if (!establishmentIds) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invalid establishmentIds. Please provide a valid establishmentIds.`
      );
      return res.status(400).json(response);
    }

    const establishmentsList = await EducationAnnuaireModel.find({
      _id: { $in: establishmentIds },
    }).select("generalInformation.sector.sectorId");

    if (!establishmentsList) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No sectors found based on the provided establishmentIds.`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    }
    let sectorIds = [];
    establishmentsList.forEach((establishment) => {
      sectorIds.push(establishment.generalInformation.sector.sectorId);
    });
    const sectors = await SectorsModel.find({
      _id: { $in: sectorIds },
    }).select("name");

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Sectors found successfully",
      sectors
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});
// Get sectors based on department and program IDs
/**
 * @swagger
 * /api/v1/sector/basedOnDepartments:
 *   get:
 *     summary: Get sectors based on department and program IDs
 *     tags: [Sectors]
 *     parameters:
 *       - in: query
 *         name: departmentId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the department
 *       - in: query
 *         name: programId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the program
 *     responses:
 *       '200':
 *         description: Successfully retrieved sectors based on department and program IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/Sector'
 *       '404':
 *         description: No sectors found based on the provided department and program IDs
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/sector/basedOnDepartments", async (req, res) => {
  try {
    const departmentId = req.query.departmentId;
    const programId = req.query.programId;
    if (!departmentId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invalid departmentId. Please provide a valid one.`
      );
      return res.status(400).json(response);
    }
    if (!programId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invalid programId. Please provide a valid one.`
      );
      return res.status(400).json(response);
    }
    const sectorsList = await SectorsModel.find({
      "departments.departmentId": departmentId,
      "program.programId": programId,
    });

    if (!sectorsList || sectorsList.length === 0) {
      // Check if sectorsList is empty
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No sectors found based on the provided departmentId.`,
        `Please try again later.`
      );
      return res.status(404).json(response); // Changed to 404 Not Found
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Sectors found successfully",
      sectorsList
    );
    return res.status(200).json(response);
  } catch (error) {
    console.error("Error fetching sectors based on departments:", error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    logger
      .newLog(logger.getLoggerTypeConstants().admin)
      .error(response + error);
    return res.status(500).json(response);
  }
});

//export router
module.exports = router;
