const express = require("express");

const router = express.Router();

const apiLoggerS = require("../services/logger/LoggerService.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

//import userHelper.js from path src/controllers/user/user.Helper.js
const userHelper = require("../controllers/user/User.Helper.js");

const tutorHelper = require("../controllers/tutor/tutor.helper.js");

const parentHelper = require("../controllers/parent/parent.helper.js");

const studentHelper = require("../controllers/students/student.helpers.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const userAdministrationHelper = require("../controllers/user/user.administration.helper.js");

const { update } = require("lodash");

require("dotenv").config();

const establishmentHelper = require("../controllers/establishments/establishments.helper.js");

const sessionStudentHelper = require("../controllers/sessions/sessions.student.helper.js");

const levelsModel = require("../models/Levels.Model.js");
const tutorJob = require("../services/aaJobs/tutor.job.js");
const sendGridConstants = require("../utils/constants/sendgrid.constants.js");

const mongoose = require("mongoose");

const admin = require("firebase-admin");
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const Users = require("../models/User.Models.js");
const matchingHelper = require("../controllers/matching/matching.helper.js")
const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");

const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
  ROLE_TUTOR,
  ROLE_PARENT,
  ROLE_STUDENT,
  VSC,
} = require("../utils/constants/userRolesConstants.js");
const userRolesConstants = require("../utils/constants/userRolesConstants.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");

router.use((req, res, next) => {
  verifyUserRole.checkUserRoleAndAccessLevel(
    req,
    res,
    next,
    mongodbModelConstants.modelName.USER_ADMINISTRATION
  );
});

router.get(
    "/student/is/stats",
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
    //
    async (req, res) => {
      try {
        const userRole = req.userRole;
        // const filter = req.query.filter;
        const userId = req.userId;
        const studentStats = await studentHelper.getStudentStatistics({
          userId,
          userRole,
        });
  
        if (!studentStats.status) {
          return res.status(400).json(
            apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_ERROR,
              studentStats.message
            )
          );
        }
  
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "Les statistiques des eleves ont été récupérées avec succès",
          studentStats.data
        );
        return res.status(200).json(response);
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          "Erreur lors de la récupération des statistiques des eleves",
          error
        );
        return res.status(500).json(response);
      }
    }
  );
  
  
  module.exports = router;
  