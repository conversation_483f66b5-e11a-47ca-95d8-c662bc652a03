//import tutorPreferences
const tutorPreferences = require("../../models/Tutor.Preferences.Model.js");

const studentPreferences = require("../../models/Student.Preferences.Model.js");

//import studentHelper
const studentHelper = require("../../controllers/students/student.helpers.js");

const tutorHelper = require("../../controllers/tutor/tutor.helper.js");

const sessionModel = require("../../models/Sessions.Model");

const userAdminModel = require("../../models/User.Administration.Model.js");

const sessionConstants = require("../../utils/constants/sessions.constants.js");

const { userStatus } = require("../../utils/constants/student.constants");

const studentPreferenceModel = require("../../models/Student.Preferences.Model.js");

const tutorConstants = require("../../utils/constants/tutor.constants.js");

const studentConstants = require("../../utils/constants/student.constants.js");

const subjectConstants = require("../../utils/constants/subject.constants.js");
const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const {
  GENDER_FEMALE,
  GENDER_MALE,
} = require("../../utils/constants/user.const.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const { exists } = require("../../models/ApiLog.Model.js");
const LevelsModel = require("../../models/Levels.Model.js");
const { default: mongoose } = require("mongoose");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");

let onlineProg = [
  HOME_CLASSES.programId,
  ZUPDEFOOT.programId,
  CLASSSES_HOME.programId,
];
const tutorSitution = [
  tutorConstants.situationOfTutor.FOR_RENEWAL,
  tutorConstants.situationOfTutor.PAUSE,
  tutorConstants.situationOfTutor.STOP,
  tutorConstants.situationOfTutor.NO_FURTHER_ACTION,
];
exports.getMatchingStats = async () => {
  try {
    const commonTutorFilter = {
      createdAt: { $exists: true },
      availability: {
        $exists: true,
        $not: { $size: 0 },
      },
      situation: { $nin: tutorSitution },
      "assignment.level": {
        $exists: true,
        $not: { $size: 0 },
      },
      gender: {
        $ne: null,
      },
      "program.programId": {
        $exists: true,
        //the tutorat solidaire program must not be included in the matching stats
        $ne: "az-fac-5f87232",
      },
      subjectToTeach: {
        $exists: true,
      },
      status: "active",
      $or: [
        {
          $and: [
            { "contactDetails.email": { $ne: null } },
            { "contactDetails.email": { $exists: true, $ne: "" } },
          ],
        },
        {
          $and: [
            { "contactDetails.phoneNumber": { $ne: null } },
            { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
          ],
        },
      ],
      // $expr: {
      //   $gte: [
      //     "$assignment.numberOfStudentsToSupport",
      //     { $size: "$matchedStudents" },
      //   ],
      // },
    };

    const commonStudentFilter = {
      createdAt: { $exists: true },
      availability: {
        $exists: true,
        $not: { $size: 0 },
      },
      situation: { $ne: studentConstants.studentStatus.STOP },
      "assignment.level": {
        $ne: null,
      },
      gender: {
        $ne: null,
      },
      "program.programId": {
        $in: onlineProg,
      },
      userStatus: "active",
      $or: [
        {
          $and: [
            { "contactDetails.email": { $ne: null } },
            { "contactDetails.email": { $exists: true, $ne: "" } },
          ],
        },
        {
          $and: [
            { "contactDetails.phoneNumber": { $ne: null } },
            { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
          ],
        },
      ],
      // "assignment.establishments": { $exists: true, $not: { $size: 0 } },
    };

    const totalMatchedTutors = await tutorPreferences.countDocuments({
      matchedStudents: { $exists: true },
      $expr: {
        $and: [
          {
            $eq: [
              { $size: "$matchedStudents" },
              "$assignment.numberOfStudentsToSupport",
            ],
          },
          {
            $ne: ["$matching", []],
          },
        ],
      },
      ...commonTutorFilter,
    });
    const totalUnMatchedTutors = await tutorPreferences.countDocuments({
      matchedStudents: { $exists: true, $size: 0 }, // Check if matchedStudents array exists and is empty
      // matching: { $exists: true, $size: 0 }, // Check if matching array exists and is empty
      ...commonTutorFilter,
    });

    const partiallyMatchedTutors = await tutorPreferences.countDocuments({
      matchedStudents: { $exists: true, $not: { $size: 0 } },
      $expr: {
        $and: [
          { $gt: [{ $size: "$matchedStudents" }, 0] },
          {
            $lt: [
              { $size: "$matchedStudents" },
              "$assignment.numberOfStudentsToSupport",
            ],
          },
        ],
      },
      ...commonTutorFilter,
    });

    const totalMatchedStudents = await studentPreferenceModel.countDocuments({
      matchedTutors: { $exists: true, $not: { $size: 0 } },
      ...commonStudentFilter,
    });

    const totalUnMatchedStudents = await studentPreferenceModel.countDocuments({
      matchedTutors: [],
      ...commonStudentFilter,
    });
    const filterTotal = {
      ...commonTutorFilter,
      $expr: {
        $gte: [
          "$assignment.numberOfStudentsToSupport",
          { $size: "$matchedStudents" },
        ],
      },
    };
    const totalNumberOfStudentsToSupportAggregation =
      await tutorPreferences.aggregate([
        { $match: filterTotal },
        {
          $group: {
            _id: null,
            total: { $sum: "$assignment.numberOfStudentsToSupport" },
          },
        },
        { $project: { _id: 0, total: 1 } },
      ]);

    const totalNumberOfStudentsToSupport =
      totalNumberOfStudentsToSupportAggregation.length > 0
        ? totalNumberOfStudentsToSupportAggregation[0].total
        : 0;
    const capacity = await tutorPreferences.aggregate([
      { $match: filterTotal },
      {
        $project: {
          matchedStudentsSize: { $size: "$matchedStudents" },
        },
      },
      {
        $group: {
          _id: null,
          totalMatchedStudents: { $sum: "$matchedStudentsSize" },
        },
      },
    ]);
    remainingCapacity =
      totalNumberOfStudentsToSupport - capacity[0]?.totalMatchedStudents;

    return {
      status: true,
      message: "Success",
      data: {
        totalMatchedTutors,
        totalUnMatchedTutors,
        partiallyMatchedTutors,
        totalMatchedStudents,
        totalUnMatchedStudents,
        totalNumberOfStudentsToSupport,
        remainingCapacity,
      },
    };
  } catch (error) {
    console.error(error);
    return {
      status: false,
      message: "Error in getting matching stats",
      data: null,
    };
  }
};

//#region  TUTOR > MATCHING > STUDENT

//get matching dashboard base on TUTOR and filter
exports.matchDashboardTutor = async (
  filter,
  sortByObject,
  page,
  pageSize,
  userRoles,
  connectedUser
) => {
  try {
    pageSize = buildPageSize(pageSize);
    const userRole = userRoles;
    page = buildPage(page);
    let sortBy = buildSortBy(sortByObject);
    const {
      status,
      tutor,
      program,
      gender,
      levels,
      partner,
      // affectability,
      leftNumberOfStudentsToSupport,
    } = JSON.parse(filter);
    const tutorsWithMatchedStudentsQuery = {
      availability: {
        $exists: true,
        $not: { $size: 0 },
      },
      // affectability: {
      //   $exists: true,
      //   $eq: false,
      // },
      "program.programId": {
        $eq: program,
        $exists: true,
      },
      status: "active",
      situation: { $nin: tutorSitution },
      "assignment.level": {
        $exists: true,
        $not: { $size: 0 },
      },
      gender: gender
        ? {
            $eq: gender,
          }
        : {
            $ne: null,
            $exists: true,
          },
      subjectToTeach: {
        $exists: true,
      },
      $or: [
        {
          $and: [
            { "contactDetails.email": { $ne: null } },
            { "contactDetails.email": { $exists: true, $ne: "" } },
          ],
        },
        {
          $and: [
            { "contactDetails.phoneNumber": { $ne: null } },
            { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
          ],
        },
      ],
      $expr: {
        $gt: [
          "$assignment.numberOfStudentsToSupport",
          { $size: "$matchedStudents" },
        ],
      },
    };
    if (
      [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(
        userRole
      ) &&
      onlineProg.includes(program)
    ) {
      tutorsWithMatchedStudentsQuery["program.programId"] = { $in: onlineProg };
    }
    if (tutor) {
      // Split the tutor string by spaces to separate first name and last name
      const tutorNames = tutor.split(" ").filter((name) => name.trim() !== "");

      if (tutorNames.length === 1) {
        // If there's only one name, search for it in both firstName and lastName fields
        tutorsWithMatchedStudentsQuery["$or"] = [
          {
            "contactDetails.firstName": {
              $regex: tutorNames[0],
              $options: "i",
            },
          },
          {
            "contactDetails.lastName": { $regex: tutorNames[0], $options: "i" },
          },
        ];
      } else if (tutorNames.length >= 2) {
        // If there are at least two names, assume the first is the first name and the last is the last name
        const firstName = tutorNames[0];
        const lastName = tutorNames.slice(1).join(" ");

        tutorsWithMatchedStudentsQuery["$and"] = [
          { "contactDetails.firstName": { $regex: firstName, $options: "i" } },
          { "contactDetails.lastName": { $regex: lastName, $options: "i" } },
        ];
      }
    }

    if (status === "private-session") {
      tutorsWithMatchedStudentsQuery["$expr"] = {
        $and: [
          {
            $eq: [
              { $size: "$matchedStudents" },
              "$assignment.numberOfStudentsToSupport",
            ],
          },
          {
            $ne: ["$matching", []],
          },
        ],
      };
    }
    if (status === "unmatched") {
      tutorsWithMatchedStudentsQuery["matchedStudents"] = {
        $exists: true,
        $eq: [],
      };
      // tutorsWithMatchedStudentsQuery["matching"] = {
      //   $exists: true,
      //   $eq: [],
      // };
    }
    if (status === "partial") {
      // Check if the number of matched students is greater than 0 and less than numberOfStudentsToSupport
      tutorsWithMatchedStudentsQuery["$expr"] = {
        $and: [
          { $gt: [{ $size: "$matchedStudents" }, 0] },
          {
            $lt: [
              { $size: "$matchedStudents" },
              "$assignment.numberOfStudentsToSupport",
            ],
          },
        ],
      };
    }

    if (levels) {
      const normalizedLevels = addAccentsToLevels(levels);
      tutorsWithMatchedStudentsQuery["assignment.level.levelName"] = {
        $exists: true,
        $in: normalizedLevels,
      };
    }
    if (leftNumberOfStudentsToSupport) {
      tutorsWithMatchedStudentsQuery["assignment.numberOfStudentsToSupport"] = {
        $eq: leftNumberOfStudentsToSupport[0],
      };
    }
    if (partner) {
      tutorsWithMatchedStudentsQuery["partner"] = {
        $in: partner,
      };
    }
    //   if(program) {
    //     tutorsWithMatchedStudentsQuery["program.programId"]= {$eq: program}
    // }
  
    // const tuuuuu = await this.initialteAffectabilities(tutorsWithMatchedStudentsQuery)
    // console.log("tutorsWithMatchedStudentsQuery", sortBy) 
    const tutorsWithMatchedStudents = await tutorPreferences
      .find(tutorsWithMatchedStudentsQuery)
      .sort(sortBy)
      .skip(pageSize * (page - 1))
      .limit(pageSize)
      .exec();
    const totalNumberOfTutors = await tutorPreferences
      .find(tutorsWithMatchedStudentsQuery)
      .countDocuments();
    const listOfTutorsData = await Promise.all(
      tutorsWithMatchedStudents.map(async (tutor) => {
        let {
          userId: tutorUserId,
          assignment,
          contactDetails,
          gender,
          matchedStudents,
          availability,
          status: tutorStatus,
          // matching,
          subjectToTeach,
        } = tutor;

        const tutorFullName = `${contactDetails.firstName} ${contactDetails.lastName}`;
        // const matchingStatus =
        //   matching && matching[0] ? [matching[0].status] : [];

        // Prepare student names from the preloaded data
        // const studentNames = matching
        //   .filter((item) => item.studentIds.length > 0)
        //   .map((item) =>
        //     item.studentIds
        //       .map((studentId) =>
        //         allStudents[studentId] ? allStudents[studentId].fullName : null
        //       )
        //       .filter(Boolean)
        //   );
        const updatedAvailability = await this.checkTutorStudentAvailability(
          availability,
          tutor.userId,
          true
        );
        availability = updatedAvailability;
        const affectable = await this.checkAffectability(tutor,availability, true, true);
        const orderedAvailability = availability.sort(
          (a, b) => a.dayOfTheWeek - b.dayOfTheWeek || a.startTime - b.startTime
        );
        let levelsList = assignment.level;

        // Vérifiez si `levelsList` contient des données
        if (levelsList?.length > 0) {
          // Parcourez chaque élément pour enrichir les données avec des informations depuis la base de données
          for (let i = 0; i < levelsList.length; i++) {
            const levelId = levelsList[i].levelId;
            if (!levelId) {
              continue; // Ignorez les niveaux sans ID
            }

            // Recherchez les données de niveau dans la base de données
            const level = await LevelsModel.findOne({
              _id: mongoose.Types.ObjectId(levelId),
            });

            // Construisez un nouvel objet pour le niveau
            levelsList[i] = {
              levelId: levelId,
              levelName: level?.levelName || "", // Assurez-vous que `levelName` est défini
            };
          }
        }

        // Only include active tutors
        if (tutorStatus === userStatus.ACTIVE) {
          // Prepare the tutor's matching dashboard data
          const matchingDashboardTutor = {
            tutorId: tutorUserId,
            tutorFullName,
            gender,
            isPartner: tutor.isPartner,
            partner: tutor.partner,
            levels: levelsList,
            availability: orderedAvailability,
            program: tutor.program.programId,
            sectors: assignment.sectors || [],
            department: assignment?.department?.departmentId
              ? assignment.department
              : {},
            establishment: assignment?.establishments[0]?.establishmentId
              ? tutor.assignment.establishments
              : {},
              // affectability : tutor.affectability,
              affectability : affectable,
            // matchingStatus,
            // studentName: studentNames.flat(),
            subjects: subjectToTeach,
            leftNumberOfStudentsToSupport:
              assignment.numberOfStudentsToSupport || 0,
            matchedStudents,
            typeOfUser: "tutor",
          };
          return matchingDashboardTutor;
        }
        return null;
      })
    );
    return {
      status: true,
      message: "Success",
      data: listOfTutorsData,
      totalNumberOfTutors,
    };
  } catch (error) {
    console.error(error);
    return { status: false, message: "Error in getting tutors", data: null };
  }
};
exports.checkAffectability = async (user , availability, tutor, dash ) => {
  let affectable = false
  const availableSlots = dash ? availability.filter(slot => !slot.taken) : availability;
    
  if (dash && availableSlots.length === 0) {
    return false;
  }
  for (const slot of availableSlots) {
    // if(slot.dayOfTheWeek == 0 && slot.startTime == "16:00") {
   
    const levelId =  JSON.stringify( user?.assignment?.level.map((l)=>l.levelId))
    const unmatchedStudentsTutors  = tutor ? await this.getUnmatchedStudentsByTutorId(
      user.userId,
      user.gender,
      user.assignment?.sectorId,
      levelId,
      JSON.stringify(slot),
      user.subjectToTeach,
      "",
      user.program.programId,
      user.assignment?.department.departmentId,
      false,
      user.userId
    ) : await this.getUnmatchedTutorsByStudentId( 
      user.userId,
      user.gender,
      user.assignment?.sectorId,
      levelId,
      JSON.stringify(slot),
      user.subjectToTeach,
      "",
      user.program?.programId,
      user.assignment?.department.departmentId,
      // false,
      // false
    );
    console.log("unmatchedStudentsTutors", unmatchedStudentsTutors)
    affectable = unmatchedStudentsTutors.status && unmatchedStudentsTutors.data.length > 0 ? true : false
    slot.affectable = affectable
    if (affectable && dash) break;
  // }     
}
  return dash ? affectable :availability ;
};
//get all students base on TutorUserId who are matched with tutor
exports.getMatchedStudentsByTutorId = async (
  tutorUserId,
  tutorAvailability
) => {
  try {
    //parse tutorAvailability to jsonObject
    let matchingAvailableObject;
    if (tutorAvailability) {
      matchingAvailableObject = JSON.parse(tutorAvailability);

      //put tutorAvailability to array
      matchingAvailableObject = [matchingAvailableObject];
    }

    //Get tutorDocument base on tutorUserId
    const tutorDocument = await tutorPreferences
      .findOne({ userId: tutorUserId })
      .exec();
    if (!tutorDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    //if tutorAvailability is not empty or null  or undefined then get available matching object from tutorDocument
    if (!matchingAvailableObject) {
      matchingAvailableObject = tutorDocument.availability;
    }

    const listOfMatching = tutorDocument.matching;
    let listOfStudents = [];
    for (let i = 0; i < listOfMatching.length; i++) {
      let itemMatching = listOfMatching[i];
      let studentIds = itemMatching.studentIds;
      const sessionId = itemMatching.sessionId;

      const sessionDocument = await sessionModel
        .findOne({ sessionId: sessionId })
        .exec();
      if (!sessionDocument) {
        continue;
      }
      const sessionDate = sessionDocument.sessionDate;
      if (!sessionDate) {
        continue;
      }
      const sessionStartHour = sessionDate.startHour;
      const sessionDayOfWeek = sessionDate.dayOfTheWeek;
      for (a = 0; a < matchingAvailableObject.length; a++) {
        let itemMatchingAvailableObject = matchingAvailableObject[a];
        if (!itemMatchingAvailableObject) {
          continue;
        }
        const dayOfTheWeek = itemMatchingAvailableObject.dayOfTheWeek;
        const startTime = itemMatchingAvailableObject.startTime;

        //convert startTime to  Number
        const startTimeNumber = Number(startTime);

        //check if dayOfWeek is the same with sessionDayOfWeek and startTime is the same with sessionStartHour
        if (
          dayOfTheWeek === sessionDayOfWeek &&
          startTimeNumber === sessionStartHour
        ) {
          for (let j = 0; j < studentIds.length; j++) {
            const studentId = studentIds[j];
            const studentFullName = await studentHelper.getStudentFullName(
              studentId
            );
            if (!studentFullName.status) {
              continue;
            }
            const studentObject = {
              studentId: studentId,
              studentFullName: studentFullName.data,
              availability: itemMatchingAvailableObject,
            };
            listOfStudents.push(studentObject);
          }
        }
      }
    }

    return { status: true, message: "Success", data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting students for tutor ${tutorUserId}, error: ${error}`,
      data: null,
    };
  }
};
exports.getUnmatchedStudentsByTutorId = async (
  tutorUserId,
  gender,
  sectorId,
  levelId,
  tutorAvailability,
  subject,
  studentName,
  program,
  departmentId,
  allunmatchedStudents,
  userIdConnectedUser
) => {
  try {
    subject = subjectConstants.getSubjectId(subject);
    // sectorId = JSON.parse(sectorId);
    const userId = userIdConnectedUser;
    const connectedUser = await userAdminModel.find({ userId: userId });
    const departmentId = connectedUser.flatMap((user) =>
      user.administrationPreferences.department.map(
        (department) => department.departmentId
      )
    );
    levelId = allunmatchedStudents ? levelId : JSON.parse(levelId);
    console.log("tutorUserId", tutorUserId)
    const tutorDocument = await tutorPreferences
      .findOne({ userId: tutorUserId })
      .exec();
    if (!tutorDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    let tutorAvailabilityObject = tutorDocument.availability; // Default to tutor's availability
    if (!allunmatchedStudents) {
      tutorAvailabilityObject = JSON.parse(tutorAvailability);
    } else {
      tutorAvailabilityObjectCond = tutorAvailabilityObject.map(
        (availability) => ({
          dayOfTheWeek: availability.dayOfTheWeek,
          startTime: availability.startTime,
        })
      );
    }
    const query = {
      userStatus: userStatus.ACTIVE,
      situation: { $ne: studentConstants.studentStatus.STOP },
      // matching: { $size: 0 }, // Filtering out matched students
      matchedTutors: { $size: 0 },
      createdAt: { $exists: true },
      "assignment.level.levelId": { $in: levelId, $exists: true },
      // "assignment.establishments": { $exists: true, $not: { $size: 0 } },
      "contactDetails.firstName": {
        $regex: studentName ? studentName : ".*",
        $options: "i",
      },

      gender:
        gender === GENDER_FEMALE
          ? { $in: [GENDER_FEMALE, GENDER_MALE], $exists: true }
          : GENDER_MALE,

      $or: [
        {
          $and: [
            { "contactDetails.email": { $ne: null } },
            { "contactDetails.email": { $exists: true, $ne: "" } },
          ],
        },
        {
          $and: [
            { "contactDetails.phoneNumber": { $ne: null } },
            { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
          ],
        },
      ],
    };
    if (!allunmatchedStudents) {
      query.availability = {
        $elemMatch: {
          dayOfTheWeek: tutorAvailabilityObject.dayOfTheWeek,
          startTime: tutorAvailabilityObject.startTime,
        },
      };
    } else {
      query.availability = {
        $elemMatch: {
          $or: tutorAvailabilityObjectCond.map((availability) => ({
            dayOfTheWeek: availability.dayOfTheWeek,
            startTime: availability.startTime,
          })),
        },
      };
    }

    if (subject && subject.length > 0) {
      query["assignment.subjectToStudy"] = {
        $in: subject,
      };
    }

    // if (program === DEVOIRS_FAITS.programId) {
    //   // query["assignment.sectors.sectorId"] = { $in: sectorId };
    //   // query["assignment.department.departmentId"] = { $eq: departmentId };
    // } else 
    console.log("HEEEEE1111",program)
    if (program && onlineProg.includes(program)) {
      console.log("HEEEEE",program)
      if (departmentId.length > 0) {
        query["assignment.department.departmentId"] = { $in: departmentId };
      }
      query["program.programId"] = { $eq: program };
    }
    console.log("querrrrr", query)
    const studentDocumentLists = await studentPreferenceModel
      .find(query)
      //adding the sort by createdAt to get the oldest created student
      .sort({
        // createdAt: 1,
        "program.priority": 1,
      })
      .exec();
    if (!studentDocumentLists || studentDocumentLists.length === 0) {
      return { status: false, message: "No student found", data: null };
    }
    const listOfStudents = studentDocumentLists.map((student) => {
      const studentFullName =
        student.contactDetails.firstName + " " + student.contactDetails.lastName;
      let availability = student.availability.sort((a, b) => {
        return a.dayOfTheWeek - b.dayOfTheWeek || a.startTime - b.startTime;
      });
      return {
        studentId: student.userId,
        studentFullName: studentFullName ? studentFullName : "",
        availability,
        programName: student.program.programName || "",
      };
    });

    return { status: true, message: "Success", data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting students for tutor ${tutorUserId}, error: ${error}`,
      data: null,
    };
  }
};

//#endregion

//#region STUDENT > MATCHING - TUTOR

exports.matchDashboardStudent = async (
  filter,
  sortByObject,
  page,
  pageSize,
  userRolesUser,
  userIdUser
) => {
  try {
    pageSize = buildPageSize(pageSize);
    page = buildPage(page);
    // sortByObject = '{"matchedTutors":1}';
    let sortBy = buildSortBy(sortByObject);
    const userRole = userRolesUser;
    const userId = userIdUser;
    const { status, student, program, gender, levels, partner } =
      JSON.parse(filter);
    const tutorsWithMatchedStudentsQuery = {
      $expr: {
        $cond: {
          // Si le `status` est null, vérifier si `matchedTutors` existe
          if: { $eq: ["$status", null] },
          then: { $ne: [{ $type: "$matchedTutors" }, "missing"] }, // Vérifie si `matchedTutors` existe (pas de type "missing")

          else: {
            $cond: {
              // Si `status` est "private-session", vérifier que `matchedTutors` n'est pas vide
              if: { $eq: [status, "private-session"] },
              then: { $ne: ["$matchedTutors", []] },

              else: {
                $cond: {
                  // Si `status` est "unmatched", vérifier que `matchedTutors` est vide
                  if: { $eq: [status, "unmatched"] },
                  then: { $eq: ["$matchedTutors", []] },

                  else: true, // Cas par défaut, ne fait pas de contrôle sur `matchedTutors`
                },
              },
            },
          },
        },
      },
      userStatus: userStatus.ACTIVE,
      situation: { $ne: studentConstants.studentStatus.STOP },
      "program.programId": {
        $eq: program,
        $exists: true,
      },
      createdAt: { $exists: true },

      availability: {
        $exists: true,
        $not: { $size: 0 },
      },
      "assignment.level": {
        $ne: null,
      },
      gender: {
        $ne: null,
      },
    };
    tutorsWithMatchedStudentsQuery["$and"] = [
      {
        $or: [
          {
            $and: [
              { "contactDetails.email": { $ne: null } },
              { "contactDetails.email": { $exists: true, $ne: "" } },
            ],
          },
          {
            $and: [
              { "contactDetails.phoneNumber": { $ne: null } },
              { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
            ],
          },
        ],
      },
      {
        $or: [
          { "contactDetails.firstName": { $regex: student, $options: "i" } },
          { "contactDetails.lastName": { $regex: student, $options: "i" } },
        ],
      },
    ];

    if (gender && gender.length) {
      tutorsWithMatchedStudentsQuery["gender"] = {
        $in: gender,
      };
    }
    if (partner && partner.length) {
      tutorsWithMatchedStudentsQuery["partner"] = {
        $in: partner,
      };
    }
    if (levels && levels.length) {
      tutorsWithMatchedStudentsQuery["assignment.level.levelName"] = {
        $in: levels,
      };
    }
    if (
      [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(
        userRole
      ) &&
      onlineProg.includes(program)
    ) {
      let connectedUser = [];
      if (userId) {
        connectedUser = await userAdminModel.find({ userId: userId });
        connectedUser = connectedUser.flatMap((user) =>
          user.administrationPreferences.department.map(
            (department) => department.departmentId
          )
        );
      }
      tutorsWithMatchedStudentsQuery["program.programId"] = { $eq: program };
      tutorsWithMatchedStudentsQuery["assignment.department.departmentId"] = {
        $in: connectedUser,
      };
    }
    // if (student) {
    //   // Search by either firstName or lastName
    //   tutorsWithMatchedStudentsQuery["$or"] = [
    //     { "contactDetails.firstName": { $regex: student, $options: "i" } },
    //     { "contactDetails.lastName": { $regex: student, $options: "i" } },
    //   ];
    // }

    const studentsWithMatchedTutors = await studentPreferences
      .find(tutorsWithMatchedStudentsQuery)
      .sort(sortBy)
      .skip(pageSize * (page - 1))
      .limit(pageSize)
      .exec();

    const totalNumberOfStudents = await studentPreferences
      .find(tutorsWithMatchedStudentsQuery)
      .countDocuments();
            const processedStudents = await Promise.all(
              studentsWithMatchedTutors
                .filter(student => student.userStatus === userStatus.ACTIVE)
                .map(async (student) => {
                  try {
                    const {
                      userId,
                      contactDetails: { firstName, lastName },
                      availability: originalAvailability,
                      gender,
                      partner,
                      isPartner,
                      assignment,
                      program,
                      matchedTutors,
                    } = student;
                    const availability = (await this.checkTutorStudentAvailability(
                      originalAvailability,
                      userId,
                      false
                    )).sort((a, b) => (
                      a.dayOfTheWeek - b.dayOfTheWeek || 
                      a.startTime - b.startTime
                    ));
                    const affectable = await this.checkAffectability(student, availability, false, true);
        
                    // Construction de l'objet de retour avec valeurs par défaut
                    return {
                      studentUserId: userId,
                      studentFullName: `${firstName} ${lastName}`,
                      gender,
                      partner,
                      isPartner,
                      levels: assignment.level,
                      availability,
                      program,
                      affectability: affectable,
                      department: assignment?.department?.departmentId 
                        ? assignment.department 
                        : {},
                      establishment: assignment?.establishments?.[0]?.establishmentId 
                        ? assignment.establishments 
                        : {},
                      subjectToStudy: assignment?.subjectToStudy,
                      matchedTutors,
                      typeOfUser: "student"
                    };
                  } catch (error) {
                    console.error(`Error processing student ${student.userId}:`, error);
                    return null; 
                  }
                })
            );
    return {
      status: true,
      message: "Success",
      data: processedStudents,
      totalNumberOfStudents: totalNumberOfStudents,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in getting tutors", data: null };
  }
};

//get all tutors base on studentUserId who are matched with Student
exports.getMatchedTutorsByStudentId = async (
  studentUserId,
  studentAvailability
) => {
  try {
    //parse tutorAvailability to jsonObject
    let matchingAvailableObject;

    if (studentAvailability) {
      matchingAvailableObject = JSON.parse(studentAvailability);

      //put tutorAvailability to array
      matchingAvailableObject = [matchingAvailableObject];
    }

    //Get tutorDocument base on tutorUserId
    const studentDocument = await studentPreferenceModel
      .findOne({ userId: studentUserId })
      .exec();

    if (!studentDocument) {
      return { status: false, message: "Student not found", data: null };
    }

    //if tutorAvailability is not empty or null  or undefined then get available matching object from tutorDocument
    if (!matchingAvailableObject) {
      matchingAvailableObject = studentDocument.availability;
    }

    const listOfMatching = studentDocument.matching;

    let listOfTutors = [];
    for (let i = 0; i < listOfMatching.length; i++) {
      let itemMatching = listOfMatching[i];
      let tutorId = itemMatching.tutorId;
      const sessionId = itemMatching.sessionId;

      const sessionDocument = await sessionModel
        .findOne({ sessionId: sessionId })
        .exec();
      if (!sessionDocument) {
        continue;
      }
      const sessionDate = sessionDocument.sessionDate;
      if (!sessionDate) {
        continue;
      }
      const sessionStartHour = sessionDate.startHour;
      const sessionDayOfWeek = sessionDate.dayOfTheWeek;
      for (a = 0; a < matchingAvailableObject.length; a++) {
        let itemMatchingAvailableObject = matchingAvailableObject[a];
        if (!itemMatchingAvailableObject) {
          continue;
        }
        const dayOfTheWeek = itemMatchingAvailableObject.dayOfTheWeek;
        const startTime = itemMatchingAvailableObject.startTime;

        //convert startTime to  Number
        const startTimeNumber = Number(startTime);

        //check if dayOfWeek is the same with sessionDayOfWeek and startTime is the same with sessionStartHour
        if (
          dayOfTheWeek === sessionDayOfWeek &&
          startTimeNumber === sessionStartHour
        ) {
          let tutorFullName = await tutorHelper.tutorFullName(tutorId);
          tutorFullName = tutorFullName.data ? tutorFullName.data : "";

          const studentObject = {
            tutorUserId: tutorId,
            tutorFullName: tutorFullName,
            availability: itemMatchingAvailableObject,
            matching: itemMatching,
          };
          listOfTutors.push(studentObject);
        }
      }
    }

    return { status: true, message: "Success", data: listOfTutors };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting tutors ${error}`,
      data: null,
    };
  }
};

//get list of students who are unmatched with tutor but can be matched with tutor base on preference
exports.getUnmatchedTutorsByStudentId = async (
  studentUserId,
  gender,
  sectorId,
  levelId,
  studentAvailability,
  subject,
  tutorName,
  program,
  departmentId,
  // is_suzali,
  // unmatched
) => {
  try {
    subject = subjectConstants.getSubjectIdForUnmachedTutors(subject);
    // sectorId = JSON.parse(sectorId);
    levelId =  JSON.parse(levelId);

    // Retrieve the student's preferences document
    const studentDocument = await studentPreferenceModel
      .findOne({ userId: studentUserId })
      .exec();
    if (!studentDocument) {
      return { status: false, message: "Student not found", data: null };
    }

    // let studentAvailabilityObject = studentDocument.availability;

     let studentAvailabilityObject = JSON.parse(studentAvailability);

    // Build the availability condition based on student availability
    // const availabilityConditions = studentDocument.availability.map(
    //   (availability) => ({
    //     dayOfTheWeek: availability.dayOfTheWeek,
    //     startTime: availability.startTime,
    //     endTime: availability.endTime,
    //   })
    // );

    // Build the main query to find tutors
    const query = {
      status: userStatus.ACTIVE,
      situation: { $nin: tutorSitution },
      "assignment.level.levelId": { $in: levelId },
      gender:
        gender === GENDER_FEMALE
          ? GENDER_FEMALE
          : { $in: [GENDER_FEMALE, GENDER_MALE] },
      "contactDetails.firstName": {
        $regex: tutorName ? tutorName : ".*",
        $options: "i",
      },
      availability : {
        $elemMatch: {
          dayOfTheWeek: studentAvailabilityObject.dayOfTheWeek,
          startTime: studentAvailabilityObject.startTime,
        },
      },
      subjectToTeach: { $exists: true },
      "assignment.level": { $exists: true, $not: { $size: 0 } },
      $or: [
        {
          $and: [
            { "contactDetails.email": { $ne: null } },
            { "contactDetails.email": { $exists: true, $ne: "" } },
          ],
        },
        {
          $and: [
            { "contactDetails.phoneNumber": { $ne: null } },
            { "contactDetails.phoneNumber": { $exists: true, $ne: "" } },
          ],
        },
      ],
      $expr: {
        $lt: [
          { $size: "$matchedStudents" },
          "$assignment.numberOfStudentsToSupport",
        ],
      },
    };

    // Add subject condition if provided
    if (subject && subject.length > 0) {
      query.subjectToTeach = { $in: subject };
    }

    // Add program-specific conditions if necessary
    if (onlineProg.includes(program)) {
      query["program.programId"] = { $in: onlineProg };
      // if (departmentId) {
      //   query["assignment.department.departmentId"] = { $eq: departmentId };
      // }
    }
    // if (is_suzali && unmatched) {
    //   query.availability = {
    //     $elemMatch: {
    //       $or: availabilityConditions.map((cond) => ({
    //         dayOfTheWeek: cond.dayOfTheWeek,
    //         startTime: cond.startTime,
    //         endTime: cond.endTime,
    //       })),
    //     },
    //   };
    // } else {
    // }
    // if (is_suzali && !unmatched) {
    //   query.isPartner = true;
    //   query.partner = "Suzali";
    // }
    const tutorDocumentLists = await tutorPreferences.find(query).exec();
    if (!tutorDocumentLists || tutorDocumentLists.length === 0) {
      return { status: false, message: "No tutors found", data: null };
    }
    const filteredTutors = await this.checkIfTutorHaveSameTimeSession(
      tutorDocumentLists,
      studentAvailabilityObject
    );
    const listOfTutors = await Promise.all(
      filteredTutors.map(async (tutor) => {
        const tutorFullName = `${tutor.contactDetails.firstName} ${tutor.contactDetails.lastName}`;
        let availability = tutor.availability.sort((a, b) => {
          return (
            a.dayOfTheWeek - b.dayOfTheWeek ||
            a.startTime.localeCompare(b.startTime)
          );
        });
        // const sessionTutor = await getSessionByTutorAvailability(
        //   tutor.userId,
        //   studentAvailabilityObject.startTime,
        //   studentAvailabilityObject.endTime,
        //   studentAvailabilityObject.dayOfTheWeek 
        // );
        return {
          tutorUserId: tutor.userId,
          tutorFullName: tutorFullName || "",
          availability,
          programName: tutor.program?.programName || "",
          // sessions: sessionTutor || [],
        };
      })
    );

    return { status: true, message: "Success", data: listOfTutors };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting tutors for student ${studentUserId}, error: ${error}`,
      data: null,
    };
  }
};
// async function getSessionByTutorAvailability(
//   tutorId,
//   startHour,
//   endHour,
//   dayOfTheWeek
// ) {
//   const today = new Date();

//   // Rechercher des sessions qui se chevauchent avec la nouvelle session
//   const query = {
//     status: { $nin: sessionConstants.excludedStatuses },
//     "tutors.userId": tutorId,
//     "sessionDate.startDate": { $gte: today },
//     "sessionDate.startHour": {
//       $gte: startHour?.slice(0, 2),
//       $lte: endHour?.slice(0, 2),
//     },
//     "sessionDate.endHour": {
//       $gte: startHour?.slice(0, 2),
//       $lte: endHour?.slice(0, 2),
//     },
//     "sessionDate.dayOfTheWeek": dayOfTheWeek,
//   };

//   const session = await sessionModel
//     .find(query)
//     .select("tutors students sessionDate");
//   // console.log("Sessions found:", session);

//   return session;
// }

//#endregion

// const checkAvailability = async (tutorAvailability, studentAvailability) => {
//   //if tutorAvailability or studentAvailability is null, return false
//   if (!tutorAvailability || !studentAvailability) {
//     return false;
//   }
//   for (const tutorSlot in tutorAvailability) {
//     const tutorDay = tutorSlot.dayOfTheWeek;
//     const tutorStartHour = parseInt(tutorSlot.startTime);
//     const tutorEndHour = parseInt(tutorSlot.endTime);

//     for (const studentSlot of studentAvailability) {
//       const studentDay = studentSlot.dayOfTheWeek;
//       const studentStartHour = parseInt(studentSlot.startTime);
//       const studentEndHour = parseInt(studentSlot.endTime);

//       if (
//         tutorDay === studentDay &&
//         tutorStartHour <= studentEndHour &&
//         tutorEndHour >= studentStartHour
//       ) {
//         console.log("The tutor is available");
//         return true; // found a matching slot, return true
//       } else {
//         console.log("The tutor is not available");
//         return false;
//       }
//     }
//   }

//   return false; // no matching slot found, return false
// };

function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null) {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null) {
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }

  return page;
}

function buildFilterPerTutor(filter) {
  let applyFilter = {};

  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (key == "establishment") {
      //update email key to contactDetails.email
      applyFilter["assignment.establishments"] = {
        $elemMatch: {
          establishmentId: value,
        },
      };
      delete applyFilter.establishment;
    } else if (key == "program") {
      const programId = value;
      applyFilter["program.programId"] = { $eq: programId };
      delete applyFilter.program;
    } else if (key == "status") {
      let status = value;
      if (
        status == sessionConstants.sessionsStatusInFilter.UNMATCHED.statusValue
      ) {
        delete applyFilter[key];
        applyFilter["$or"] =
          sessionConstants.sessionsStatusInFilter.UNMATCHED.statusCondition;
        //   applyFilter["$or"] = sessionConstants.sessionsStatusInFilter.UNMATCHED.statusCondition;
      } else if (
        status ==
        sessionConstants.sessionsStatusInFilter.MATCHING_CONFIRMED.statusValue
      ) {
        delete applyFilter[key];
        applyFilter["$or"] =
          sessionConstants.sessionsStatusInFilter.MATCHING_CONFIRMED.statusCondition;
      } else {
        delete applyFilter[key];
        applyFilter["$and"] =
          sessionConstants.sessionsStatusInFilter.ALL_OTHERS.statusCondition;
      }
    } else if (key == "tutor") {
      applyFilter["contactDetails.firstName"] = {
        $regex: value,
        $options: "i",
      };
      delete applyFilter.tutor;
    }
  }

  //add another filter to get only tutors who status is not       TUTOR_DEACTIVATED: "deactivated", TUTOR_ON_HOLD: "on-hold",
  applyFilter["status"] = {
    $nin: [
      tutorConstants.statusOfTutors.TUTOR_DEACTIVATED,
      tutorConstants.statusOfTutors.TUTOR_ON_HOLD,
    ],
  };

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (typeof value == "string") {
      let regex = new RegExp(value, "i");
      applyFilter[key] = regex;
    }
  }
  return { status: true, data: applyFilter };
}

function buildFilter(filter) {
  let applyFilter = {};

  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (key == "establishment") {
      //update email key to contactDetails.email
      applyFilter["assignment.establishments"] = {
        $elemMatch: {
          establishmentId: value,
        },
      };
      delete applyFilter.establishment;
    } else if (key == "program") {
      const programId = value;
      applyFilter["program.programId"] = { $eq: programId };
      delete applyFilter.program;
    } else if (key == "status") {
      let status = value;
      if (
        status == sessionConstants.sessionsStatusInFilter.UNMATCHED.statusValue
      ) {
        delete applyFilter[key];
        applyFilter["$or"] =
          sessionConstants.sessionsStatusInFilter.UNMATCHED.statusCondition;
        //   applyFilter["$or"] = sessionConstants.sessionsStatusInFilter.UNMATCHED.statusCondition;
      } else if (
        status ==
        sessionConstants.sessionsStatusInFilter.MATCHING_CONFIRMED.statusValue
      ) {
        delete applyFilter[key];
        applyFilter["$or"] =
          sessionConstants.sessionsStatusInFilter.MATCHING_CONFIRMED.statusCondition;
      } else {
        delete applyFilter[key];
        applyFilter["$and"] =
          sessionConstants.sessionsStatusInFilter.ALL_OTHERS.statusCondition;
      }
    } else if (key == "tutor") {
      applyFilter["contactDetails.firstName"] = {
        $regex: value,
        $options: "i",
      };
      delete applyFilter.tutor;
    }
  }

  //add another filter to get only student who status is not       TUTOR_DEACTIVATED: "deactivated", TUTOR_ON_HOLD: "on-hold",
  applyFilter["status"] = {
    $nin: [
      studentConstants.userStatus.DEACTIVATED,
      studentConstants.userStatus.ON_HOLD,
    ],
  };

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (typeof value == "string") {
      let regex = new RegExp(value, "i");
      applyFilter[key] = regex;
    }
  }
  return { status: true, data: applyFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = {};
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        sortBy["contactDetails.lastName"] = value;
      } else if (key == "firstName") {
        sortBy["contactDetails.firstName"] = value;
      } else if (key == "email") {
        sortBy["contactDetails.email"] = value;
      } else if (key == "matchedStudents") {
        sortBy["matchedStudents"] = value;
        sortBy["_id"] = value;
      } else if (key == "matchedTutors") {
        sortBy["matchedTutors"] = value;
        sortBy["_id"] = value;
      }
    }
  } else {
    sortBy = { createdAt: -1 };
  }
  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

// // This function fetches full names of students based on an array of student IDs
// async function getStudentsFullNames(studentIds) {
//   // Fetch students by IDs in bulk
//   const students = await studentPreferenceModel.find({
//     userId: { $in: studentIds },
//   });
//   // Create a mapping of student IDs to their full names
//   const studentNames = students.reduce((acc, student) => {
//     acc[student.userId.toString()] = `${student.firstName} ${student.lastName}`;
//     return acc;
//   }, {});

//   return studentNames;
// }
function addAccentsToLevels(levels) {
  const replacements = {
    "5eme": "5ème",
    "6eme": "6ème",
    "4eme": "4ème",
    "3eme": "3ème",
  };

  // Créer un tableau avec chaque élément et son équivalent avec accent
  return levels.flatMap((level) => {
    // Si le niveau est dans les remplacements, on ajoute les deux versions
    if (replacements[level]) {
      return [level, replacements[level]];
    }
    // Sinon, on ne garde que l'élément original
    return [level];
  });
}
// this helper function is used to filter the tutors that have a session at the same time as the student
exports.checkIfTutorHaveSameTimeSession = async (
  tutorsData,
  studentAvailability
) => {
  const { dayOfTheWeek, startTime, endTime } = studentAvailability;

  const availableTutors = [];
  for (const tutor of tutorsData) {
    const tutorMatchings = tutor.matching.map((match) => match.sessionId);
    const today = new Date();
    const tutorExistingSessions = await sessionModel
      .find({
        sessionId: { $in: tutorMatchings },
        status: sessionConstants.sessionsStatus.SESSIONS_0_TO_BE_SCHEDULED,
        parentSessionId: { $ne: null },
        "sessionDate.startDate": { $gte: today }, // get only future sessions
      })
      .distinct("sessionDate");
    const isTaken = tutorExistingSessions.some((session) => {
      const sessionStartHour = new Date(session.startHour).toLocaleTimeString("fr-FR", {
      timeZone: "Europe/Paris",
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      });
      const sessionEndHour = new Date(session.endHour).toLocaleTimeString("fr-FR", {
      timeZone: "Europe/Paris",
      hour12: false,
      hour: "2-digit",
      minute: "2-digit",
      });

      return (
      session.dayOfTheWeek === dayOfTheWeek &&
      ((sessionStartHour >= startTime && sessionStartHour < endTime) ||
       (sessionEndHour >= startTime && sessionEndHour < endTime))
      );
    });
    if (!isTaken) {
      availableTutors.push(tutor);
    }
  }
  return availableTutors;
};
exports.checkTutorStudentAvailability = async (availability, userID, tutor) => {
  for (let slot of availability) {
    // if (slot.dayOfTheWeek !== 2) continue;

    // if (!slot.dayOfTheWeek) continue; // Passer si dayOfTheWeek est vide
    const today = new Date();
    const dayOfTheWeek = slot.dayOfTheWeek;
    // const adjustedStartTime = slot.startTime; // Heure de début de la plage (ex. : "17:30")
    // const adjustedEndTime = slot.endTime; // Heure de fin de la plage (ex. : "18:30")

    // Requête MongoDB pour trouver les séances correspondantes
    let query = {
      status: { $nin: sessionConstants.excludedStatuses },
      parentSessionId: { $ne: null },
      "sessionDate.startDate": { $gte: today },
      "sessionDate.dayOfTheWeek": dayOfTheWeek,
    };


    query[tutor ? "tutors.userId" : "students.userId"] = userID;

    // Pipeline d'agrégation pour convertir en heure Paris et éliminer les doublons sur les heures
    let pipeline = [
      {
        $match: query, // Appliquer les filtres de la requête
      },
      {
        $addFields: {
          parisTime: {
            $dateToString: {
              format: "%H:%M:%S", // Extraire uniquement l'heure
              date: "$sessionDate.startDate",
              timezone: "Europe/Paris", // Convertir en fuseau horaire Paris
            },
          },
        },
      },
      {
        $group: {
          _id: "$parisTime", // Grouper par heure unique
          session: { $first: "$$ROOT" }, // Garder un seul document pour chaque heure
        },
      },
      {
        $replaceRoot: { newRoot: "$session" }, // Retourner le document complet
      },
    ];

    // Exécuter l'agrégation pour récupérer les séances uniques
    const sessions = await sessionModel.aggregate(pipeline);

    // Vérification et mise à jour des champs `taken` dans l'objet disponibilité
    for (let session of sessions) {
      const sessionStartTime = session.sessionDate.startDate; // Récupérer le startDate complet
      const sessionEndTime = session.sessionDate.endDate; // Récupérer le startDate complet
      const parisTime = new Date(sessionStartTime).toLocaleTimeString("fr-FR", {
        timeZone: "Europe/Paris",
        hour12: false,
        hour: "2-digit",
        minute: "2-digit",
      });
      const parisTimeEnd = new Date(sessionEndTime).toLocaleTimeString(
        "fr-FR",
        {
          timeZone: "Europe/Paris",
          hour12: false,
          hour: "2-digit",
          minute: "2-digit",
        }
      ); // Convertir en heure de Paris au format HH:mm

      // Vérifier si l'heure de la séance correspond à la plage horaire de disponibilité

      const isInRange =
        (parisTime >= slot.startTime && parisTime < slot.endTime) ||
        (parisTimeEnd > slot.startTime && parisTimeEnd < slot.endTime);
      if (isInRange) {
        slot.taken = isInRange;
        slot.fullName = tutor ? session.students[0].fullName : session.tutors[0].fullName;
        break;
      }
      // Mettre à jour le champ `taken` dans l'objet disponibilité
    }
  }
  // Retourner l'objet disponibilité mis à jour
  return availability;
};

// exports.initialteAffectabilities = async (filter) => {
//   try {
//     // Récupération optimisée des tuteurs
//     const tutorsList = await TutorPreferencesModel.find(filter)
//       .lean()  // Optimisation : retourne des objets JS simples

//     // Traitement des tuteurs en parallèle avec gestion d'erreurs
//     const tutorData = await Promise.all(
//       tutorsList.map(async (tutor) => {
//         try {
//           const affectability = await checkAffectability(tutor, tutor.availability, true);
          
//           // Mise à jour de la base de données
//           await TutorPreferencesModel.updateOne(
//             { userId: tutor.userId },
//             { affectability },
//             { new: true }  // Retourne le document mis à jour
//           );
//           console.log("tutor =>", {...tutor, affectability })
//           return { ...tutor, affectability };
//         } catch (error) {
//           console.error(`Error processing tutor ${tutor.userId}:`, error);
//           return { ...tutor, affectability: null, error: error.message };
//         }
//       })
//     );

//     return tutorData.filter(tutor => !tutor.error);
    
//   } catch (error) {
//     console.error('Error in initialteAffectabilities:', error);
//     throw error;
//   }
// };
