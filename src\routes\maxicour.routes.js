const express = require("express");
const router = express.Router();
const jwt = require("jsonwebtoken");
// const verifyApiKey = require("../middleware/apiAuth/verifyApiKey");
const maxicoursEndPointsByClass = require("../utils/constants/maxicoursConstants");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");
const MaxiCoursConnexionsModel = require("../models/MaxiCoursConnexions.Model.js");

//generate maxicours token
/**
 * @swagger
 * /api/v1/maxicours:
 *   post:
 *     summary: Generate Maxicours Token
 *     description: Generate a token for accessing Maxicours platform based on the provided class key.
 *     tags: [Maxicours]
 *     parameters:
 *       - in: query
 *         name: class
 *         schema:
 *           type: string
 *         required: true
 *         description: The class key for which the Maxicours token is generated.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             description: Payload containing user data for token generation.
 *     responses:
 *       200:
 *         description: Maxicours token generated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 redirectionEndpoint:
 *                   type: string
 *                   description: The redirection endpoint with the generated token appended.
 *       400:
 *         description: Bad request. The class key is missing.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.post(
  "/maxicours",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    const classKey = req.query.class;
    await MaxiCoursConnexionsModel.create({
      userId: req.userId,
      userType: req.userRole,
    });
    if (!classKey) {
      return res.status(400).json({ error: "Please provide a class" });
    }
    try {
      const token = jwt.sign(req.body, process.env.MAXICOURS_SECRET);
      if (!token) {
        return res.status(500).json({ error: "Invalid token" });
      }
      return res.status(200).json({
        redirectionEndpoint: `${maxicoursEndPointsByClass[classKey]}?token=${token}`,
      });
    } catch (error) {
      console.log("Error in generating token", error);
      return res
        .status(500)
        .json({ error: "Internal Server Error", message: error });
    }
  }
);
//decode maxicours token
/**
 * @swagger
 * /api/v1/maxicours/decode:
 *   post:
 *     summary: Decode Maxicours Token
 *     description: Decode a Maxicours token to retrieve user data.
 *     tags: [Maxicours]
 *     parameters:
 *       - in: query
 *         name: token
 *         schema:
 *           type: string
 *         required: true
 *         description: The Maxicours token to be decoded.
 *     responses:
 *       200:
 *         description: Token decoded successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 decoded:
 *                   type: object
 *                   description: The decoded user data from the token.
 *       400:
 *         description: Bad request. The token is missing or invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.post(
  "/maxicours/decode",
  [
    // verifyApiKey,
    // checkIfAuthenticated,
    // checkIfAuthorized([]),
  ],
  async (req, res) => {
    try {
      const token = req.query.token;
      if (!token) {
        return res.status(400).json({ error: "Please provide a valid token" });
      }
      const decoded = jwt.verify(token, process.env.MAXICOURS_SECRET);
      if (!decoded) {
        return res.status(500).json({ error: "Problem with decoding token" });
      }
      return res.status(200).json({ decoded });
    } catch (error) {
      return res.status(500).json({ error: "Internal Server Error" });
    }
  }
);
module.exports = router;
