const schedule = require("node-schedule");

var moment = require("moment-timezone");

//import BigBlueButton.helper.js
const bigBlueButtonHelper = require("../../controllers/bigBlueButton/bigBlueButton.Helper.js");

//import Webhooks.Helper.js
const webhooksHelper = require("../../controllers/webhooks/WebHooks.Helper.js");

//every 5 minutes check for the health of the webhooks
const ruleResumeGenerated = new schedule.RecurrenceRule();
//set hour to 8AM and time zone to UTC +2
ruleResumeGenerated.hour = 8;
ruleResumeGenerated.minute = 0;
ruleResumeGenerated.tz = "France/Paris";

const webhookHealthCheck = schedule.scheduleJob(
  "*/59 * * * *",
  async function () {
    try {
      await checkWebhooksHealth();
    } catch (error) {
      console.log(error);
      // logger
      //   .newLog(logger.getLoggerTypeConstants().bbbWebHooksJob)
      //   .alert(`Error in webhookHealthCheck: ${error}`);
    }
  }
);

async function checkWebhooksHealth() {
  try {
    let titleHeader = "######################################################";
    let title = `Webhook Health Check is running at ${moment()
      .tz("Europe/Paris")
      .format("YYYY-MM-DD HH:mm:ss")} 🩻`;
    console.log(titleHeader + "\n" + title);

    //get all webhooks
    const webhooksInMongoDB = await webhooksHelper.getAllWebHooks();

    let webHooksInBBBServer = await bigBlueButtonHelper.getHooksList();
    webHooksInBBBServer = webHooksInBBBServer.data;
    let allWebhooksAreInBBBServer = true;

    if (webhooksInMongoDB.length > 0) {
      //webhooksInMongoDB foreach webhook and await for the result
      webhooksInMongoDB.forEach(async (webhook) => {
        //check if webhook is in the webHooksInBBBServer is list of data
        let hookInBigBlueButtonServer;
        const isWebhookInBBBServer = webHooksInBBBServer.find(
          (webhookInBBBServer) => {
            hookInBigBlueButtonServer = webhookInBBBServer;
            return webhookInBBBServer.hookID === webhook.webHookId;
          }
        );

        //if all weebhooks are in the BBB server save in one variable and print in console

        //if webhook is not in the webHooksInBBBServer, create it again in the bigBlueButton server
        if (!isWebhookInBBBServer) {
          allWebhooksAreInBBBServer = false;
          //create webhook in bigBlueButton server
          const webhookCreated = await bigBlueButtonHelper.createWebhooks(
            webhook.callbackURL
          );
          //update webhook in mongoDB
          const webhookUpdated = await webhooksHelper.updateWebhook(
            webhook._id,
            hookInBigBlueButtonServer?.hookID,
            hookInBigBlueButtonServer?.callbackURL,
            hookInBigBlueButtonServer?.permanentHook,
            true
          );

          let logTitle = `Webhook is recreated in the BigBlueButton`;
          let logMessage = `Webhook with id ${webhookUpdated.webHookId} was not in the BigBlueButton server and was created again and updated in the MongoDB`;

          //new Log for webhook created
          // logger
          //   .newLog(logger.getLoggerTypeConstants().bbbWebHooksJob)
          //   .info(logTitle + "\n" + logMessage);
        }

        //show a message in the console, the webhook is up and running
        console.log(
          `\nWebhook is up and running hookID: ${webhook.webHookId}, callbackURL: ${webhook.callbackURL}`
        );
      });
    } else {
      //new Log for  no  webhook found in MongoDB
      let webHookAlert = `No webhook found in MongoDB 🚨`;
      let webHookBody = `Please notify the administrator to create a webhook in MongoDB`;
      // logger
      //   .newLog(logger.getLoggerTypeConstants().bbbWebHooksJob)
      //   .alert(webHookAlert + "\n" + webHookBody);
    }

    if (!allWebhooksAreInBBBServer) {
      let webHookAlert = `Some webhook are not in the BigBlueButton server, but they are recreated automatically  from webHookJob 🧡`;
      console.log(webHookAlert);
    } else {
      let webHookAlert = `All webhooks are in the BigBlueButton server and is up and running 🧡`;
      console.log(webHookAlert);
    }

    // end of the function checkWebhooksHealth
    let endTitleHeader =
      "######################################################";
    let endTitle = `\nWebhook Health Check is finished at ${moment()
      .tz("Europe/Paris")
      .format("YYYY-MM-DD HH:mm:ss")} 🩺`;
    console.log(endTitle + "\n" + endTitleHeader);
  } catch (error) {
    console.log(error);
    // logger
    //   .newLog(logger.getLoggerTypeConstants().bbbWebHooksJob)
    //   .alert(`Error in checkWebhooksHealth: ${error}`);
  }
}

module.exports = {
  webhookHealthCheck,
};
