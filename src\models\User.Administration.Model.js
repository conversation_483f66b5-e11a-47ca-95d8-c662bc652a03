const mongoose = require("mongoose");

const userRole = require("../utils/constants/userRolesConstants.js");

const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const userAdministrationPreferencesSchema = new mongoose.Schema({
  program: [
    {
      programId: String,
      programName: String,
      priority: Number,
    },
  ],
  sector: [
    {
      sectorId: String,
      sectorName: String,
    },
  ],
  establishment: [
    {
      establishmentId: String,
      establishmentName: String,
    },
  ],
  department: {
    type: [
      {
        departmentId: String,
        departmentName: String,
      },
    ],
    default: [],
  },
  coordinators: [
    {
      coordinatorId: String,
      coordinatorName: String,
    },
  ],
  studyLevel: String,
  comments: String,

  availability: [
    {
      dayOfTheWeek: Number,
      startTime: String,
      endTime: String,
    },
  ],
  commitmentStartDate: {
    type: Date,
  },
  commitmentEndDate: {
    type: Date,
  },
  preemptiveEndDate: {
    type: Date,
  },
  motiveForPreemptiveEnd: String,
});

const userAdministrationSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      index: {
        unique: true,
        sparse: false,
      },
    },
    firebaseIds: [
      {
        identifier: String,
        firebaseUserId: String,
        provider: String,
      },
    ],
    // profilePic: String,
    status: String,
    contactDetails: {
      firstName: String,
      lastName: String,
      phoneNumber: String,
      email: String,
    },
    address: {
      addressLine1: String,
      city: String,
      country: String,
      zipCode: String,
    },
    dateOfBirth: Date,
    gender: String,
    userRole: String,
    invitation: {
      invitationCode: String,
      invitationAccepted: Boolean,
      invitationDate: {
        type: Date,
      },
      invitationAcceptedDate: {
        type: Date,
      },
    },
    createdAt: {
      type: Date,
      default: dateTimeHelper.getCurrentDateTimeInParisZone,
    },
    updatedAt: {
      type: Date,
    },
    iAgreeToReceiveInformationAndUpdates: {
      type: Boolean,
      default: false,
    },
    hasChangedPassword: {
      type: Boolean,
      default: false,
    },
    iAcceptTermsAndPolicyOfConfidentiality: {
      type: Boolean,
      default: false,
    },
    administrationPreferences: userAdministrationPreferencesSchema,
  },
  {
    versionKey: false,
  }
);

//export the model
module.exports = mongoose.model("userAdmin", userAdministrationSchema);
