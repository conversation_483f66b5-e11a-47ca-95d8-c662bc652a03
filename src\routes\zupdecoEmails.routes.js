const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("zupdecoEmails.routes.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const formidable = require("formidable");

//import EmailHelper.js from path src/controllers/email/EmailHelper.js
const emailHelper = require("../middleware/mailServiceSendGrid/EmailHelper.js");

const sendGrid = require("@sendgrid/mail");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");


/**
 * @swagger
 * /api/v1/contactZupDeco:
 *   post:
 *     summary: Send contact message to ZupDeco
 *     description: Endpoint to send contact messages to ZupDeco.
 *     tags:
 *       - Contact ZupDeco
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the sender.
 *               subject:
 *                 type: string
 *                 description: Subject of the message.
 *               message:
 *                 type: string
 *                 description: Message content.
 *     responses:
 *       200:
 *         description: Email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: Email sent successfully.
 *                 details:
 *                   type: string
 *                   example: Email is sent successfully, please wait for the response.
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: Bad request, please provide all required fields.
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: Failed to save new ScholarYear
 */
router.post("/contactZupDeco",[verifyApiKey], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const message = fields.message;
               const subject = fields.subject;
               const email = fields.email;
               const userId = req.cookies.userId
               let setupInvitation = await emailHelper.contactZupDecoEmail(email, subject, message, userId);
               console.log(`email sent to:`, setupInvitation);
               await sendGrid.send(setupInvitation)
                         let response = apiResponse.responseWithStatusCode(
                              apiResponse.apiConstants.API_REQUEST_SUCCESS,
                              `Email sent successfully.`,
                              `Email is sent successfully, please wait for the response.`
                         );
                         res.status(200).json(response);
          } catch (error) {
               console.error(error);
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to save new ScholarYear", error);
               // logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
               return res.status(500).json(response);
          }
     });
});

//export router
module.exports = router;
