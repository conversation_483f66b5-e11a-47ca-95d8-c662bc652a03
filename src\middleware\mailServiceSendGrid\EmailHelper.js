const sendGrid = require("@sendgrid/mail");

require("dotenv").config();

const sendGridConstants = require("../../utils/constants/sendgrid.constants.js");

sendGrid.setApiKey(process.env.SENDGRID_API_KEY);

const moment = require("moment");
const UserModels = require("../../models/User.Models.js");
//Invitation Email

exports.invitationToStudent = (email, subject, invitationLink, firstName) => {
  const encodeLink = encodeURIComponent(invitationLink);
  const setupInvitation = {
    to: email,
    dynamic_template_data: {
      subject: subject,
      invitationLink: invitationLink,
      firstName: firstName,
      email: email,
    },
    from: sendGridConstants.ZupDecoEmails.EMAIL_HELLO_ZUPDECO,
    templateId:
      sendGridConstants.EmailTemplates.TEMPLATE_SEND_INVITATION_TO_STUDENT,
  };

  return setupInvitation;
};

exports.invitationToAdmin = (
  email,
  subject,
  invitationLink,
  firstName,
  role
) => {
  const setupInvitation = {
    to: email,
    dynamic_template_data: {
      subject: subject,
      invitationLink: invitationLink,
      firstName: firstName,
      role: role,
    },
    from: sendGridConstants.ZupDecoEmails.EMAIL_HELLO_ZUPDECO,
    templateId: sendGridConstants.EmailTemplates.TEMPLATE_SEND_USER_INVITATION,
  };

  return setupInvitation;
};

exports.invitationToTutor = (email, subject, invitationLink, firstName) => {
  const setupInvitation = {
    to: email,
    dynamic_template_data: {
      subject: subject,
      invitationLink: invitationLink,
      firstName: firstName,
      email: email,
      role: "Tuteur.trice",
    },
    from: sendGridConstants.ZupDecoEmails.EMAIL_ZUPDECO_ORG,
    templateId:
      sendGridConstants.EmailTemplates.TEMPLATE_SEND_INVITATION_TO_TUTOR,
  };

  return setupInvitation;
};

exports.sendSessionNotification = (
  email,
  target,
  date,
  time,
  invitedUserFullName,
  emailCC,
  subject,
  firstName,
  sessionDate,
  sessionLink
) => {
  const timeStartSession = moment(sessionDate?.startDate).format("HH:mm");
  const timeEndSession = moment(sessionDate?.endDate).format("HH:mm");

  const setupInvitation = {
    to: email,
    ...(emailCC ? { cc: emailCC } : {}),
    dynamic_template_data: {
      name: subject,
      date: date,
      dateTime: time,
      tutorName: target === "tutor" ? firstName : invitedUserFullName,
      studentName: target === "student" ? firstName : invitedUserFullName,
      hours: `${timeStartSession} - ${timeEndSession}`,
      sessionLink: sessionLink,
    },
    from: sendGridConstants.ZupDecoEmails.EMAIL_ZUPDECO_ORG,
    templateId:
      sendGridConstants.EmailTemplates.TEMPLATE_NEW_SESSION_FROM_SESSIONS,
  };
  return setupInvitation;
};

//Contact ZupeDeco Email
exports.contactZupDecoEmail = async (email,subject, message, userId) => {
  const userData = await UserModels.findOne({ userId: userId }).lean();
  const setupInvitation = {
    to: sendGridConstants.ZupDecoEmails.EMAIL_CONTACT_ZUPDECO_TUTOR,
    cc : email !== "" ? email  : {},
    dynamic_template_data: {
      firstName: userData ? userData.contactDetails.firstName : "",
      lastName: userData ? userData.contactDetails.lastName : "",
      phoneNumber: userData && userData.contactDetails.phoneNumber ? userData.contactDetails.phoneNumber : "",
      subject: subject,
      message: message,
      isTutor: userData && userData.userRole == "tutor" ? true : false,
      isParent: userData && userData.userRole == "parent" ? true : false,
      isStudent: userData && userData.userRole == "student" ? true : false
    },
    from: sendGridConstants.ZupDecoEmails.EMAIL_HELLO_ZUPDECO,
    templateId: sendGridConstants.EmailTemplates.TEMPLATE_CONTACT_ZUPDECO,
  };
  return setupInvitation;
};

//Welcome Email
function welcomeEmail(email, subject, firstName) {
  const sendWelcomeEmail = {
    templateId: sendGridConstants.EmailTemplates.TEMPLATE_WELCOME_EMAIL,
    to: email,
    from: {
      name: sendGridConstants.SendGridConstants.EMAIL_SEND_FROM,
      email: sendGridConstants.CvitaeEmails.EMAIL_HELLO_CVITAE,
    },

    dynamic_template_data: {
      subject: subject,
      firstName: firstName,
      email: email,
    },
  };

  return sendWelcomeEmail;
}
