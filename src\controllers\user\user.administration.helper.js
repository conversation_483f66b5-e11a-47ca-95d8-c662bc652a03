const userAdministrationModel = require("../../models/User.Administration.Model.js");

const userModel = require("../../models/User.Models.js");

const userHelper = require("./User.Helper.js");

//import Establishment.Model.js
const establishmentModel = require("../../models/Establishment.Model.js");

//import Sector.Model.js
const sectorModel = require("../../models/Sectors.Model.js");

const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME
} = require("../../utils/constants/program.constants.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const userRoleStatusConstants = require("../../utils/constants/user.role.status.constants.js");

const appConstants = require("../../utils/constants/app.constants.js");

const tutorHelper = require("../tutor/tutor.helper.js");

const parentHelper = require("../parent/parent.helper.js");
const studentHelper = require("../students/student.helpers.js");
const sessionHelper = require("../sessions/sessions.helper.js");


const emailHelper = require("../../middleware/mailServiceSendGrid/EmailHelper.js");

const { v4: uuidv4 } = require("uuid");

const sendGrid = require("@sendgrid/mail");

require("dotenv").config();

const establishmentHelper = require("../establishments/establishments.helper.js");

const firebaseHelper = require("../firebase/firebase.helper.js");
const userRoleTranslated = require("../../utils/constants/user.roles.translated");
const GovernmentSectorsModel = require("../../models/GovernmentSectors.Model.js");
const SessionsModel = require("../../models/Sessions.Model.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const {
  getTutorsCountFromPreferences,
} = require("../tutor/tutor.pipelines.js");
const {
  getStudentsCountFromPreferences,
} = require("../students/student.pipelines.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const programConstants = require("../../utils/constants/program.constants.js");
const SectorsModel = require("../../models/Sectors.Model.js");
const onligneProgram = [HOME_CLASSES.programId, CLASSSES_HOME.programId, ZUPDEFOOT.programId]
const StudentPreferencesModel = require('../../models/Student.Preferences.Model.js');
const crSessionsModel = require('../../models/Cr.Report.Model.js');
const crReportsModel = require('../../models/Cr.Report.Model.js');

//function addNewUserAdministration
exports.addNewUserAdministration = async (userAdministrationData) => {
  try {
    const userId = userHelper.generateNewUserId();
    //create new user in database
    const contactDetails = {
      firstName: userAdministrationData.firstName,
      lastName: userAdministrationData.lastName,
      email: userAdministrationData.email ? userAdministrationData.email : "",
      phoneNumber: userAdministrationData.phoneNumber
        ? userAdministrationData.phoneNumber
        : "",
    };
    let newUserAdmin = await userAdministrationModel.create({
      userId: userId,
      contactDetails: contactDetails,
      hasChangedPassword: true,
      dateOfBirth: userAdministrationData.dateOfBirth,
      gender: userAdministrationData.gender,
      userRole: userAdministrationData.userRole,
      status: userAdministrationData.status,
      administrationPreferences:
        userAdministrationData.administrationPreferences,
    });
    // Add the coordinator to the sector coordinators array
    // if (
    //   userAdministrationData.userRole === "coordinator" &&
    //   userAdministrationData.administrationPreferences.sector.length
    // ) {
    //   userAdministrationData.administrationPreferences.sector.forEach(
    //     async ({ sectorId }) => {
    //       const updatedSector = await GovernmentSectorsModel.findOneAndUpdate(
    //         { _id: sectorId }, // Find the sector by _id
    //         {
    //           $push: {
    //             coordinators: {
    //               coordinatorName: `${contactDetails.firstName} ${contactDetails.lastName}`,
    //               userId,
    //             },
    //           },
    //         }, // Add the new coordinator item
    //         { new: true } // Return the updated document
    //       );
    //     }
    //   );
    // }

    const newUserDocument = await newUserAdmin.save();
    return {
      status: true,
      message: "User created successfully",
      data: newUserDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//update user administration
exports.updateUserAdministration = async (userAdministrationData) => {
  try {
    //get old data from database
    const userId = userAdministrationData.userId;
    let userAdministration = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();
    userAdministration = userAdministration.toObject();
    if (!userAdministration) {
      return { status: false, message: "User not found", data: null };
    }
    const currentEmail = userAdministration.contactDetails?.email?.toLowerCase();
    const targetEmail = userAdministrationData.email.toLowerCase();
    let identifierFirebase = ""
    if (currentEmail !== targetEmail) {
    try {
    console.log("Emails différents, mise à jour nécessaire.");
    const firebaseIds = await firebaseHelper.resetEmail(currentEmail, targetEmail);
    if (firebaseIds) {
      identifierFirebase = firebaseIds.email;
      // console.log("firebaseIds", firebaseIds.email, firebaseIds);
    } else {
      console.log("Aucune donnée retournée par resetEmail.");
    }
  } catch (error) {
    console.error("Erreur lors de la mise à jour de l'email Firebase:", error);
  }
  }

    const contactDetails = {
      firstName: userAdministrationData.firstName,
      lastName: userAdministrationData.lastName,
      email: userAdministrationData.email ? userAdministrationData.email : "",
      phoneNumber: userAdministrationData.phoneNumber
        ? userAdministrationData.phoneNumber
        : "",
    };

    userAdministration.contactDetails = contactDetails;
    if(identifierFirebase && identifierFirebase != "" ){
    userAdministration.firebaseIds[0].identifier = identifierFirebase
  } 
    userAdministration.gender = userAdministrationData.gender;
    userAdministration.status = userAdministrationData.status;
    userAdministration.administrationPreferences =
      userAdministrationData.administrationPreferences;
    if (
      userAdministrationData?.administrationPreferences?.program.programId ===
      HOME_CLASSES.programId
    ) {
      if (
        userAdministrationData?.administrationPreferences?.department?.length
      ) {
        const department =
          userAdministrationData.administrationPreferences.department;
        const coordinatorName =
          userAdministration.contactDetails.firstName +
          " " +
          userAdministration.contactDetails.lastName;
        // if (!userAdministration.administrationPreferences.departement)
        //   userAdministration.administrationPreferences.departement =
        //     new Object();

        // userAdministration.administrationPreferences.departement = {
        //   departementId: department.departmentId,
        //   departementName: department.departmentName,
        // };

        if (!userAdministration.administrationPreferences.departement?.length) {
          userAdministration.administrationPreferences.departement = [];
        }

        department.map((department) => {
          const existingDepartment =
            userAdministration.administrationPreferences.departement.find(
              (dept) => dept.departementId === department.departmentId
            );

          if (!existingDepartment) {
            userAdministration.administrationPreferences.departement.push({
              departementId: department.departmentId,
              departementName: department.departmentName,
            });
          }
        });

        const sectorCoordinatorDetails = {
          userId: userAdministration.userId,
          coordinatorName,
        };

        // const assignedSectors = await SectorsModel.find({
        //   "coordinators.userId": userAdministration.userId,
        // });
        // if (assignedSectors?.length) {
        //   const sectorIds = assignedSectors.map((sector) => sector._id); // Extract sector IDs
        //   await SectorsModel.updateMany(
        //     { _id: { $in: sectorIds } }, // Match documents with IDs in the assignedSectors list
        //     { $pull: { coordinators: { userId: userAdministration.userId } } } // Remove the specific coordinator from the coordinators array
        //   );
        // }

        const sector = await SectorsModel.findOne({
          code: { $in: department.map((dep) => dep.departmentId) },
        });
        if (!sector?.coordinators) {
          sector.coordinators = [];
          sector.coordinators.push(sectorCoordinatorDetails);
        } else {
          const foundUser = sector.coordinators.find(
            (dep) => dep.userId === userAdministration.userId
          );
          if (foundUser) foundUser.coordinatorName = coordinatorName;
          else {
            sector.coordinators.push(sectorCoordinatorDetails);
          }
        }
        await sector.save();
      }
    }
    const updatedUserDocument = await userAdministrationModel.findOneAndUpdate(
      { userId: userId },
      userAdministration,
      { new: true }
    );
    return {
      status: true,
      message: "User updated successfully",
      data: updatedUserDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//delete user administration
exports.deleteUserAdministration = async (userId) => {
  try {
    //find user in database
    const userAdminDocument = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();

    if (!userAdminDocument) {
      return { status: false, message: "User not found", data: null };
    }

    //get list of firebaseIds
    const firebaseIds = userAdminDocument.firebaseIds;

    await userAdminDocument.remove();

    //delete user from firebase
    for (let i = 0; i < firebaseIds.length; i++) {
      const firebaseId = firebaseIds[i];
      await firebaseHelper.deleteUserFromAuth(firebaseId.identifier);
    }

    return { status: true, message: "User deleted successfully", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//update firebaseIds and profilePicture
exports.updateUserAdministrationFirebaseIdsAndProfilePicture = async (
  userId,
  newFirebaseIds,
  profilePicture
) => {
  const updatedDocument = await userAdministrationModel.findOneAndUpdate(
    { userId: userId },
    {
      $push: {
        firebaseIds: newFirebaseIds,
        // profilePic: profilePicture,
      },
    },
    { new: true }
  );

  if (!updatedDocument) {
    return { status: false, message: "User not found", data: null };
  } else {
    return {
      status: true,
      message: "User updated successfully",
      data: updatedDocument,
    };
  }
};

//get userAdministration by userId
exports.getUserAdministrationByUserId = async (userId) => {
  try {
    //get User Details
    const userDocument = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();
    let coordinator = null;
    if (userDocument) {
      const departmentIds =
        userDocument.administrationPreferences.department.map(
          (dep) => dep.departmentId
        );
      // const coordinatorDetails = await UserAdministrationModel.findOne({
      //   "administrationPreferences.department.departmentId": { "$in": departmentIds },
      //   userRole: userRoleConstants.COORDINATOR,
      // }).exec();
      const coordinatorDetails = await UserAdministrationModel.findOne({
        "administrationPreferences.department": {
          $elemMatch: {
            departmentId: { $in: departmentIds },
          },
        },
        userRole: userRoleConstants.COORDINATOR,
      }).exec();
      if (coordinatorDetails) {
        coordinator = {
          userId: coordinatorDetails?.userId,
          fullName: `${coordinatorDetails?.contactDetails.firstName} ${coordinatorDetails?.contactDetails.lastName}`,
          email: coordinatorDetails?.contactDetails.email,
        };
      }
    }

    //get Establishment list, establishmentId, establishmentName
    const establishmentList = await establishmentModel.find({}).exec();
    const modifiedEstablishmentList = establishmentList.map((establishment) => {
      return {
        establishmentId: establishment._id,
        establishmentName: establishment.establishmentName,
      };
    });

    //get Sector list, sectorId, sectorName
    const sectorList = await sectorModel.find({}).exec();
    const modifiedSectorList = sectorList.map((sector) => {
      return {
        sectorId: sector._id,
        sectorName: sector.name,
      };
    });

    //get Coordinator list, coordinatorId, coordinatorName
    // const coordinatorList = await userAdministrationModel
    //   .find({ userRole: userRoleConstants.COORDINATOR })
    //   .exec();

    //return just userId and name
    // const modifiedCoordinatorList = coordinatorList.map((coordinator) => {
    //   return {
    //     userId: coordinator.userId,
    //     coordinatorName:
    //       coordinator.contactDetails.firstName +
    //       " " +
    //       coordinator.contactDetails.lastName,
    //   };
    // });

    //get Program list, programId, programName
    const programList = programConstants;

    let listOfOptions = {
      establishmentList: modifiedEstablishmentList,
      sectorList: modifiedSectorList,
      programList: programList,
      // coordinatorList: modifiedCoordinatorList,
    };

    //let combinedData = { ...userDataCombined, ...listOfOptions };
    let combinedData = {
      userData: userDocument?._doc,
      ...listOfOptions,
      coordinator,
    };

    return {
      status: true,
      message: "User created successfully",
      data: combinedData,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

exports.getAdminStats = async (payload) => {
  try {
    const { userId, userRole } = payload;
    const page = 1;
    const pageSize = 10;
    const parents = await parentHelper.geISParentDashboard({
      page,
      pageSize,
      filter : '{}',
      sortBy : '{"department": 1}',
      userId,
      userRole,
    });
    const session =  await sessionHelper.sessionsDashboard({
      page,
      pageSize,
      filter : '{"sessionStatus":"session-0-to-be-scheduled"}',
      sortBy : undefined,
      userId,
      userRole,
      tutorUserId : undefined,
    });
    const tutor  = await tutorHelper.getIsTutorsDashboard({filter :'{}',
      sortBy : undefined,
      page,
      pageSize,
      userId,
      userRole,})

    const student = await studentHelper.getISStudentDashboard({
      page,
      pageSize,
      filter : undefined,
      sortBy : undefined,
      userId,
      userRole,
    });

    const totalNumberOfStudents = student?.totalNumberOfStudents || 0;
    const totalNumberOfTutors = tutor?.totalNumberOfTutors || 0;
    const totalNumberOfsession = session?.total || 0;
    const totalNumberOfParents = parents?.totalNumberOfParents || 0;
    return [
      { _id:"parent", count : totalNumberOfParents},
      { _id: "sessions", count: totalNumberOfsession },
      { _id: "student", count: totalNumberOfStudents },
      { _id: "tutors", count: totalNumberOfTutors },
    ];
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get All users based on userRole, and get details from UserAdministration.Preferences.Model.js
exports.getAllAdminUsersByUserRole = async (payload) => {
  try {
    const {
      page,
      pageSize,
      filter,
      sortByObject,
      userRole,
      userId,
      reqUserRole,
    } = payload;
    let filterData = buildFilter(filter);
    if (!filterData.status) {
      return { status: false, message: filterData.message, data: null };
    }
    let  filtredItem
    if(filter != undefined)
    {
      filtredItem = JSON.parse(filter);}    
    if (
      [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(
        reqUserRole
      )
    ) {
      const userAdminDetails = await UserAdministrationModel.findOne({
        userId,
      });
      if(reqUserRole == userRole){
        filterData.data.userRole = reqUserRole,
        filterData.data.userId = userId
      }
      const pref = userAdminDetails?.administrationPreferences;
      if (reqUserRole !== userRole && pref && pref.program?.length) {
        filterData.data["administrationPreferences.coordinators.coordinatorId"] = userId;
      }
    }
    let sortBy = buildSortBy(sortByObject);

    //add userRole to filter, if userRole is not null
    if (userRole) {
      //make value of userRole as string RegExp
      let userRoleRegExp = new RegExp(userRole, "i");
      filterData.data.userRole = userRoleRegExp;
    }
    // console.log("filterData =>",filterData.data)
    let userList = await userAdministrationModel
      .find(filterData.data)
      .sort(sortBy)
      .skip(pageSize * (page - 1))
      .limit(pageSize)
      .exec();

    // if (pageSize === 15) console.log("userList", userList.map(user => user.administrationPreferences.program))

    let totalUsers = await userAdministrationModel
      .countDocuments(filterData.data)
      .exec();

    let modifiedUserList = [];

    for (let i = 0; i < userList.length; i++) {
      const user = userList[i];
      let establishmentNames = [];
      const listOfEstablishments = user.administrationPreferences.establishment;

      for (let j = 0; j < listOfEstablishments.length; j++) {
        const establishment = listOfEstablishments[j];
        const establishmentId = establishment.establishmentId;
        // console.log("establishment", establishmentId);
       const establishmentName =
         await establishmentHelper.getEstablishmentName(establishmentId);
       establishmentNames.push(establishmentName);
       }
      // const department =
      //   user?.administrationPreferences?.departement?.departementName ?? "";
      const newItem = {
        userId: user.userId,
        firstName: user.contactDetails.firstName,
        lastName: user.contactDetails.lastName,
        email: user.contactDetails.email,
        status: user.status,
        userRole: user.userRole,
        establishmentName: establishmentNames,
        departmentName: user.administrationPreferences.department.length
          ? user.administrationPreferences.department
              .map((dep) => extractCityName(dep.departmentName)+`(${dep.departmentId})`)
              .join(", ")
          : "",
      };

      modifiedUserList.push(newItem);
    }

    return {
      status: true,
      message: "User created successfully",
      data: modifiedUserList,
      totalCount: totalUsers,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
function extractCityName(input) {
  const match = input.match(/^(.+?)(?:\s*\(\d{2}\))?$/);
  if (match) {
      return match[1];
  }
  return input;
}

// Get user details from FirebaseUserId
exports.getUserDetailsFromFirebaseIdentifier = async (firebaseIdentifier) => {
  try {
    const emailRegex = new RegExp(`^${firebaseIdentifier.replace(/[-\/\\^$.*+?()[\]{}|]/g, '\\$&')}$`, 'i'); // 'i' pour insensibilité à la casse
    const query = {
      'firebaseIds.identifier': { '$regex': emailRegex }
    };
    let user = await userAdministrationModel
      .findOne(query)
      .exec();
    return user;
  } catch (error) {
    console.log(error);
    return null;
  }
};

//get user details from email in contactDetails.email
exports.getUserDetailsFromEmail = async (email) => {
  try {
    const userDocument = await userAdministrationModel
      .findOne({ "contactDetails.email": email })
      .exec();
    return userDocument;
  } catch (error) {
    return null;
  }
};

// Get user details from userId
exports.getUserDetailsFromUserId = async (userId) => {
  try {
    const userDocument = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();
    if (!userDocument) {
      return { status: false, message: "User not found", data: null };
    }
    return { status: true, message: "User found", data: userDocument };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

function buildFilter(filter) {
  let applyFilter = {};
  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //add userRole to filter
  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (key == "lastName") {
      let valueReg = new RegExp(value, "i");
      applyFilter["contactDetails.lastName"] = valueReg;
    } else if (key == "email") {
      if (value && value.length > 0) {
        let valueReg = new RegExp(value, "i");
        applyFilter["contactDetails.email"] = valueReg;
        delete applyFilter[key];
      } else {
        delete applyFilter[key];
      }
    } else if (key == "firstName") {
      if (value && value.length > 0) {
        let valueReg = new RegExp(value, "i");
        applyFilter["contactDetails.firstName"] = valueReg;
        delete applyFilter[key];
      } else {
        delete applyFilter[key];
      }
    } else if (key == "programId") {
      delete applyFilter[key];
      applyFilter["administrationPreferences.program.programId"] = {
        $in: value,
      };
    } else if (key == "establishmentIds") {
      //this is an array
      applyFilter["administrationPreferences.establishment.establishmentId"] = {
        $in: value,
      };
    } else if (key === "department") {
      if (value && value.length > 0) {
        applyFilter["administrationPreferences.department.departmentId"] =
        {$in: value

        }

        delete applyFilter[key];
      } else {
        delete applyFilter[key];
      }
    } else if (key == "status") {
      if (value && value.length > 0) {
        applyFilter["status"] = { $in: value };
      } else {
        delete applyFilter[key];
      }
    } else if (key === "coordinators") {
      if (!value || value.length === 0) {
        delete applyFilter[key];
        continue;
      } else {
        applyFilter["administrationPreferences.coordinators.coordinatorId"] = {
          $in: value,
        };
        delete applyFilter[key];
      }
    }
  }
  return { status: true, data: applyFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);

    //check if sortBy is empty or not
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      } else if (key == "firstName") {
        key = "contactDetails.firstName";
      } else if (key == "email") {
        key = "contactDetails.email";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["createdAt", -1]];
  }

  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

//check if email is already registered or not
exports.checkIfEmailIsAlreadyRegistered = async (email) => {
  try {
    let user = await userAdministrationModel
      .findOne({
        $or: [
          { "contactDetails.email": email },
          { "firebaseIds.identifier": email },
        ],
      })
      .exec();

    //check if email is already exist or not in firebaseIds.identifier
    let emailInFirebaseIds = await userAdministrationModel
      .findOne({ "firebaseIds.identifier": { $eq: email } })
      .exec();

    if (user || emailInFirebaseIds) {
      return {
        status: true,
        message: "Email is already registered",
        data: null,
      };
    }
    return { status: false, message: "Email is not registered", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

////check if email is already registered or not in update user
exports.checkIfEmailIsAlreadyRegisteredInUpdateUser = async (email, userId) => {
  try {
    let user = await userAdministrationModel
      .findOne({
        $or: [
          { "contactDetails.email": email },
          { "firebaseIds.identifier": email },
        ],
      })
      .exec();

    //check if userIds are same or not
    if (user?.userId == userId) {
      return {
        status: false,
        message: "Email is already registered for the same user",
        data: null,
      };
    } else if (user) {
      return {
        status: true,
        message: "Email is already registered for another user",
        data: null,
      };
    } else {
      return { status: false, message: "Email is not registered", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: true, message: error.message, data: null };
  }
};

//#region SEND INVITATION EMAIL TO USER ADMIN

//generate random invitation code from email or phone number and add 10 random characters
exports.generateInvitationCode = (firebaseIdentifier) => {
  let invitationCode = firebaseIdentifier + "=" + uuidv4();
  return invitationCode;
};

//update user Admin with invitationCode
exports.updateUserAdminWithInvitationCode = async (
  userId,
  invitationCode,
  invitationAccepted
) => {
  try {
    let userDocument = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();

    if (!userDocument) {
      return { status: false, message: "User not found", data: null };
    }

    if (invitationAccepted) {
      userDocument.invitation.invitationAccepted = true;
      userDocument.invitation.invitationAcceptedDate = new Date();
      userDocument.status = userRoleStatusConstants.userAdminStatus.ACTIVE;
    } else {
      //setup invitation object
      let invitationObject = {
        invitationCode: invitationCode,
        invitationAccepted: false,
        invitationDate: new Date(),
        invitationAcceptedDate: null,
      };

      //add invitation object to userDocument
      userDocument.invitation = invitationObject;

      //update user status to active
      userDocument.status = userRoleStatusConstants.userAdminStatus.ACTIVE;
    }

    //save userDocument
    let updatedUserDocument = await userDocument.save();

    return {
      status: true,
      message: "Invitation code updated successfully",
      data: updatedUserDocument,
    };
  } catch (error) {
    return { status: false, message: error.message, data: error.message };
  }
};

//Check if invitation code is valid
exports.checkInvitationCodeValidity = async (userId, invitationCode) => {
  const isExistInvitationCode = await userAdministrationModel
    .findOne({
      userId: userId,
      "invitation.invitationCode": invitationCode,
    })
    .exec();

  if (!isExistInvitationCode) {
    return {
      status: false,
      message: "Invitation code is not valid",
      data: null,
    };
  }
  return {
    status: true,
    message: "Invitation code is valid",
    data: invitationCode,
  };
};

/**
 * Sends an invitation email to an external user.
 *
 * @param {Object} userDetails - The user's details.
 * @param {string} userRole - The user's role.
 * @param {boolean} sendDirectToSendGrid - Whether to send the email directly to SendGrid.
 * @returns {Object} An object containing the status and data of the invitation process.
 */
exports.sendInvitationToExternalUserEmail = async (
  userDetails,
  userRole,
  sendDirectToSendGrid
) => {
  try {
    const {
      userId,
      contactDetails: { email, firstName },
    } = userDetails;
    const { label: role } = userRoleTranslated.find(
      (role) => role.id === userDetails.userRole
    );

    // Generate invitation code
    const invitationCode = this.generateInvitationCode(email);
    let baseOnUserRoleBuildRoute;

    switch (userRole) {
      case userRoleConstants.ROLE_TUTOR:
        baseOnUserRoleBuildRoute = "tutor";
        break;
      case userRoleConstants.ROLE_STUDENT:
        baseOnUserRoleBuildRoute = "student";
        break;
      case userRoleConstants.ROLE_PARENT:
        baseOnUserRoleBuildRoute = "parent";
        break;
    }

    // Check if email is already registered or not
    const invitationBaseLink = `${appConstants.APP_BASE_URL}/${baseOnUserRoleBuildRoute}/register/?accept=${invitationCode}&firebaseIdentifier=${email}&userId=${userId}`;
    console.log("invitationBaseLink", invitationBaseLink);
    const invitationLink = await firebaseHelper.emailVerificationLink(
      invitationBaseLink,
      email
    );
    console.log("invitationLink", invitationLink);
    const subject = `Invited to join zupdeco.org`;
    const setupInvitation = emailHelper.invitationToAdmin(
      email,
      subject,
      invitationLink,
      firstName,
      role
    );

    switch (userRole) {
      case userRoleConstants.ROLE_TUTOR:
        await tutorHelper.updateUserAdminWithInvitationCode(
          userId,
          invitationCode,
          false
        );
        break;
      case userRoleConstants.ROLE_PARENT:
        await parentHelper.updateUserAdminWithInvitationCode(
          userId,
          invitationCode,
          false
        );
        break;
    }

    const invitationData = {
      setupInvitation,
      invitationCode,
      email,
    };
    console.log("invitationData", invitationData);
    console.log("sendDirectToSendGrid", sendDirectToSendGrid);
    if (sendDirectToSendGrid) {
      try {
        await sendGrid.send(setupInvitation);
        console.log(`Invitation email sent to ${email} successfully`);
      } catch (error) {
        console.error(
          `Error while sending invitation email to ${email}`,
          error
        );
      }
    } else {
      return invitationData;
    }
  } catch (error) {
    return { status: false, data: error };
  }
};

//get fullNames from userId
exports.getFullNamesFromUserId = async (userId) => {
  try {
    const contactDetails = await userAdministrationModel
      .findOne({ userId: { $eq: userId } })
      .select("contactDetails")
      .exec();

    if (contactDetails) {
      return (
        contactDetails.contactDetails.firstName +
        " " +
        contactDetails.contactDetails.lastName
      );
    } else {
      return null;
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

//get  admin profile
exports.getAdminProfile = async (userId) => {
  try {
    const contactDetails = await userAdministrationModel
      .findOne({ userId: { $eq: userId } })
      .exec();

    if (contactDetails) {
      let adminProfile = {
        fullName:
          contactDetails.contactDetails.firstName +
          " " +
          contactDetails.contactDetails.lastName,
        coordinatorName:
          contactDetails.contactDetails.firstName +
          " " +
          contactDetails.contactDetails.lastName,
        email: contactDetails.contactDetails.email,
        profilePicture: contactDetails.profilePic,
        userId: userId,
      };
      return {
        status: true,
        message: "Admin profile fetched successfully",
        data: adminProfile,
      };
    } else {
      return { status: false, message: "Admin profile not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get list of userRole : vsc
exports.getAllUserBaseOnUserRole = async (userRole) => {
  try {
    let userRoleList = await userAdministrationModel
      .find({ userRole: userRole })
      .exec();

    if (userRoleList) {
      return {
        status: true,
        message: "User role list fetched successfully",
        data: userRoleList,
      };
    } else {
      return { status: false, message: "User role list not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get All active VSCs
exports.getAllActiveVSCs = async (dayOfWeek) => {
  try {
    let userRoleList = await userAdministrationModel
      .find({
        userRole: userRoleConstants.VSC,
        status: userRoleStatusConstants.userAdminStatus.ACTIVE,
        "administrationPreferences.availability.dayOfTheWeek" : dayOfWeek
      })
      .exec();

    if (userRoleList) {
      return {
        status: true,
        message: "User role list fetched successfully",
        data: userRoleList,
      };
    } else {
      return { status: false, message: "User role list not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get coordinators by programId
exports.getCoordinatorsByProgram = async (programIds) => {
  try {
    let query = {
      userRole: userRoleConstants.COORDINATOR,
      status: userRoleStatusConstants.userAdminStatus.ACTIVE
    };

    // Si des programIds sont fournis, on ajoute le filtre
    if (programIds && programIds.length > 0) {
      query["administrationPreferences.program.programId"] = { $in: programIds };
    }

    const coordinators = await userAdministrationModel
      .find(query)
      .select("userId contactDetails.firstName contactDetails.lastName contactDetails.email contactDetails.phoneNumber")
      .exec();

    if (!coordinators || coordinators.length === 0) {
      return {
        status: false,
        message: "Aucun coordinateur trouvé",
        data: []
      };
    }

    const formattedCoordinators = coordinators.map(coordinator => ({
      userId: coordinator.userId,
      fullName: `${coordinator.contactDetails.firstName} ${coordinator.contactDetails.lastName}`,
      email: coordinator.contactDetails.email,
      phoneNumber: coordinator.contactDetails.phoneNumber
    }));

    return {
      status: true,
      message: "Liste des coordinateurs récupérée avec succès",
      data: formattedCoordinators
    };
  } catch (error) {
    console.error("Erreur dans getCoordinatorsByProgram:", error);
    return {
      status: false,
      message: "Erreur lors de la récupération des coordinateurs",
      data: error
    };
  }
};


