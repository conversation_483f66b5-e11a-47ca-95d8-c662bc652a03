const { MongoClient, ObjectId } = require("mongodb");
const fs = require("fs");
const path = require("path");
const moment = require("moment");
const gettypeOfEstablishments = require("./typeOfEstablishments.js");

const uri = "mongodb://localhost:27017/";
const dbName = "zupdeco-prod-db";
const collectionName = "educationannuaires";
const sectorCollectionName = "sectors";

const jsonFilePath = path.join(__dirname, "./estabZubBenevole.json");

// Fonction pour trouver le secteur basé sur le code postal
async function findSectorIdByPostalCode(client, postalCode) {
  const sectorCollection = client.db(dbName).collection(sectorCollectionName);
  const sector = await sectorCollection.findOne({
    code: postalCode.slice(2, 4),
  });
  if (sector) {
    return { id: sector._id.toHexString(), name: sector.name };
  }
  return { id: null, name: "" };
}

async function main() {
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    // Connexion à MongoDB
    await client.connect();
    console.log("Connected to MongoDB.");

    // Lire les données JSON
    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, "utf-8"));
    const jsonEstablishments = jsonData.map((item) => item.nom_etablissement);
    jsonEstablishments.push("Lycée général privé Saint Joseph La Salle");
    //  Accéder à la collection MongoDB
    const db = client.db(dbName);
    const collection = db.collection(collectionName);

    const mongoEstablishments = await collection.find({
      "typeOfEstablishment.typeOfEstablishmentName": {
        $in: jsonEstablishments,
      },
    });
    const list = mongoEstablishments.map(
      (item) => item.typeOfEstablishment.typeOfEstablishmentName
    );
    console.log("Number of establishments found in MongoDB:", list.length);
  } catch (err) {
    console.error(
      "Error connecting to MongoDB or inserting establishments:",
      err
    );
  } finally {
    await client.close();
  }
}
main().catch(console.error);
