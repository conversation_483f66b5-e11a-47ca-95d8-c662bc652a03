exports.typeOfTutors = {
  TYPE_TUTOR_FACE_TO_FACE: "face-to-face",
  TYPE_TUTOR_REMOTE: "remote",
};

exports.statusOfTutors = {
  TUTOR_ACTIVE: "active",
  TUTOR_DEACTIVATED: "deactivated",
  TUTOR_ON_HOLD: "on-hold",
  TUTOR_COMMITMENT_END: "commitment-end",
  TUTOR_PREEMPTIVE_END: "preemptive-end",
};

exports.tutoringType = {
  TUTORING_TYPE_PRIVATE: "private",
  TUTORING_TYPE_GROUP: "group",
};

exports.onBoardingStatus = {
  ON_BOARDING_IN_PROGRESS: "on-boarding-in-progress",
  ON_BOARDING_IN_REVIEW: "on-boarding-in-review",
  ON_BOARDING_COMPLETED: "on-boarding-completed",
};

exports.situationOfTutor = {
  NEW: "new",
  INCOMPLETE_RELANCE_1: "incomplete-relance-1",
  INCOMPLETE_RELANCE_2: "incomplete-relance-2",
  COMPLETE: "complete",
  AWAITING_STUDENT_1: "awaiting-student-1",
  STUDENT_1_FOUND: "student-1-found",
  SCHEDULED_SESSION_0: "scheduled-session-0",
  IN_TUTORING: "in-tutoring-1",
  FOR_RENEWAL: "for-renewal",
  PAUSE: "pause",
  STOP: "stop",
  NO_FURTHER_ACTION: "no-further-action",
};
exports.onBoardingStep = {
  ON_BOARDING_STEP_0: "0",
  ON_BOARDING_STEP_1: "1",
  ON_BOARDING_STEP_2: "2",
};
