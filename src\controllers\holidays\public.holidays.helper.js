const publicHolidaysModel = require("../../models/Public.Holidays.Model.js");
const moment = require("moment-timezone");

//get all public holidays
exports.getPublicHolidays = async () => {
     try {
          const publicHolidays = await publicHolidaysModel.find();
          return { status: true, message: "Public Holidays", data: publicHolidays };
     } catch (error) {
          console.log(error);
          return { status: false, message: error.message, data: null };
     }
};

// function name : isPublicHolidayAndAllowingTutoring
// input : date
// output : boolean
// description : check if the date is a public holiday and tutoring is allowed on that day
exports.isPublicHolidayAndAllowingTutoring = async (inputDate, scheduleDuringHolidays) => {
     try {
          const yearMonthDay = moment(inputDate).format("YYYY-MM-DD");

          const publicHoliday = await publicHolidaysModel.findOne({
               $expr: {
                    $eq: [{ $dateToString: { format: "%Y-%m-%d", date: "$date" } }, yearMonthDay],
               },
          });

          let tutoringAllowedStatus = false;
          if (scheduleDuringHolidays) {
               if (!publicHoliday || (publicHoliday && publicHoliday.tutoringAllowed)) {
                    //  console.log("Tutoring is allowed on this day ✅");
                    tutoringAllowedStatus = true;
               } else {
                    // console.log("Tutoring is not allowed on this day 🛑");
                    tutoringAllowedStatus = false;
               }
          } else {
               //if scheduleDuringHolidays is false  and publicHoliday is true then return false
               if (publicHoliday) {
                    // console.log("Tutoring is not allowed on this day 🛑");
                    tutoringAllowedStatus = false;
               } else {
                    // console.log("Tutoring is allowed on this day ✅");
                    tutoringAllowedStatus = true;
               }
          }
          console.log(`Tutoring is allowed on this public day ✅ ${yearMonthDay} : ${tutoringAllowedStatus}`);
          return tutoringAllowedStatus;
     } catch (error) {
          console.log(`Error in isPublicHolidayAndAllowingTutoring function: ${error.message}`);
          return false;
     }
};
