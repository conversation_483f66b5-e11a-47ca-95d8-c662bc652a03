const sessionModel = require("../../models/Sessions.Model.js");

const sessionsConstants = require("../../utils/constants/sessions.constants.js");

const userHelper = require("../user/User.Helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const sectorHelper = require("../sectors/sectors.helper.js");

const parentHelper = require("../parent/parent.helper.js");

const studentHelper = require("../students/student.helpers.js");

const tutorHelper = require("../tutor/tutor.helper.js");
const UserModels = require("../../models/User.Models.js");
const StudentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const crscreportsModel = require("../../models/Cr.Report.Model.js");

//get next session for student based on  userId
exports.getNextSessionsPerStudent = async (userId) => {
  try {
    console.log("userId", userId);
    //get current timestamp in paris zone
    const currentTimestamp = dateTimeHelper.getCurrentDateInParisZone();

    //get next session for student, next session is a session that is scheduled for today or in the future  but not before the current time
    const session = await sessionModel
      .findOne({
        students: {
          $elemMatch: {
            userId: userId,
            // , absence: false
          },
        },
        "sessionDate.startDate": { $gt: currentTimestamp },
        parentSessionId: { $ne: null },
        status: { $ne: "canceled" },
      })
      .sort({
        "sessionDate.startDate": 1, // sort by month in ascending order
      })
      .exec();

    //if there is no next session
    if (!session) {
      return { status: false, message: "No next session" };
    }

    //get student profile
    const studentProfile = await userHelper.getStudentProfileAndLevel(userId);

    // const coordinatorProfile = await this.getCoordinatorProfilePerStudent(
    //   userId
    // );
    const referentProfile = await userHelper.getUserDetailsByUserId(
      studentProfile.data.parentUserId
    );
    //get tutors profile
    const tutorIds = session.tutors.map((tutor) => tutor.userId);
    const tutorProfiles = await tutorHelper.getTutorProfiles(tutorIds);

    const nextSession = {
      sessionId: session.sessionId,
      sessionType: session.sessionType,
      program: session.program,
      status: session.status,
      vsc: session.vsc,
      studentProfile: studentProfile.data,
      school: session.school,
      tutorProfile: tutorProfiles.data,
      sessionLink: session.sessionLink,
      placeOrRoom: session.placeOrRoom,
      sessionDate: session.sessionDate,
      referent: referentProfile,
    };
    return { status: true, data: nextSession };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get rightReports for student based on  userId
exports.getRightReportsPerStudent = async (userId) => {
  try {
    const allMySessions = await this.getAllSessionsPerStudentCount(userId);

    const absenceCount = await this.getAllAbsencesPerStudentCount(userId);

    const referentProfile = await this.getReferentProfilePerStudent(
      userId
    );

    const rightReports = {
      sessions: allMySessions.data ? allMySessions.data : 0,
      absence: absenceCount.data ? absenceCount.data : 0,
      referent: referentProfile.data ? referentProfile.data : {},
    };

    return { status: true, data: rightReports };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get All sessions for student based on  userId as number
exports.getAllSessionsPerStudentCount = async (userId) => {
  try {
    const numberOfSessions = await sessionModel.countDocuments({
      students: { $elemMatch: { userId: userId } },
      parentSessionId: { $ne: null },
      status: { $ne: "canceled" },
    });

    return { status: true, data: numberOfSessions ? numberOfSessions : 0 };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//get All absences for student based on  userId as number
exports.getAllAbsencesPerStudentCount = async (userId) => {
  try {
    const numberOfAbsences = await sessionModel.countDocuments({
      students: { $elemMatch: { userId: userId, absence: true } },
      parentSessionId: { $ne: null },
      status: { $ne: "canceled" },
    });

    return { status: true, data: numberOfAbsences ? numberOfAbsences : 0 };
  } catch (error) {}
};

//get coordinator profile based  on sectorId on student profile
exports.getCoordinatorProfilePerStudent = async (userId) => {
  try {
    const studentProfile = await studentHelper.getStudentPreferences(userId);
    const sectors = studentProfile.data?.assignment?.department;

    if (!sectors) {
      return { status: false, message: "No sector for this student" };
    }

    const sectorId = sectors.departmentId;

    const coordinatorProfile = await sectorHelper.getCoordinatorsPerSector(
      sectorId
    );

    if (coordinatorProfile.status === false) {
      return { status: false, message: "No coordinator for this sector" };
    }
    return { status: true, data: coordinatorProfile.data };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get parent profile based  on sectorId on student profile
exports.getParentProfilePerStudent = async (userId) => {
  try {
    const studentProfile = await studentHelper.getStudentPreferences(userId);
    const parentProfile = await parentHelper.getParentIS(
      studentProfile.data.parentUserId
    );

    return { status: true, data: parentProfile.data };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get All sessions for Student based on  userId
exports.getAllSession = async (studentUserId) => {
  try {
    //get current timestamp in paris zone
    // const currentTimestamp = dateTimeHelper.getCurrentDateTimeInParisZone();

    //get top 3 sessions for tutor, top 3 sessions are sessions that are scheduled for today or in the future  but not before the current time
    const sessionList = await sessionModel
      .find({
        students: {
          $elemMatch: {
            userId: studentUserId,
            // , absence: false
          },
        },
        status: { $ne: "canceled" },
        parentSessionId: { $ne: null },
      })
      .sort({
        "sessionDate.startDate": 1, // sort by month in ascending order
      })
      .exec();

    //print students in session
    sessionList.forEach((session) => {
      console.log("session.students", session.students);
    });

    //if there is no top 3 sessions
    if (sessionList.length === 0) {
      return { status: false, message: "No top 3 sessions" };
    } else {
      //return these fields only : sessionId, sessionType, program, status, tutors, vsc, school, placeOrRoom, sessionDate
      let nextSessionList = [];
      const pastSessions = await this.getPastSessions(studentUserId);

      const studentProfile = await userHelper.getStudentProfileAndLevel(
        studentUserId
      );

      const coordinatorProfile = await this.getCoordinatorProfilePerStudent(
        studentUserId
      );

      const parentProfile = await this.getParentProfilePerStudent(
        studentUserId
      );

      for (let i = 0; i < sessionList.length; i++) {
        const session = sessionList[i];
        const sessionStudents = session.students;
        const tutorProfile = await tutorHelper.getTutorProfile(
          session.tutors[0].userId
        );
        
        // Récupérer le rapport pour cette session
        const report = await crscreportsModel.findOne({ sessionId: session.sessionId });

        if (studentProfile.status && studentProfile.data) {
          const nextSession = {
            sessionId: session.sessionId,
            sessionType: session.sessionType,
            program: session.program,
            status: session.status,
            vsc: session.vsc,
            tutorProfile: [tutorProfile.data],
            studentProfile: studentProfile.data ? studentProfile.data : [],
            school: session.school,
            referent: parentProfile.data ? [parentProfile.data] : [],
            sessionLink: session.sessionLink,
            placeOrRoom: session.placeOrRoom,
            sessionDate: session.sessionDate,
            pastSessions: pastSessions.data ? pastSessions.data : 0,
            sessionRecurrence: session.recurrence,
            report: report || null, // Ajouter le rapport ou null s'il n'existe pas
          };

          nextSessionList.push(nextSession);
        }
      }

      return {
        status: true,
        message: "All sessions for tutor",
        data: nextSessionList,
      };
    }
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//check if Student has a Tutor
exports.checkIfStudentHasTutor = async (studentUserId) => {
  try {
    //get student preferences
    const studentPreferences = await studentHelper.getStudentPreferences(
      studentUserId
    );

    let tutorProfiles = [];

    //if student has no preferences
    if (studentPreferences.status === false) {
      return { status: false, message: "Student has no preferences" };
    }

    const matching = studentPreferences.data.matching;

    //if student has no matching
    if (matching.length === 0) {
      return { status: false, message: "Student has no matching" };
    }

    for (let i = 0; i < matching.length; i++) {
      const tutorId = matching[i].tutorId;
      const session0Status = matching[i].status;

      //if session0Status is session-0-confirmed return tutor profile
      if (
        session0Status === sessionsConstants.sessionsStatus.SESSIONS_0_CONFIRMED
      ) {
        const tutorProfile = await tutorHelper.getTutorProfile(tutorId);
        tutorProfiles.push(tutorProfile.data);
      }
    }

    return { status: true, data: tutorProfiles };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get Past sessions for Student based on  userId
exports.getPastSessions = async (studentUserId) => {
  try {
    //const currentDate = dateTimeHelper.getCurrentDateTimeInParisZone();
    const currentDate = new Date();
    //get as number all the past sessions for student
    const pastSessions = await sessionModel
      .find({
        students: {
          $elemMatch: {
            userId: studentUserId,
            // , absence: false
          },
        },
        parentSessionId: { $ne: null },
        "sessionDate.startDate": { $lt: currentDate },
      })
      .exec();

    //if there is no past sessions
    if (pastSessions.length === 0) {
      return { status: false, message: "No past sessions", data: 0 };
    }

    return {
      status: true,
      message: "Past sessions",
      data: pastSessions.length,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
exports.getReferentProfilePerStudent = async (userId) => {
  try {
    // Fetch student preferences
    const student = await StudentPreferencesModel.findOne({ userId , });

    if (!student) {
      return {
        status: false,
        message: "Student not found.",
      };
    }

    // Extract parent user ID
    const parentUserId = student.parentUserId;

    if (!parentUserId) {
      return {
        status: false,
        message: "No parent associated with this student.",
      };
    }

    // Fetch referent profile (parent)
    const referent = await UserModels.findOne({ userId: parentUserId },
    { contactDetails: 1, address: 1, userRole: 1, userId: 1, status: 1, _id: 0 } // Projection to include only the required fields
    );

    if (!referent) {
      return {
        status: false,
        message: "Referent not found for this student.",
      };
    }

    // Return success response with referent data
    return {
      status: true,
      data: referent,
    };
  } catch (error) {
    console.error("Error in getReferentProfilePerStudent:", error);
    return {
      status: false,
      message: "An error occurred. Please try again later.",
    };
  }
};
