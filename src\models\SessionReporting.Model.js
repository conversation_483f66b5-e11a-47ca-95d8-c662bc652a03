const mongoose = require("mongoose");
const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const SessionReportingSchema = new mongoose.Schema(
  {
    meetingID: String,
    createDate: String,
    startTime: Number,
    participantCount: Number,
    attendees: [
      {
        userID: String,
        fullName: String,
      },
    ],

    createdAt: {
      type: Date,
      default: dateTimeHelper.getCurrentDateTimeInParisZone(),
    },
    lastUpdatedAt: {
      type: Date,
      default: dateTimeHelper.getCurrentDateTimeInParisZone(),
    },
  },
  {
    versionKey: false,
  }
);

module.exports = mongoose.model("sessionReportings", SessionReportingSchema);
