const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("specialty.routes.js");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const SpecialtyModel = require("../models/Specialty.Model.js");

//get specialty
/**
 * @swagger
 * /api/v1/specialties-level:
 *   get:
 *     summary: Get specialties by level
 *     tags: [Specialties]
 *     parameters:
 *       - in: query
 *         name: group
 *         schema:
 *           type: string
 *         description: The group name of the specialties
 *     responses:
 *       '200':
 *         description: Successfully retrieved the specialties
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   // Define the properties of the response object here
 *       '400':
 *         description: Bad request, missing group parameter
 *       '500':
 *         description: Internal server error
 */
router.get("/specialties-level", async (req, res, next) => {
     try {
          const group = req.query.group;

          const specialtyDocument = await SpecialtyModel.find({group}).exec();
          if (specialtyDocument) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `A level by id`, specialtyDocument);
               return res.status(200).json(response);
          } else {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Failed to get a level by id`, `No level with id ${levelId} found`);
               return res.status(200).json(response);
          }
     } catch (error) {
          console.log(error);
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Failed to get a level by id`, error);
          return res.status(500).json(response);
     }
});

//export router
module.exports = router;
