const express = require("express");

const router = express.Router();

const apiLoggerS = require("../services/logger/LoggerService.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

//import userHelper.js from path src/controllers/user/user.Helper.js
const userHelper = require("../controllers/user/User.Helper.js");

const tutorHelper = require("../controllers/tutor/tutor.helper.js");

const parentHelper = require("../controllers/parent/parent.helper.js");

const studentHelper = require("../controllers/students/student.helpers.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const userAdministrationHelper = require("../controllers/user/user.administration.helper.js");

const { update } = require("lodash");

require("dotenv").config();

const establishmentHelper = require("../controllers/establishments/establishments.helper.js");

const sessionStudentHelper = require("../controllers/sessions/sessions.student.helper.js");

const levelsModel = require("../models/Levels.Model.js");
const tutorJob = require("../services/aaJobs/tutor.job.js");
const sendGridConstants = require("../utils/constants/sendgrid.constants.js");

const mongoose = require("mongoose");

const admin = require("firebase-admin");
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const Users = require("../models/User.Models.js");
const matchingHelper = require("../controllers/matching/matching.helper.js")
const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");

const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
  ROLE_TUTOR,
  ROLE_PARENT,
  ROLE_STUDENT,
  VSC,
} = require("../utils/constants/userRolesConstants.js");
const userRolesConstants = require("../utils/constants/userRolesConstants.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");
const sessionModel = require("../models/Sessions.Model.js");
const crscreportsModel = require("../models/Cr.Report.Model.js");

router.use((req, res, next) => {
  verifyUserRole.checkUserRoleAndAccessLevel(
    req,
    res,
    next,
    mongodbModelConstants.modelName.USER_ADMINISTRATION
  );
});

//#region TUTOR ADMINISTRATION

//create new Tutor
/**
 * @swagger
 * /api/v1/tutor/is:
 *   post:
 *     summary: Create New Tutor (IS)
 *     description: Create a new tutor from the Information System (IS).
 *     tags: [Tutor]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               tutorData:
 *                 type: string
 *                 description: JSON string representing the details of the tutor to be created.
 *                 example: '{"contactDetails": {"firstName": "John", "lastName": "Doe", "email": "<EMAIL>"}, "otherDetails": {"field": "value"}}'
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: New tutor created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the newly created tutor.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.post(
  "/tutor/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const tutorData = JSON.parse(fields.tutorData);
        //check if tutorData is not null
        if (!tutorData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The tutor data is required to create a new tutor.`,
            `Please provide a valid tutor data.`
          );
          return res.status(500).json(response);
        }

        //check if tutor exists
        /* const { status } =
        await userHelper.ifUserExistOnCreateWithSameNameLastName(
          userRolesConstants.ROLE_TUTOR,
          tutorData.contactDetails.firstName,
          tutorData.contactDetails.lastName
        );

      if (!status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The tutor already exists with the same name and last name. Please check the list of tutors.`,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(200).json(response);
      } */
        //Adding user to firebase when creating new tutor from IS
        const addUserToFirebase = await admin.auth().createUser({
          email: tutorData.contactDetails.email,
          password: await userHelper.generatePassword(), // Temporary password that will be updated when user signs in
          displayName: `${tutorData.contactDetails.firstName} ${tutorData.contactDetails.lastName}`,
        });
        //check if tutor exists with same email address
        const email = tutorData.contactDetails.email;
        const ifTutorExistsWithEmail =
          await userHelper.checkIfEmailIsAlreadyRegistered(email);

        if (
          ifTutorExistsWithEmail.status &&
          !ifTutorExistsWithEmail.proceed &&
          addUserToFirebase
        ) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Email address already registered`,
            `Please provide another email address`,
            apiResponse.apiConstants.EMAIL_IS_ALREADY_REGISTERED
          );
          return res.status(200).json(response);
        }
        const token = generateAccessToken(tutorData.userId);
        const refreshToken = generateRefreshToken(tutorData.userId);
        const updateDocument = await tutorHelper.createNewTutorIS(tutorData);
        if (updateDocument.status) {
          userAdministrationHelper.sendInvitationToExternalUserEmail(
            { ...updateDocument.data, token },
            userRolesConstants.ROLE_TUTOR,
            true
          );

          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `New tutor created successfully`,
            updateDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          res.cookie("refreshToken", refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            path: "/",
          });
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error creating new tutor`,
            updateDocument.data,
            apiResponse.apiConstants.DATA_NOT_SAVED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error creating new tutor`,
          error,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(500).json(response);
      }
    });
  }
);

//update Tutor data
/**
 * @swagger
 * /api/v1/tutor/is:
 *   put:
 *     summary: Update Tutor (IS)
 *     description: Update an existing tutor from the Information System (IS).
 *     tags: [Tutor]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               tutorData:
 *                 type: string
 *                 description: JSON string representing the details of the tutor to be updated.
 *                 example: '{"_id": "60a82459b3a97a24c491ed41", "contactDetails": {"firstName": "John", "lastName": "Doe", "email": "<EMAIL>"}, "otherDetails": {"field": "value"}}'
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: Tutor updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the updated tutor.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.put(
  "/tutor/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const tutorData = await JSON.parse(fields.tutorData);
        const connectedUser = req.userId;
        //check if tutorData is not null
        if (!tutorData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The tutor data is required to update a tutor.`,
            `Please provide a valid tutor data.`
          );
          return res.status(500).json(response);
        }

        //check if parent exists
        const updateDocument = await tutorHelper.updateTutorIS(
          tutorData,
          connectedUser
        );

        if (updateDocument.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Tutor updated successfully`,
            updateDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          console.log("error on updating tutor data", updateDocument);
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error updating tutor`,
            updateDocument.data,
            apiResponse.apiConstants.DATA_NOT_UPDATED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log("error on updating tutor data");
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error on updating tutor data`,
          error,
          apiResponse.apiConstants.DATA_NOT_UPDATED
        );
        return res.status(500).json(response);
      }
    });
  }
);
router.patch(
  "/tutor/update/simple",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    try {
      const tutorData = req.body; // Extract fields directly from JSON body
      const connectedUser = req.body.userId;
      // Ensure at least one field is provided for update
      if (!tutorData || Object.keys(tutorData).length === 0) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `No update fields provided.`,
          `Please provide at least one valid field to update.`
        );
        return res.status(400).json(response);
      }

      // Perform partial update
      const updateResult = await TutorPreferencesModel.findOneAndUpdate(
        { userId: connectedUser },
        {
          $set: {
            "activity.comments": tutorData.comment,
            situation: tutorData.situation,
            suiviCoordo: tutorData.suiviCoordo,
            updatedAt: Date.now(),
            "LastUpdatedBy.userId": tutorData.UpdaterUserId,
            "LastUpdatedBy.firstName": tutorData.updaterFirstName,
            "LastUpdatedBy.lastName": tutorData.updaterLastName,
            "LastUpdatedBy.email": tutorData.updaterEmail,
          },
        }
      );
      if (updateResult.contactDetails) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Tutor updated successfully`,
          updateResult.data,
          apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
        );
        return res.status(200).json(response);
      } else {
        console.error("Error updating tutor data", updateResult);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating tutor`,
          updateResult.data,
          apiResponse.apiConstants.DATA_NOT_UPDATED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.error("Error on updating tutor data", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error on updating tutor data`,
        error.message,
        apiResponse.apiConstants.DATA_NOT_UPDATED
      );
      return res.status(500).json(response);
    }
  }
);

router.patch(
  "/tutor/is/availability",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    try {
      const availabilityData = req.body;

      //check if tutorData is not null
      if (!availabilityData) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The availability data is required to update a tutor.`,
          `Please provide a valid availability data.`
        );
        return res.status(500).json(response);
      }
      const { tutorId, availabilityId, taken } = availabilityData;

      const updateDocument = await tutorHelper.updateTutorAvailability({
        tutorId: tutorId,
        availability_id: availabilityId,
        taken: taken,
      });

      if (updateDocument.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Tutor availability updated successfully`,
          updateDocument.data,
          apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
        );
        return res.status(200).json(response);
      } else {
        console.log("error on updating tutor availability", updateDocument);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating tutor availability`,
          updateDocument.data,
          apiResponse.apiConstants.DATA_NOT_UPDATED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log("error on updating tutor data");
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error on updating tutor data`,
        error,
        apiResponse.apiConstants.DATA_NOT_UPDATED
      );
      return res.status(500).json(response);
    }
  }
);

//get list of all tutors base on tutor type
/**
 * @swagger
 * /api/v1/tutor/is/dashboard:
 *   get:
 *     summary: Get Tutor Dashboard (IS)
 *     description: Retrieve a list of tutors for the Information System (IS) dashboard.
 *     tags: [Tutor]
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: Number of items per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number.
 *       - in: query
 *         name: establishments
 *         schema:
 *           type: string
 *         description: Filter by establishments.
 *       - in: query
 *         name: program
 *         schema:
 *           type: string
 *         description: Filter by program.
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *         description: Filter by status.
 *       - in: query
 *         name: sector
 *         schema:
 *           type: string
 *         description: Filter by sector.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter query.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by field.
 *       - in: query
 *         name: exportCsv
 *         schema:
 *           type: boolean
 *         description: Export as CSV.
 *     responses:
 *       200:
 *         description: List of tutors retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of tutors.
 *                   items:
 *                     type: object
 *                     description: Tutor details.
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                 pageSize:
 *                   type: integer
 *                   description: Number of items per page.
 *                 totalNumberOfTutors:
 *                   type: integer
 *                   description: Total number of tutors.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.get(
  "/tutor/is/dashboard",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  async (req, res) => {
    try {
      let pageSize = req.query.pageSize;
      let page = req.query.page;

      const userRole = req.userRole;
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const userId = req.query.userId;
      const exportCsv = req.query.exportCsv === "true";

      //if exportCsv is true, then return all parents paginated and page size 1000000000
      if (exportCsv) {
        page = 1;
        pageSize = 1000000000;
      }

      const listOfTutors = await tutorHelper.getIsTutorsDashboard({
        filter,
        sortBy,
        page,
        pageSize,
        userId,
        userRole,
      });

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of tutors on the [IS] dashboard was retrieved successfully.`,
        listOfTutors.data,
        listOfTutors.page,
        listOfTutors.pageSize,
        listOfTutors.totalNumberOfTutors
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Error retrieving tutor list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/tutor/is/all:
 *   get:
 *     summary: Get All Tutors (IS)
 *     description: Retrieve a complete list of tutors for the Information System (IS) without pagination.
 *     tags: [Tutor]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for tutors.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort by field.
 *     responses:
 *       200:
 *         description: Complete list of tutors retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of tutors.
 *                   items:
 *                     type: object
 *                     description: Tutor details.
 *                 totalNumberOfTutors:
 *                   type: integer
 *                   description: Total number of tutors.
 *       400:
 *         description: Bad Request.
 *       500:
 *         description: Internal Server Error occurred.
 */
// router.get(
//   "/tutor/is/stats",
//   verifyApiKey,
//   checkIfAuthenticated,
//   checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
//   async (req, res) => {
//     try {
//       const userRole = req.userRole;
//       const filter = req.query.filter;
//       const userId = req.userId;

//       const tutorStats = await tutorHelper.getTutorStatistics({
//         filter,
//         userId,
//         userRole,
//       });

//       if (!tutorStats.status) {
//         return res.status(400).json(
//           apiResponse.responseWithStatusCode(
//             apiResponse.apiConstants.API_REQUEST_ERROR,
//             tutorStats.message
//           )
//         );
//       }

//       let response = apiResponse.responseWithStatusCode(
//         apiResponse.apiConstants.API_REQUEST_SUCCESS,
//         "Les statistiques des tuteurs ont été récupérées avec succès",
//         tutorStats.data
//       );
//       console.log("tttttttttt", tutorStats.data)
//       return res.status(200).json(response);
//     } catch (error) {
//       console.log(error);
//       let response = apiResponse.responseWithStatusCode(
//         apiResponse.apiConstants.API_REQUEST_ERROR,
//         "Erreur lors de la récupération des statistiques des tuteurs",
//         error
//       );
//       return res.status(500).json(response);
//     }
//   }
// );

//get user data per Tutor, based on userId. Combine data from user data and user preferences
/**
 * @swagger
 * /api/v1/tutor/is:
 *   get:
 *     summary: Get Tutor Information (IS)
 *     description: Retrieve information about a tutor for the Information System (IS).
 *     tags: [Tutor]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: The unique identifier of the tutor.
 *         required: true
 *     responses:
 *       200:
 *         description: Tutor data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Tutor information.
 *                 tutorPreferences:
 *                   type: object
 *                   description: Tutor preferences.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */

router.get(
  "/tutor/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is not null
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The userId is required to retrieve tutor data.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      const tutorUserDocument = await userHelper.getUserDetailsByUserId(userId);

      //check if tutorUserDocument is not null
      if (!tutorUserDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The tutor data was not found.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      //delete firebaseIds from tutorUserDocument
      delete tutorUserDocument.firebaseIds;

      const tutorUserPreferencesDocument =
        await tutorHelper.getTutorPreferences(userId);
      let establishmentLists = [];
      if (tutorUserPreferencesDocument) {
        const establishmentList =
          tutorUserPreferencesDocument.assignment.establishments;
        if (establishmentList.length > 0) {
          for (let i = 0; i < establishmentList.length; i++) {
            const establishmentId = establishmentList[i].establishmentId;
            const establishmentName = establishmentList[i].establishmentName;
            const establishment =
              await establishmentHelper.getEstablishmentName(establishmentId);
            let newEstablishment = {
              establishmentId: establishmentId,
              establishmentName: establishment || establishmentName,
              isMissing: establishmentList[i].isMissing,
            };
            establishmentLists.push(newEstablishment);
          }
          tutorUserPreferencesDocument.assignment.establishments =
            establishmentLists;
        }

        //get levels Name for levelsId in tutorUserPreferencesDocument
        const levelsList = tutorUserPreferencesDocument.assignment.level;

        if (levelsList.length > 0) {
          for (let i = 0; i < levelsList.length; i++) {
            const levelId = levelsList[i].levelId;
            if (!levelId) {
              continue;
            }
            const level = await levelsModel.findOne({
              _id: mongoose.Types.ObjectId(levelId),
            });
            let newLevel = {
              levelId: levelId,
              levelName: level.levelName ? level.levelName : "",
            };
            levelsList[i] = newLevel;
          }
          tutorUserPreferencesDocument.assignment.level = levelsList;
        }
      }
      if (
        tutorUserPreferencesDocument !== null &&
        tutorUserPreferencesDocument._doc
      ) {
        tutorUserPreferencesDocument._doc.documents = {
          ...tutorHelper.encodeBase64(
            tutorUserPreferencesDocument._doc.documents
          ),
        };
      }
      //combine data from tutorUserDocument and tutorUserPreferencesDocument
      const tutorPreferences =
        tutorUserPreferencesDocument !== null &&
        tutorUserPreferencesDocument._doc
          ? tutorUserPreferencesDocument._doc
          : {};
          
           let sessionPassesNbre = 0;
                          let nbrCr = 0;
                          let note = 0;
                          let lastSession = {};
                          let futurSessions = {};
                          if(tutorPreferences.matchedStudents.length > 0){
          
                            const sessionPasses = await sessionModel.find({
                              status: "session-0-to-be-scheduled",
                              "tutors.userId": tutorPreferences.userId,
                              "students.userId": tutorPreferences.matchedStudents[0].userId,
                              "sessionDate.startDate": { $lte: new Date() },
                              parentSessionId : {$ne : null}
                            }).sort({"sessionDate.endDate" : -1});
                            sessionPassesNbre = sessionPasses?.length || 0
                            if(sessionPassesNbre !== 0){
                              lastSession = sessionPasses[0]
                            }
                            futurSessions = await sessionModel.findOne({
                              status: "session-0-to-be-scheduled",
                              "tutors.userId": tutorPreferences.userId,
                              "students.userId": tutorPreferences.matchedStudents[0].userId,
                              "sessionDate.startDate": { $gte: new Date() },
                              parentSessionId : {$ne : null}
                            }).sort({"sessionDate.endDate" : 1});
                            nbrCr = await crscreportsModel.countDocuments({
                              status: { $ne: "to-be-entered" },
                             "tutors.userId": tutorPreferences.userId,
                              "students.userId": tutorPreferences.matchedStudents[0].userId,
                            });
                            const aggregationResult = await crscreportsModel.aggregate([
                              {
                                $match: {
                                  status: { $ne: "to-be-entered" },
                               "tutors.userId": tutorPreferences.userId,
                              "students.userId": tutorPreferences.matchedStudents[0].userId,
                                }
                              },
                              {
                                $group: {
                                  _id: null,
                                  sumGrade: { $sum: "$grade" },
                                  count: { $sum: 1 }
                                }
                              }
                            ]);
                            const SumGradeOptimized = aggregationResult.length > 0 ? aggregationResult[0].sumGrade : 0;
                            note = nbrCr > 0 ? Math.floor(SumGradeOptimized / nbrCr) : 0;
                          console.log("gggggggggggg",sessionPassesNbre,nbrCr, note)
          
                          }
                          tutorPreferences.nbrSession = sessionPassesNbre
                          tutorPreferences.nbrCr = nbrCr
                          tutorPreferences.note = note
                          tutorPreferences.lastSession = lastSession
                          tutorPreferences.futurSessions = futurSessions
          tutorPreferences.availability =  await matchingHelper.checkTutorStudentAvailability(
            tutorPreferences?.availability,
            tutorPreferences.userId,
            true
          );
      const tutorData = {
        ...(tutorUserDocument !== null && tutorUserDocument._doc
          ? tutorUserDocument._doc
          : {}),
        tutorPreferences,

        // Ajouter manuellement l'affectabilité aux données de retour
        affectability : await matchingHelper.checkAffectability(tutorPreferences,tutorPreferences.availability, true, true)
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The tutor data was retrieved successfully.`,
        tutorData
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving tutor data`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//delete tutor by userId
/**
 * @swagger
 * /api/v1/tutor/is:
 *   delete:
 *     summary: Delete Tutor (IS)
 *     description: Delete a tutor from the Information System (IS).
 *     tags: [Tutor]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: The unique identifier of the tutor.
 *         required: true
 *     responses:
 *       200:
 *         description: Tutor deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.delete(
  "/tutor/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is not null
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The userId is required to delete tutor.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      const deleteDocument = await tutorHelper.deleteTutorIS(userId);

      if (deleteDocument.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The tutor was deleted successfully.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_DELETED
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The tutor was not deleted.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_NOT_DELETED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting tutor`,
        error,
        apiResponse.apiConstants.DATA_NOT_DELETED
      );
      return res.status(500).json(response);
    }
  }
);

//#endregion

//#region PARENT ADMINISTRATION

//create new parent
/**
 * @swagger
 * /api/v1/parent/is:
 *   post:
 *     summary: Create Parent (IS)
 *     description: Create a new parent in the Information System (IS) with the provided details.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               parentData:
 *                 type: string
 *                 description: JSON string representing the details of the parent to be created.
 *                 example: '{"contactDetails": {"firstName": "John", "lastName": "Doe", "email": "<EMAIL>"}}'
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: Parent created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the newly created parent.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.post(
  "/parent/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, ROLE_PARENT]),
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const parentData = fields.parentData
          ? JSON.parse(fields.parentData)
          : null;
        if (!parentData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent data is required to create a new parent.`,
            `Please provide a valid parent data.`
          );
          return res.status(500).json(response);
        }
        //check if parent exists
        /* const { status } =
        await userHelper.ifUserExistOnCreateWithSameNameLastName(
          userRolesConstants.ROLE_PARENT,
          parentData.contactDetails.firstName,
          parentData.contactDetails.lastName
        );

      if (!status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The parent already exists with the same name and last name. Please check the list of Parents.`,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(200).json(response);
      } */

        //check if parent exists with same email address
        const email = parentData.contactDetails.email;
        const ifExistsWithEmail =
          await userHelper.checkIfEmailIsAlreadyRegistered(email);

        if (ifExistsWithEmail.status && !ifExistsWithEmail.proceed) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Email address already registered`,
            `Please provide another email address`,
            apiResponse.apiConstants.EMAIL_IS_ALREADY_REGISTERED
          );
          return res.status(200).json(response);
        }

        const parentDocument = await parentHelper.createParentIS(parentData);
        //Adding user to firebase when creating new parent from IS
        const addUserToFirebase = await admin.auth().createUser({
          email: parentData.contactDetails.email,
          password: await userHelper.generatePassword(), // Temporary password that will be updated when user signs in
          displayName: `${parentData.contactDetails.firstName} ${parentData.contactDetails.lastName}`,
        });
        if (parentDocument.status && addUserToFirebase) {
          userAdministrationHelper.sendInvitationToExternalUserEmail(
            parentDocument.data,
            userRolesConstants.ROLE_PARENT,
            true
          );
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent was created successfully.`,
            parentDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent was not created.`,
            parentDocument.message,
            apiResponse.apiConstants.DATA_NOT_SAVED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error creating parent`,
          error,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(500).json(response);
      }
    });
  }
);

//update parent password in firebase
/**
 * @swagger
 * /api/v1/parent/update-pass:
 *   post:
 *     summary: Update Parent Password
 *     description: Update the password of a parent with the provided email.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email of the parent whose password needs to be updated.
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 description: New password for the parent.
 *                 example: new_password123
 *     responses:
 *       200:
 *         description: Parent password updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the parent whose password was updated.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.post(
  "/parent/update-pass",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([ROLE_PARENT]),
  async (req, res) => {
    const { email, password } = req.body; // Get the email and password from the request body
    const user = await admin.auth().getUserByEmail(email);
    if (!user) {
      return res
        .status(500)
        .json({ message: `user with email : ${email} does not exists` });
    }
    await admin.auth().updateUser(user.uid, {
      password: password,
      displayName: user.displayName,
    });

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `The parent password was updated successfully.`,
      user,
      apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
    );
    return res.status(200).json(response);
  }
);
//update tutor password in firebase
/**
 * @swagger
 * /api/v1/tutor/update-pass:
 *   post:
 *     summary: Update Tutor Password
 *     description: Update the password of a tutor with the provided email.
 *     tags: [Tutor]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email of the tutor whose password needs to be updated.
 *                 example: <EMAIL>
 *               password:
 *                 type: string
 *                 description: New password for the tutor.
 *                 example: new_password123
 *     responses:
 *       200:
 *         description: Tutor password updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the tutor whose password was updated.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.post(
  "/tutor/update-pass",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([ROLE_TUTOR]),
  async (req, res) => {
    const { email, password } = req.body; // Get the email and password from the request body
    const user = await admin.auth().getUserByEmail(email);
    if (!user) {
      return res
        .status(500)
        .json({ message: `user with email : ${email} does not exists` });
    }
    await admin.auth().updateUser(user.uid, {
      password: password,
      displayName: user.displayName,
    });

    await tutorJob.sendNotificationToTutor({
      email: email,
      data: {
        firstName: user.displayName,
        loginLink: `${process.env.APP_BASE_URL}/tutor/login`,
      },
      templateId:
        sendGridConstants.EmailTemplates.TEMPLATE_TUTOR_EMAIL_NEW_STATUT,
    });

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `The parent password was updated successfully.`,
      user,
      apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
    );
    return res.status(200).json(response);
  }
);

//update parent
/**
 * @swagger
 * /api/v1/parent/is:
 *   put:
 *     summary: Update Parent Information
 *     description: Update the information of a parent with the provided data.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               parentData:
 *                 type: string
 *                 description: JSON string representing the details of the parent to be updated.
 *                 example: '{"firstName": "John", "lastName": "Doe", "email": "<EMAIL>"}'
 *     responses:
 *       200:
 *         description: Parent information updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Details of the parent whose information was updated.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.put(
  "/parent/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, ROLE_PARENT]),
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const parentData = fields.parentData
          ? JSON.parse(fields.parentData)
          : null;
        if (!parentData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent data is required to update a parent.`,
            `Please provide a valid parent data.`
          );
          return res.status(500).json(response);
        }

        const parentDocument = await parentHelper.updateParentIS(parentData);
        //delete firebaseIds from parentDocument
        delete parentDocument?.data?.firebaseIds;

        if (parentDocument.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent was updated successfully.`,
            parentDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The parent was not updated.`,
            parentDocument.message,
            apiResponse.apiConstants.DATA_NOT_SAVED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating parent`,
          error,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(500).json(response);
      }
    });
  }
);

//get list of all parents
/**
 * @swagger
 * /api/v1/parent/is/dashboard:
 *   get:
 *     summary: Get Parent Dashboard Information
 *     description: Retrieve parent dashboard information with optional filtering and pagination.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number to retrieve.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for parent dashboard.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by.
 *       - in: query
 *         name: exportCsv
 *         schema:
 *           type: boolean
 *         description: Indicates whether to export data to CSV format.
 *     responses:
 *       200:
 *         description: Parent dashboard information retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of parent dashboard information.
 *                   items:
 *                     type: object
 *                     properties:
 *                       parentId:
 *                         type: string
 *                         description: The unique identifier of the parent.
 *                       firstName:
 *                         type: string
 *                         description: The first name of the parent.
 *                       lastName:
 *                         type: string
 *                         description: The last name of the parent.
 *                       email:
 *                         type: string
 *                         description: The email address of the parent.
 *                 page:
 *                   type: integer
 *                   description: The current page number.
 *                 pageSize:
 *                   type: integer
 *                   description: The number of items per page.
 *                 totalNumberOfParents:
 *                   type: integer
 *                   description: The total number of parents.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.get(
  "/parent/is/dashboard",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, VSC, MANAGER]),
  async (req, res) => {
    try {
      let pageSize = req.query.pageSize;
      let page = req.query.page;
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const exportCsv = req.query.exportCsv === "true";

      const userId = req.userId;
      const userRole = req.userRole;
      //if exportCsv is true, then return all parents paginated and page size 1000000000
      if (exportCsv) {
        page = 1;
        pageSize = 1000000000;
      }

      const listOfParents = await parentHelper.geISParentDashboard({
        page,
        pageSize,
        filter,
        sortBy,
        userId,
        userRole,
      });

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of parents on the [IS] dashboard was retrieved successfully.`,
        listOfParents?.data,
        listOfParents?.page,
        listOfParents?.pageSize,
        listOfParents?.totalNumberOfParents
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving parent list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//get user data per Parent, based on userId. Combine data from user data and parent preferences
/**
 * @swagger
 * /api/v1/parent/is:
 *   get:
 *     summary: Get Parent Information
 *     description: Retrieve parent information by userId.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the parent.
 *     responses:
 *       200:
 *         description: Parent data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   description: The unique identifier of the parent.
 *                 firstName:
 *                   type: string
 *                   description: The first name of the parent.
 *                 lastName:
 *                   type: string
 *                   description: The last name of the parent.
 *                 email:
 *                   type: string
 *                   description: The email address of the parent.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.get(
  "/parent/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, ROLE_PARENT]),
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is not null
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The userId is required to retrieve parent data.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      const parentUserDocument = await parentHelper.getParentIS(userId);

      //check if parentUserDocument is not null
      if (!parentUserDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The parent data was not found.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The parent data was retrieved successfully.`,
        parentUserDocument.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving parent data`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//delete parent by userId
/**
 * @swagger
 * /api/v1/parent/is:
 *   delete:
 *     summary: Delete Parent
 *     description: Delete parent by userId.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique identifier of the parent to be deleted.
 *     responses:
 *       200:
 *         description: Parent deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A message indicating the success of the deletion operation.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.delete(
  "/parent/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is not null
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The userId is required to delete parent.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      const deleteDocument = await parentHelper.deleteParentIS(userId);

      if (deleteDocument.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The parent was deleted successfully.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_DELETED
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The parent was not deleted.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_NOT_DELETED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting parent`,
        error,
        apiResponse.apiConstants.DATA_NOT_DELETED
      );
      return res.status(500).json(response);
    }
  }
);

//#endregion

//#region STUDENT ADMINISTRATION

//create student
/**
 * @swagger
 * /api/v1/student/is:
 *   post:
 *     summary: Create Student
 *     description: Create a new student.
 *     tags: [Student]
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               studentData:
 *                 type: string
 *                 format: binary
 *                 description: The student data in JSON format.
 *     responses:
 *       200:
 *         description: Student created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A message indicating the success of the creation operation.
 *                 data:
 *                   type: object
 *                   description: The created student data.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.post(
  "/student/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_STUDENT,
    ROLE_PARENT,
    VSC,
  ]),
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const studentData = fields.studentData
          ? JSON.parse(fields.studentData)
          : null;

        //check if studentData is not null
        if (!studentData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student data is required to create student.`,
            `Please provide a valid student data.`
          );
          return res.status(500).json(response);
        }

        //check if student exists
        /* const { status } =
        await userHelper.ifUserExistOnCreateWithSameNameLastName(
          userRolesConstants.ROLE_STUDENT,
          studentData.contactDetails.firstName,
          studentData.contactDetails.lastName
        );

      if (!status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The student already exists with the same name and last name. Please check the list of Students.`,
          apiResponse.apiConstants.USER_EXIST_WITH_SAME_NAME
        );
        return res.status(200).json(response);
      } */

        //check if student exists with same email address
        const email = studentData.contactDetails.email;
        const phone = studentData.contactDetails.phoneNumber;
        const ifExistsWithEmail =
          await userHelper.checkIfStudentEmailIsAlreadyRegistered(email);
        const ifExistsWithPhone =
          await userHelper.checkIfStudentPhoneAlreadyRegistered(phone);
        if (
          (ifExistsWithEmail.status && !ifExistsWithEmail.proceed) ||
          ifExistsWithPhone.status
        ) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Email or phone already registered`,
            `Please provide another email/Phone address`,
            apiResponse.apiConstants.EMAIL_IS_ALREADY_REGISTERED
          );
          return res.status(500).json(response);
        }

        const studentDocument = await studentHelper.createStudentIS(
          studentData
        );

        if (studentDocument.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student was created successfully.`,
            studentDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student was not created.`,
            studentDocument.message,
            apiResponse.apiConstants.DATA_NOT_SAVED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error creating student`,
          error,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(500).json(response);
      }
    });
  }
);

//update student
/**
 * @swagger
 * /api/v1/student/is:
 *   put:
 *     summary: Update Student
 *     description: Update an existing student.
 *     tags: [Student]
 *     requestBody:
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               studentData:
 *                 type: string
 *                 format: binary
 *                 description: The student data in JSON format.
 *     responses:
 *       200:
 *         description: Student updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A message indicating the success of the update operation.
 *                 data:
 *                   type: object
 *                   description: The updated student data.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.put(
  "/student/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_STUDENT,
    ROLE_PARENT,
    VSC,
  ]),
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const studentData = fields.studentData
          ? JSON.parse(fields.studentData)
          : null;
        //check if studentData is not null
        if (!studentData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student data is required to update student.`,
            `Please provide a valid student data.`
          );
          return res.status(500).json(response);
        }

        const studentDocument = await studentHelper.updateStudentIS(
          studentData
        );
        if (studentDocument.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student was updated successfully.`,
            studentDocument.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `The student was not updated.`,
            studentDocument.message,
            apiResponse.apiConstants.DATA_NOT_SAVED
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating student`,
          error,
          apiResponse.apiConstants.DATA_NOT_SAVED
        );
        return res.status(500).json(response);
      }
    });
  }
);
router.patch(
  "/student/update/simple",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_TUTOR,
    VSC,
  ]),
  async (req, res) => {
    try {
      const studentData = req.body; // Extract fields directly from JSON body
      const connectedUser = req.body.userId;
      // Ensure at least one field is provided for update
      if (!studentData || Object.keys(studentData).length === 0) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `No update fields provided.`,
          `Please provide at least one valid field to update.`
        );
        return res.status(400).json(response);
      }
      console.log("connectedUser===>", connectedUser);
      console.log("studentData===>", studentData);
      // Perform partial update
      const updateResult = await StudentPreferencesModel.findOneAndUpdate(
        { userId: connectedUser },
        {
          $set: {
            comments: studentData.comment,
            situation: studentData.situation,
            suiviCoordo: studentData.suiviCoordo,
            updatedAt: Date.now(),
            "LastUpdatedBy.userId": studentData.UpdaterUserId,
            "LastUpdatedBy.firstName": studentData.updaterFirstName,
            "LastUpdatedBy.lastName": studentData.updaterLastName,
            "LastUpdatedBy.email": studentData.updaterEmail,
          },
        }
      );
      console.log("updateResult===>", updateResult);
      if (updateResult.contactDetails) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Student updated successfully`,
          updateResult.data,
          apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
        );
        return res.status(200).json(response);
      } else {
        console.error("Error updating student data", updateResult);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating student data`,
          updateResult.data,
          apiResponse.apiConstants.DATA_NOT_UPDATED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.error("Error on updating student data", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error on updating tutor data`,
        error.message,
        apiResponse.apiConstants.DATA_NOT_UPDATED
      );
      return res.status(500).json(response);
    }
  }
);
//get list of all students
/**
 * @swagger
 * /api/v1/student/is/dashboard:
 *   get:
 *     summary: Get Student Dashboard
 *     description: Retrieve the list of students for the [IS] dashboard.
 *     tags: [Student]
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         description: The number of students per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: The page number.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for the students.
 *       - in: query
 *         name: exportCsv
 *         schema:
 *           type: string
 *           enum: [true, false]
 *         description: Indicates whether to export the data as CSV format.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort the results by.
 *     responses:
 *       200:
 *         description: List of students retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A message indicating the success of the retrieval operation.
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                 page:
 *                   type: integer
 *                   description: The current page number.
 *                 pageSize:
 *                   type: integer
 *                   description: The number of students per page.
 *                 totalNumberOfStudents:
 *                   type: integer
 *                   description: The total number of students.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.get(
  "/student/is/dashboard",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  async (req, res) => {
    try {
      let pageSize = req.query.pageSize;
      let page = req.query.page;
      const filter = req.query.filter;
      const exportCsv = req.query.exportCsv === "true";
      const sortBy = req.query.sortBy;
      //if exportCsv is true, then return all parents paginated and page size 1000000000
      const userId = req.userId;
      const userRole = req.userRole;
      if (exportCsv) {
        page = 1;
        pageSize = 1000000000;
      }
      const listOfStudents = await studentHelper.getISStudentDashboard({
        page,
        pageSize,
        filter,
        sortBy,
        userId,
        userRole,
      });
      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of students on the [IS] dashboard was retrieved successfully.`,
        listOfStudents.data,
        listOfStudents.page,
        listOfStudents.pageSize,
        listOfStudents.totalNumberOfStudents
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving student list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//get user data per Student, based on userId. Combine data from user data and student preferences
/**
 * @swagger
 * /api/v1/student/is:
 *   get:
 *     summary: Get All Dashboard Student Data
 *     description: Retrieve the data of a student.
 *     tags: [Student]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the student.
 *     responses:
 *       200:
 *         description: Student data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               data:
 *                type: object
 *               properties:
 *                userId:
 *                 type: string
 *                 description: The unique identifier of the student.
 *                firstName:
 *                 type: string
 *                 description: The first name of the student.
 *                lastName:
 *                 type: string
 *                 description: The last name of the student.
 *                email:
 *                 type: string
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/student/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([
    SUPER_ADMINISTRATOR,
    COORDINATOR,
    MANAGER,
    ROLE_STUDENT,
    ROLE_PARENT,
    VSC,
  ]),
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const userDocument = await Users.findOne({ userId: userId });
      const studentUserDocument = await StudentPreferencesModel.findOne({
        userId: userId,
      });

      //check if studentUserDocument is not null
      if (!studentUserDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The student data was not found.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }
      const studentUserPreferencesDocument =
        await studentHelper.getStudentPreferences(userId);
      //check if student has already Tutor
      const tutorsOfStudent = await sessionStudentHelper.checkIfStudentHasTutor(
        userId
      );
      //combine data from studentUserDocument and studentUserPreferencesDocument
      const studentData = {
        ...studentUserDocument._doc,
        contactDetails: userDocument.contactDetails,
        firebaseIds: userDocument.firebaseIds.length,
        studentPreferences: studentUserPreferencesDocument.data,
        tutorsOfStudent: tutorsOfStudent.data,
        dateOfBirth: userDocument.dateOfBirth,
        address: userDocument.address,
      };
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The student data was retrieved successfully.`,
        studentData
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving student data`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//delete student by userId
/**
 * @swagger
 * /api/v1/student/is:
 *   delete:
 *     summary: Delete a student by userId
 *     description: Delete a student record by providing the userId.
 *     tags: [Students]
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user to delete.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Successfully deleted the student.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "The student was deleted successfully."
 *                 data:
 *                   type: object
 *                   example: {}
 *       '500':
 *         description: Failed to delete the student.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error deleting student"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 *                 data:
 *                   type: string
 *                   example: "DATA_NOT_DELETED"
 */
router.delete(
  "/student/is",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is not null
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The userId is required to delete student.`,
          `Please provide a valid userId.`
        );
        return res.status(500).json(response);
      }

      const deleteDocument = await studentHelper.deleteStudentIS(userId);

      if (deleteDocument.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The student was deleted successfully.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_DELETED
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `The student was not deleted.`,
          deleteDocument.message,
          apiResponse.apiConstants.DATA_NOT_DELETED
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting student`,
        error,
        apiResponse.apiConstants.DATA_NOT_DELETED
      );
      return res.status(500).json(response);
    }
  }
);

//#endregion

//accept invitation
/**
 * @swagger
 * /api/v1/externalUser/acceptInvitation:
 *   post:
 *     summary: Accept invitation for external user
 *     description: Accept invitation for an external user by providing invitation code and Firebase identifier.
 *     tags: [External Users]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               invitationCode:
 *                 type: string
 *               firebaseIdentifier:
 *                 type: string
 *     responses:
 *       '200':
 *         description: Invitation accepted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Invitation is accepted successfully"
 *                 data:
 *                   type: object
 *                   example: {}
 *       '400':
 *         description: Invalid invitation code or error while accepting invitation.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error while updating invitation code: XYZ123"
 *                 error:
 *                   type: string
 *                   example: "Bad Request"
 *                 data:
 *                   type: string
 *                   example: "ERROR_WHILE_ACCEPTING_INVITATION"
 */
router.post(
  "/externalUser/acceptInvitation",
  verifyApiKey,
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      const { invitationCode, firebaseIdentifier } = fields;

      //check if invitationCode is present in the request
      if (!invitationCode) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is null: ${invitationCode}`,
          `Invitation code is required, please provide a valid invitation code`
        );
        return res.status(200).json(response);
      }

      //check if firebaseIdentifier is present in the request
      if (!firebaseIdentifier) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase identifier is null: ${firebaseIdentifier}`,
          `Firebase identifier is required, please provide a valid firebase identifier`
        );
        return res.status(200).json(response);
      }

      //check if invitation code is valid
      let invitationCodeIsValid = await userHelper.checkInvitationCodeValidity(
        invitationCode,
        firebaseIdentifier
      );

      if (!invitationCodeIsValid.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is invalid: ${invitationCode}`,
          `Invitation code is invalid, please provide a valid invitation code`
        );
        return res.status(400).json(response);
      }
      const userDocument = await userHelper.getUserDetailsFromEmail(
        firebaseIdentifier
      );

      const userRole = userDocument.userRole;
      let updateInvitation = { status: false, message: "" };
      if (userRole === userRolesConstants.ROLE_PARENT) {
        updateInvitation = await parentHelper.updateUserAdminWithInvitationCode(
          userDocument.userId,
          invitationCode,
          true
        );
      } else if (userRole === userRolesConstants.ROLE_TUTOR) {
        updateInvitation = await tutorHelper.updateUserAdminWithInvitationCode(
          userDocument.userId,
          invitationCode,
          true
        );
      }

      if (!updateInvitation.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating invitation code: ${invitationCode}`,
          `Error while updating invitation code, please try again`,
          apiResponse.apiConstants.ERROR_WHILE_ACCEPTING_INVITATION
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invitation is accepted successfully`,
        `Now you can register as a user and start using the app`,
        apiResponse.apiConstants.ADMIN_ACCEPT_INVITATION_SUCCESS
      );
      return res.status(200).json(response);
    });
  }
);

//register userAdministration account
/**
 * @swagger
 * /api/v1/externalUser/register:
 *   post:
 *     summary: Register an external user
 *     description: Register an external user by providing necessary details.
 *     tags: [External Users]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               firebaseIdentifier:
 *                 type: string
 *               firebaseUserId:
 *                 type: string
 *               provider:
 *                 type: string
 *               profilePic:
 *                 type: string
 *               userId:
 *                 type: string
 *     responses:
 *       '200':
 *         description: User registered successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "User is registered successfully"
 *                 data:
 *                   type: object
 *                   example: {}
 *       '400':
 *         description: Invalid request or error while registering user.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Provider is null: google"
 *                 error:
 *                   type: string
 *                   example: "Bad Request"
 *                 data:
 *                   type: string
 *                   example: "ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error while registering userAdministration account"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 *                 data:
 *                   type: string
 *                   example: "ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT"
 */
router.post("/externalUser/register", verifyApiKey, async (req, res) => {
  const form = new formidable.IncomingForm();
  form.parse(req, async (err, fields, files) => {
    try {
      const {
        firebaseIdentifier,
        firebaseUserId,
        provider,
        profilePic,
        userId,
      } = fields;

      //check if firebaseIdentifier is present in the request
      if (!firebaseIdentifier) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase identifier is null: ${firebaseIdentifier}`,
          `Firebase identifier is required, please provide a valid firebase identifier`
        );
        return res.status(200).json(response);
      }

      //check if firebaseUserId is present in the request
      if (!firebaseUserId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase user id is null: ${firebaseUserId}`,
          `Firebase user id is required, please provide a valid firebase user id`
        );
        return res.status(200).json(response);
      }

      //check if provider is present in the request
      if (!provider) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Provider is null: ${provider}`,
          `Provider is required, please provide a valid provider`
        );
        return res.status(400).json(response);
      }

      const newFirebaseIds = {
        firebaseUserId,
        identifier: firebaseIdentifier,
        provider,
      };

      const updateFirebaseIdsAndProfilePic =
        await userHelper.updateUserAdministrationFirebaseIdsAndProfilePicture(
          userId,
          newFirebaseIds,
          profilePic
        );

      if (!updateFirebaseIdsAndProfilePic.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while registering userAdministration account: ${firebaseIdentifier}`,
          `Error while registering userAdministration account, please try again`,
          apiResponse.apiConstants
            .ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT
        );
        return res.status(200).json(response);
      }
      const token = generateAccessToken(
        updateFirebaseIdsAndProfilePic.data.userId
      );
      const refreshToken = generateRefreshToken(
        updateFirebaseIdsAndProfilePic.data.userId
      );

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `User is registered successfully`,
        { userData: updateFirebaseIdsAndProfilePic.data, token },
        apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
      );
      res.cookie("refreshToken", refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        path: "/",
      });
      return res.status(200).json(response);
    } catch (error) {
      console.log("Error while registering userAdministration account", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while registering userAdministration account`,
        error,
        apiResponse.apiConstants
          .ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT
      );
      return res.status(500).json(response);
    }
  });
});

/**
 * @swagger
 * /api/v1/tutor/is/stats:
 *   get:
 *     summary: Obtenir les statistiques des tuteurs
 *     description: Récupère les statistiques agrégées sur les tuteurs (total, complets, incomplets, par étape, par statut)
 *     tags: [Tutor]
 *     parameters:
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filtres à appliquer aux statistiques
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 *       500:
 *         description: Erreur lors de la récupération des statistiques
 */
router.get(
  "/tutor/is/stats",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  //
  async (req, res) => {
    try {
      const userRole = req.userRole;
      const filter = req.query.filter;
      const userId = req.userId;

      const tutorStats = await tutorHelper.getTutorStatistics({
        filter,
        userId,
        userRole,
      });

      if (!tutorStats.status) {
        return res.status(400).json(
          apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_ERROR,
            tutorStats.message
          )
        );
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Les statistiques des tuteurs ont été récupérées avec succès",
        tutorStats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        "Erreur lors de la récupération des statistiques des tuteurs",
        error
      );
      return res.status(500).json(response);
    }
  }
);


// Get last login date from Firebase using email
/**
 * @swagger
 * /api/v1/user/last-login:
 *   get:
 *     summary: Récupérer la date de dernière connexion d'un utilisateur
 *     description: Récupère la date de dernière connexion d'un utilisateur depuis Firebase en utilisant son email.
 *     tags: [User]
 *     parameters:
 *       - in: query
 *         name: email
 *         schema:
 *           type: string
 *         required: true
 *         description: L'email de l'utilisateur.
 *     responses:
 *       200:
 *         description: Date de dernière connexion récupérée avec succès.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Date de dernière connexion récupérée avec succès"
 *                 data:
 *                   type: object
 *                   properties:
 *                     lastLoginDate:
 *                       type: string
 *                       format: date-time
 *                       description: La date de dernière connexion de l'utilisateur.
 *       400:
 *         description: Requête invalide ou utilisateur non trouvé.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Utilisateur non trouvé avec cet email"
 *       500:
 *         description: Erreur interne du serveur.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Erreur lors de la récupération de la date de dernière connexion"
 */
router.get(
  "/user/last-login",
  verifyApiKey,
  checkIfAuthenticated,
  checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  async (req, res) => {
    try {
      const email = req.query.email;
      const phoneNumber = req.query.phoneNumber;

      if (!email && !phoneNumber) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `L'email ou le numéro de téléphone est requis pour récupérer la date de dernière connexion.`,
          `Veuillez fournir un email ou un numéro de téléphone valide.`
        );
        return res.status(400).json(response);
      }

      const user = email ? await admin.auth().getUserByEmail(email) : await admin.auth().getUserByPhoneNumber(phoneNumber);

      if (!user) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Utilisateur non trouvé avec cet email: ${email}`,
          `Veuillez fournir un email valide.`
        );
        return res.status(400).json(response);
      }

      const lastLoginDate = user.metadata.lastSignInTime;
      
      // Formater la date en français selon le format demandé (5 Novembre 2024)
      const date = new Date(lastLoginDate);
      const options = { day: 'numeric', month: 'long', year: 'numeric' };
      const formattedDate = date.toLocaleDateString('fr-FR', options);

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Date de dernière connexion récupérée avec succès`,
        { lastLoginDate: formattedDate }
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Erreur lors de la récupération de la date de dernière connexion`,
        error.message
      );
      return res.status(500).json(response);
    }
  }
);
module.exports = router;
