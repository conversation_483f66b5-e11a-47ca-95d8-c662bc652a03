const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("apiLogger.js");

//import axios
const axios = require("axios");

//import xml-js
const xmlJs = require("xml-js");

let _ = require("lodash");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import dotenv

const initBigBlueButton = require("../middleware/bigbluebutton/Init.BigBlueButton.js");

//import BigBlueButton.helper.js
const bigBlueButtonHelper = require("../controllers/bigBlueButton/bigBlueButton.Helper.js");

//import xmlToJson Helpers from utils
const xmlToJsonHelper = require("../utils/xmlToJson/xmlToJson.helper.js");

const bigBlueButtonConstants = require("../utils/constants/bigbluebutton.constans.js");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");


//Get meeting info
/**
 * @swagger
 * /api/v1/bigBlueButton/getMeetingInfo:
 *   get:
 *     summary: Get BigBlueButton Meeting Info
 *     description: Retrieve information about a BigBlueButton meeting using the provided meeting ID.
 *     tags: [BigBlueButton]
 *     parameters:
 *       - in: query
 *         name: meetingId
 *         description: The ID of the meeting.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Meeting information retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   description: The meeting information.
 *                   example: {}
 *       '500':
 *         description: Error occurred while retrieving meeting information or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error occurred while retrieving meeting information"
 */
router.get("/bigBlueButton/getMeetingInfo", async (req, res) => {
  //get meeting info
  let metingId = req.query.meetingId;

  let objectParam = {
    meetingID: metingId,
    password: "mp",
  };

  const objectParamString = bigBlueButtonHelper.getStringFromObjectParams(
    objectParam,
    "getMeetingInfo"
  );

  let checksum = initBigBlueButton.generateChecksum(objectParamString);

  let queryString = Object.keys(objectParam)
    .map((key) => key + "=" + objectParam[key])
    .join("&");

  //setup url
  let url =
    process.env.BIGBLUEBUTTON_URL +
    "getMeetingInfo?" +
    queryString +
    "&checksum=" +
    checksum;
  console.log(url);

  //get meeting info
  axios
    .get(url)
    .then((response) => {
      //check if response is xml
      if (response.headers["content-type"] === "text/xml;charset=utf-8") {
        let dataJson = xmlToJsonHelper.xmlToJson(response.data);

        return res.status(200).json({
          status: "success",
          data: dataJson,
        });
      } else {
        return res.status(200).json({
          status: "success",
          data: response.data,
        });
      }
    })
    .catch((error) => {
      console.log("error: ", error);
      res.send(error);
    });
});

//get list of meetings
/**
 * @swagger
 * /api/v1/bigBlueButton/getMeetings:
 *   get:
 *     summary: Get BigBlueButton Meetings
 *     description: Retrieve information about all active BigBlueButton meetings.
 *     tags: [BigBlueButton]
 *     responses:
 *       '200':
 *         description: Meeting information retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: object
 *                   description: The information about all active meetings.
 *                   example: {}
 *       '500':
 *         description: Error occurred while retrieving meeting information or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error occurred while retrieving meeting information"
 */
router.get("/bigBlueButton/getMeetings", async (req, res) => {
  const meetingId = req.query.meetingId;
  const bigBlueButtonMethod =
    bigBlueButtonConstants.API_METHOD_NAME_GET_MEETINGS;

  let fullUrl = bigBlueButtonHelper.getFullUrl(null, bigBlueButtonMethod);

  console.log("fullUrl: ", fullUrl);

  //get meeting info
  axios
    .get(fullUrl)
    .then((response) => {
      //check if response is xml
      if (response.headers["content-type"] === "text/xml;charset=utf-8") {
        let dataJson = xmlToJsonHelper.xmlToJson(response.data);

        return res.status(200).json({
          status: "success",
          data: dataJson,
        });
      } else {
        return res.status(200).json({
          status: "success",
          data: response.data,
        });
      }
    })
    .catch((error) => {
      console.log("error: ", error);
    });
});

//create meeting room
/**
 * @swagger
 * /api/v1/bigBlueButton/newRoom:
 *   post:
 *     summary: Create a new BigBlueButton room
 *     description: Create a new room in BigBlueButton with the specified room name and welcome message.
 *     tags: [BigBlueButton]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               roomName:
 *                 type: string
 *                 description: The name of the new room.
 *               welcomeMessage:
 *                 type: string
 *                 description: The welcome message for the new room.
 *     responses:
 *       '200':
 *         description: New room created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Create new room MyRoom successfully"
 *                 data:
 *                   type: object
 *                   description: The details of the newly created room.
 *                   example: {}
 *       '500':
 *         description: Error occurred while creating a new room or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "New room creation failed"
 */
router.post("/bigBlueButton/newRoom", async (req, res) => {
  const form = formidable({ multiples: true });

  form.parse(req, async (err, fields, files) => {
    try {
      const { roomName, welcomeMessage } = fields;
      console.log("roomName: ", roomName);
      console.log("welcomeMessage: ", welcomeMessage);
      //Create new Room object
      const newRoom = await bigBlueButtonHelper.createNewRoomInMongoDB(
        roomName,
        welcomeMessage
      );
      const _id = newRoom._doc._id;
      const createdAt = newRoom._doc.createdAt;

      //get newRoom object for request
      let newRoomObjectForRequest = Object.assign({}, newRoom);
      delete newRoomObjectForRequest._doc._id;
      delete newRoomObjectForRequest._doc.createdAt;

      newRoom._id = _id;
      newRoom.createdAt = createdAt;

      const bigBlueButtonMethod = bigBlueButtonConstants.API_METHOD_NAME_CREATE;

      //get full url
      let fullUrl = bigBlueButtonHelper.getFullUrl(
        newRoomObjectForRequest._doc,
        bigBlueButtonMethod
      );

      //get meeting info
      axios
        .get(fullUrl)
        .then(async (response) => {
          //check if response is xml
          if (response.headers["content-type"] === "text/xml;charset=utf-8") {
            let dataJson = xmlToJsonHelper.xmlToJson(response.data);

            console.log("dataJson: ", dataJson.response);

            newRoom.internalMeetingID = dataJson.response.internalMeetingID;

            //save new room to database
            let newRoomDoc = await bigBlueButtonHelper.saveNewRoom(newRoom);

            let apiData = apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_SUCCESS,
              `Create new room ${roomName} successfully`,
              newRoomDoc
            );
            return res.status(200).json(apiData);
          } else {
            let responseData = response.data;
            newRoom.internalMeetingID = responseData.internalMeetingID;

            //save new room to database
            let newRoomDoc = await bigBlueButtonHelper.saveNewRoom(newRoom);
            let apiData = apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_SUCCESS,
              `Create new room ${roomName} successfully`,
              newRoomDoc
            );
            return res.status(200).json(apiData);
          }
        })
        .catch((error) => {
          console.log("error: ", error.message);
          let apiData = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `New room ${roomName} failed`,
            error.message
          );
          return res.status(200).json(apiData);
        });
    } catch (error) {
      console.log("error: ", error);
    }
  });
});

//get joinUrl for room , joinUrl is used to join room
/**
 * @swagger
 * /api/v1/bigBlueButton/joinUrl:
 *   get:
 *     summary: Get join URL for a BigBlueButton session
 *     description: Get the join URL for the specified BigBlueButton session and user ID.
 *     tags: [BigBlueButton]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         required: true
 *         description: The ID of the BigBlueButton session.
 *         schema:
 *           type: string
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Join URL retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Get joinUrl for room MyRoom successfully"
 *                 data:
 *                   type: string
 *                   description: The join URL for the specified session and user.
 *                   example: "https://example.com/join?meetingId=12345&userId=67890"
 *       '500':
 *         description: Error occurred while retrieving the join URL or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Join room MyRoom failed"
 */
router.get("/bigBlueButton/joinUrl", async (req, res) => {
  const sessionId = req.query.sessionId;

  const userId = req.query.userId;

  //check if user is logged in
  if (!userId) {
    let apiData = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "UserId is required",
      "Please login to get joinUrl"
    );
    return res.status(200).json(apiData);
  }

  //check if sessionId is valid
  if (!sessionId) {
    let apiData = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "SessionId is required",
      "Please provide sessionId to get joinUrl"
    );
    return res.status(200).json(apiData);
  }
  try {
    //get room info from mongodb
    let roomInfo = await bigBlueButtonHelper.getRoomByMeetingID(roomID);

    //get userDetail from mongodb
    const userDocument = await userHelper.getUserDetailsByUserId(userId);
    const fullName =
      userDocument.contactDetails.firstName +
      " " +
      userDocument.contactDetails.lastName;

    //get joinUrl
    let joinUrl = bigBlueButtonHelper.generateJoinURL(roomInfo, userDocument);
    console.log("joinUrl: ", joinUrl);
    let apiData = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `Get joinUrl for room ${roomID} successfully`,
      joinUrl
    );
    return res.status(200).json(apiData);
  } catch (error) {
    console.log("error: ", error);
    let apiData = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Join room ${roomID} failed`,
      error.message
    );
    return res.status(200).json(apiData);
  }
});

//get Recordings
/**
 * @swagger
 * /api/v1/bigBlueButton/getRecordings:
 *   get:
 *     summary: Get recordings for a BigBlueButton meeting
 *     description: Get the recordings for the specified BigBlueButton meeting.
 *     tags: [BigBlueButton]
 *     parameters:
 *       - in: query
 *         name: meetingId
 *         required: true
 *         description: The ID of the BigBlueButton meeting.
 *         schema:
 *           type: string
 *       - in: query
 *         name: internalMeetingID
 *         required: true
 *         description: The internal meeting ID of the BigBlueButton meeting.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Recordings retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 data:
 *                   type: array
 *                   description: An array containing the recordings for the specified meeting.
 *                   items:
 *                     type: object
 *                     properties:
 *                       recordingId:
 *                         type: string
 *                         description: The ID of the recording.
 *                         example: "12345"
 *                       recordingName:
 *                         type: string
 *                         description: The name of the recording.
 *                         example: "Recording 1"
 *                       startTime:
 *                         type: string
 *                         description: The start time of the recording.
 *                         example: "2024-05-30T12:00:00Z"
 *                       endTime:
 *                         type: string
 *                         description: The end time of the recording.
 *                         example: "2024-05-30T13:00:00Z"
 *       '500':
 *         description: Error occurred while retrieving the recordings or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Error occurred while retrieving recordings"
 */
router.get("/bigBlueButton/getRecordings", async (req, res) => {
  const meetingId = req.query.meetingId;
  const internalMeetingID = req.query.internalMeetingID;

  const bigBlueButtonMethod =
    bigBlueButtonConstants.API_METHOD_NAME_GET_RECORDINGS;

  const record = await bigBlueButtonHelper.getRecordings(
    meetingId,
    internalMeetingID
  );
  return res.status(200).json(record);
});

//export router
module.exports = router;
