//bigBlueButton Constants
const bigBlueButtonConstants = {
     //initialize room name two characters
     TWO_CHARACTERS_ROOM_NAME: "az",
     //default attendee password
     DEFAULT_ATTENDEE_PASSWORD: "aa-ap",
     //default moderator password
     DEFAULT_MODERATOR_PASSWORD: "aa-mp",

     //bigBlueButton api methods name : create
     API_METHOD_NAME_CREATE: "create",

     //get getMeetings
     API_METHOD_NAME_GET_MEETINGS: "getMeetings",
     API_METHOD_NAME_GET_RECORDINGS: "getRecordings",
     API_METHOD_NAME_GET_RECORDING: "getRecording",
     API_METHOD_NAME_PUBLISH_RECORDING: "publishRecordings",
     API_METHOD_NAME_DELETE_RECORDING: "deleteRecordings",
     API_METHOD_NAME_END_MEETING: "end",
     API_METHOD_NAME_GET_MEETING_INFO: "getMeetingInfo",
     API_METHOD_NAME_IS_MEETING_RUNNING: "isMeetingRunning",
     API_METHOD_NAME_JOIN: "join",
     API_METHOD_NAME_CREATE_WEBHOOKS: "hooks/create",
     API_METHOD_NAME_GET_HOOKS_LIST: "hooks/list",
     API_METHOD_NAME_DELETE_WEBHOOKS: "hooks/destroy",

     //Webhooks constants
     WEBHOOKS_MEETING_CREATED: "meeting_created",
     WEBHOOKS_CREATED_DUPLICATE: "duplicateWarning",
};

//export bigBlueButtonConstants
module.exports = bigBlueButtonConstants;
