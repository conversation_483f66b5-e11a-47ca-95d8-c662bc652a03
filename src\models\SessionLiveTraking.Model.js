const mongoose = require('mongoose');

const SessionLiveTrackingSchema = new mongoose.Schema({
  idSession: {
    type: String,
    ref: 'sessions',
    required: true,
    unique: true
  },
  idBinome :  {
    type: String,
    ref: 'binomes',
    required: true,
    unique: true
  },
  liveData: {
    learnerHourIn: { type: String },
    learnerHourOut: { type: String },
    mentorHourIn: { type: String },
    mentorHourOut: { type: String },
    learnerStatus: {
      type: String,
      enum: ['présent', 'absent']
    },
    mentorStatus: {
      type: String,
      enum: ['présent', 'absent']
    }
  },

  closeData: {
    startTime: { type: String },
    endTime: { type: String },
    durationSession: { type: String },
    totalConnectionTimeLearner: { type: String },
    totalConnectionTimeMentor: { type: String },
    statutPresenceLearner: {
      type: String,
      enum: ['présent', 'absent']
    },
    statutPresenceMentor: {
      type: String,
      enum: ['présent', 'absent']
    },
    seancestatut: { type: String }
  }

}, {
  timestamps: true
});

module.exports = mongoose.model('SessionLiveTracking', SessionLiveTrackingSchema);
