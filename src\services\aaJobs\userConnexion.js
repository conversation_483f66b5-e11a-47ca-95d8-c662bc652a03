const cron = require("node-cron");
const firebaseHelper = require("../../controllers/firebase/firebase.helper.js")
const UserModels = require("../../models/User.Models.js");
// Fonction pour récupérer la date de dernière connexion d’un utilisateur

// Fonction principale du cron job
const updateLastLoginDates = async () => {
  console.log("🚀 [Cron Job] Vérification des connexions Firebase...");

  try {
    // Étape 1 : Récupérer les utilisateurs avec firebaseIdentifier
    const users = await UserModels.find({ "firebaseIds.identifier": { $exists: true }
        //  userId : {$in : ["zu-b8f7ce02-6fc5-45eb-a8bd-1ef9bba5ef3d", "zu-7240c3ae-a190-486b-94dd-f9ac3acf1371"]}
         })
      .select("userId firebaseIds")
      .lean();

    if (users.length === 0) {
      console.log("Aucun utilisateur avec Firebase Identifier trouvé.");
      return;
    }

    // Étape 2 : Récupérer la dernière connexion Firebase pour chaque utilisateur
    const updates = [];
    for (const user of users) {
      const firebaseUid = user.firebaseIds[0].identifier; // Récupération de l'UID Firebase
      const lastLoginDate = await firebaseHelper.getUserByEmailOrPhone(firebaseUid);
      if (lastLoginDate && lastLoginDate?.data?.metadata?.lastSignInTime && lastLoginDate?.data?.metadata?.lastSignInTime && !isNaN(Date.parse(lastLoginDate?.data?.metadata?.lastSignInTime))) {
        updates.push({
          userId: user.userId,
        //   firebaseUid,
          lastLogin:  new Date(lastLoginDate?.data?.metadata?.lastSignInTime) ,
        });
      }
    }

    // Étape 3 : Insérer les données mises à jour dans MongoDB
    if (updates.length > 0) {
      for (const update of updates) {
        await UserModels.updateOne({ userId: update.userId }, { $set: { lastLogin: update.lastLogin } });
      }
      console.log(`✅ ${updates.length} dates de connexion mises à jour.`);
    } else {
      console.log("❌ Aucune mise à jour à insérer.");
    }
  } catch (error) {
    console.error("Erreur lors de la mise à jour des connexions :", error);
  }
};

// **Planifier le job pour s'exécuter chaque jour à minuit (UTC)**
cron.schedule("0 1 * * *", async () => {
    console.log("⏳ [Cron Job] Démarrage du traitement...");
    await updateLastLoginDates();
}, {
    timezone: "Europe/Paris"
});
module.exports = {
    updateLastLoginDates,
  };