//init firebase
const initFirebase = require("../../middleware/firebase/Init.Firebase.js");

const admin = require("firebase-admin");
const { getAuth, updateEmail } = require("firebase/auth");

require("dotenv").config();

//function to reset email of user
exports.resetEmail = async (userId, email) => {
  try {
    const userRecord = await admin.auth().getUserByEmail(userId);

    const response = await admin.auth().updateUser(userRecord.uid, {
      email: email,
    });

    return response;
  } catch (error) {
    console.log(error.code);
    if (error.code === "auth/user-not-found") {
      return { status: 404, message: "user not found" };
    }
    return error;
  }
};

//function to reset password of user
exports.resetPassword = async (userId, email) => {
  const auth = await initFirebase.getAuth();
  const actionCodeSettings = {
    url:
      "https://zupdeco-web-dev-ah77n.ondigitalocean.app/user/?email=" + email,
  };

  try {
    const response = await auth.generatePasswordResetLink(
      email,
      actionCodeSettings
    );
    return response;
  } catch (error) {
    console.log(error);
    return error;
  }
};

exports.changePassword = async (userId, newPassword) => {
  try {
    const userRecord = await admin.auth().getUserByEmail(userId);
    const response = await admin.auth().updateUser(userRecord.uid, {
      password: newPassword,
    });
    return response;
  } catch (error) {
    console.log(error.code);
    return error;
  }
};

//function  email verification
exports.emailVerificationLink = async (actionUrl, email) => {
  const actionCodeSettings = {
    url: actionUrl,
    handleCodeInApp: true,
  };
  try {
    const link = await admin
      .auth()
      .generateEmailVerificationLink(email, actionCodeSettings);

    return link;
  } catch (error) {
    console.log(error);
    return error;
  }
};

//SignUp a user with email and password
exports.signUpWithEmailPassword = async (email, password) => {
  try {
    const userCredential = await admin.auth().createUser({
      email: email,
      password: password,
    });

    // Return the user UID or any other relevant information
    return {
      status: true,
      message: "User signed up successfully",
      data: userCredential,
    };
  } catch (error) {
    console.error("Error signing up user:", error);
    return { status: false, message: "Failed to sign up user", data: null };
  }
};

// Passwordless authentication with email link (Firebase)
exports.sendSignInLinkToEmail = async (email) => {
  const auth = await initFirebase.getAuth();
  const actionCodeSettings = {
    url:
      "https://zupdeco-web-dev-ah77n.ondigitalocean.app/user/?email=" + email,
    handleCodeInApp: true,
  };

  try {
    const response = await auth.sendSignInLinkToEmail(
      email,
      actionCodeSettings
    );
    return response;
  } catch (error) {
    console.log(error);
    return error;
  }
};

exports.generateVerificationCode = async (userEmail) => {
  try {
    var verificationCode = Math.floor(100000 + Math.random() * 900000);
    var response = await admin
      .auth()
      .getUserByEmail(userEmail)
      .then((userRecord) => {
        // Update the user's custom claims with the verification code
        return admin
          .auth()
          .setCustomUserClaims(userRecord.uid, { verificationCode });
      })
      .then(() => {
        // Send the verification code via email
        console.log("verification code:", verificationCode);
        return verificationCode;
      })
      .catch((error) => {
        console.log("Error generating verification code:", error);
      });
    return { status: true, data: response };
  } catch (error) {
    console.log(error);
    return { status: false, message: error, data: null };
  }
};
exports.updateFirebaseEmail = async (uid, newEmail) => {
  try {
    const response = await admin.auth().updateUser(uid, {
      email: newEmail,
    });

    return response;
  } catch (error) {
    console.log(error);
    return null;
  }
};
//get firebase user by email, @params: email or phone
exports.getUserByEmailOrPhone = async (emailOrPhone) => {
  try {
    //identify if email or phone
    const isEmail = emailOrPhone.includes("@");
    let response;
    if (isEmail) {
      await admin.auth().getUserByProviderUid;
      response = await admin.auth().getUserByEmail(emailOrPhone);
    } else {
      response = await admin.auth().getUserByPhoneNumber(emailOrPhone);
    }
    if (response) {
      return { status: true, data: response };
    } else {
      return { status: false, data: `User ${emailOrPhone} not found` };
    }
  } catch (error) {
    console.log(error);
    return { status: false, data: error };
  }
};

//delete user from firebase auth, @params: user
exports.deleteUserFromAuth = async (firebaseIdentifier) => {
  try {
    //get user data from firebase
    var userData = await this.getUserByEmailOrPhone(firebaseIdentifier);
    if (userData.status) {
      const userUid = userData.data.uid;
      await admin
        .auth()
        .deleteUser(userUid)
        .then(() => {
          console.log("Successfully deleted user");
          return { status: true, data: `User ${userUid} deleted` };
        })
        .catch((error) => {
          console.log("Error deleting user:", error);
          return { status: false, data: error };
        });
    } else {
      console.log(`User ${userData} not found`);
      return {
        status: false,
        data: `User with identifier ${userData} not found`,
      };
    }
  } catch (error) {
    console.log(`User  not deleted from firebase auth`);
    console.log(error);
    return { status: false, data: error };
  }
};
exports.checkEmailVerification = async (email) => {
  try {
    const userRecord = await admin.auth().getUserByEmail(email);
    return userRecord.emailVerified;
  } catch (error) {
    console.log(error);
    return false;
  }
};
