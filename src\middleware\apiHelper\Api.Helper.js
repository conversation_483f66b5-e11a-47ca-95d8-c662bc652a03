const axios = require("axios");
const xmlJs = require("xml-js");

exports.makeGetRequest = async (url) => {
  const response = await axios.get(url, {});

  let result;

  if (typeof response.data === "object") {
    result = response.data.response;
  } else {
    //convert xml to json
    let json = xmlJs.xml2json(response.data, { compact: true, spaces: 4 });
    const webhookData = JSON.parse(json);

    const data = webhookData.response;

    result = Object.keys(data).reduce((acc, key) => {
      acc[key] = data[key]._text || data[key];
      return acc;
    }, {});
  }
  return result;
};
