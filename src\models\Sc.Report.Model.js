const mongoose = require("mongoose");

const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const scReportSchema = new mongoose.Schema(
     {
          creation_date: Date, // For SC Reports only
          entry_date: Date,
          status: String, // For CRs and SC Reports
          postponed_hours: Number, // For SC Reports only
          vsc: {
               // For SC Reports only
               vscFullName: String,
               userId: String,
          },
          coordinator: {
               // For SC Reports only
               coordinatorFullName: String,
               userId: String,
          },
          proof_delivered: Boolean, // For SC Reports only
          appreciation: String,
          daysContent: String,
          createdAt: {
               type: Date,
               default: dateTimeHelper.getCurrentDateTimeInParisZone(),
          },
     },
     { versionKey: false }
);

module.exports = mongoose.model("scReport", scReportSchema);
