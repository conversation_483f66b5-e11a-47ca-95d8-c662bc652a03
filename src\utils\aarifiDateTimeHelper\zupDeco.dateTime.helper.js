const { DateTime } = require("luxon");

const moment = require("moment-timezone");

// Set Paris timezone
moment.tz.setDefault("Europe/Paris");

//get current year
exports.getCurrentYear = () => {
  return DateTime.local().year;
};

//get current month
exports.getCurrentMonth = () => {
  return DateTime.local().month;
};

//get current day
exports.getCurrentDay = () => {
  return DateTime.local().day;
};

//get current date and time timestamp -  in French format
exports.getCurrentDateTimeInParisZone = () => {
  const currentDateTime = moment()
    .tz("Europe/Paris")
    .format("YYYY-MM-DDTHH:mm:ssZ");
  return currentDateTime;
};

////get current date and time timestamp -  in French format from time 00:00:00
exports.getCurrentDateInParisZone = () => {
  return moment
    .tz("Europe/Paris")
    .startOf("day")
    .format("YYYY-MM-DDTHH:mm:ssZ");
};

exports.subtractDaysFromDate = (date, days) => {
  return moment(date).subtract(days, "days").format("YYYY-MM-DDTHH:mm:ssZ");
};

//get timestamp from String date -  in French format
exports.getDateTimeInParisZoneFromTimeStamp = (timestamp) => {
  //if timestamp not number convert it to number
  if (typeof timestamp !== "number") {
    timestamp = Number(timestamp);
  }
  // Create a new Moment.js object from the timestamp, and set the time zone to Europe/Paris
  const dateTime = moment.tz(timestamp, "Europe/Paris");
  console.log(
    "dateTime from TimeStamp",
    dateTime.format("YYYY/MM/DD HH:mm:ss Z")
  );
  return dateTime.format("DD/MM/YYYY HH:mm:ss Z");
};

//convert date string to full dateTime with moment
exports.convertStringDateToFullDateTime = (dateString) => {
  try {
    const date = moment.tz(dateString, "YYYY-MM-DDTHH:mm:ssZ", "Europe/Paris");
    return date;
  } catch (error) {
    console.log("error", error);
    return null;
  }
};

//convert date and time object to full dateTime with moment -  in French format
exports.convertDateAndTimeObjectToStringFormat = (dateObject) => {
  try {
    ///MMM D, YYYY, h:mm:ss
    const formateDate = moment
      .tz(dateObject, "Europe/Paris")
      .format("MMM D, YYYY, HH:mm");
    return formateDate;
  } catch (error) {
    console.log("error", error);
    return null;
  }
};

//get current date and time -  in French format  in timestamp
exports.getCurrentDateTimeInParisZoneInTimestamp = () => {
  // Get current date and time in Paris timezone as ISO string
  const parisDateTimeISO = DateTime.local().setZone("Europe/Paris").toISO();

  // Convert ISO string to Unix timestamp
  const parisDateTimeTimestamp = Date.parse(parisDateTimeISO);

  return parisDateTimeTimestamp;
};

//compare two  timestamps  startTimestamp and endTimestamp, return true if  endTimestamp is greater than startTimestamp. In Zone Europe/Paris
exports.ifEndTimeIsGreaterThanStartTime = (startTimestamp, endTimestamp) => {
  const startDateTime = DateTime.fromISO(startTimestamp);
  const endDateTime = DateTime.fromISO(endTimestamp);
  return endDateTime > startDateTime;
};

//compare three dateTimes commitmentEndDate, preemptiveEndDate and dateTimeNow,
//return true if commitmentEndDate or preemptiveEndDate is greater than dateTimeNow. In Zone Europe/Paris
//This function is used to check if the commitment end date or preemptive end date is greater than the current date for TUTOR
exports.isEndDateGreaterThanNow = (commitmentEndDate, preemptiveEndDate) => {
  const commitmentEndDateDateTime = DateTime.fromISO(commitmentEndDate);
  const preemptiveEndDateDateTime = DateTime.fromISO(preemptiveEndDate);
  const dateTimeNow = DateTime.local().setZone("Europe/Paris");
  return (
    commitmentEndDateDateTime > dateTimeNow ||
    preemptiveEndDateDateTime > dateTimeNow
  );
};

//check calendar range type and return the appropriate date
exports.getCalendarRangeType = (rangeType) => {
  if (rangeType === "year") {
    return this.getCurrentYear();
  } else if (rangeType === "month") {
    return this.getCurrentMonth();
  } else if (rangeType === "week") {
    return this.getCurrentDay();
  }
};

//get day of the week from date
exports.getDayOfTheWeekFromDate = (date) => {
  const startDate = moment(date);
  console.log("startDate", startDate);
  const dayOfTheWeek = startDate.day() === 0 ? 6 : startDate.day() - 1; // 0 to 6, starting from Monday
  console.log("dayOfTheWeek", dayOfTheWeek);
  return dayOfTheWeek;
};
