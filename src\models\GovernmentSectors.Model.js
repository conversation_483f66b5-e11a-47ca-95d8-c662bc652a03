const mongoose = require("mongoose");

const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const governmentSectorsSchema = new mongoose.Schema(
    {
        name: {
            type: String,
        },
        comments: String,
        schoolYear: {
            schoolYearName: String,
            schoolYearId: String,
        },
        schoolZone: {
            schoolZoneName: String,
            schoolZoneId: String,
        },
        coordinators: [
            {
                coordinatorName: String,
                userId: String,
            },
        ],
        createdAt: {
            type: Date,
            default: dateTimeHelper.getCurrentDateTimeInParisZone(),
        },
    },
    { versionKey: false }
);

module.exports = mongoose.model("govermentsector", governmentSectorsSchema);
