const parentPreferenceModel = require("../../models/Parent.Preferences.Model.js");

const userModel = require("../../models/User.Models.js");

const sessionModel = require("../../models/Sessions.Model.js");

const userHelper = require("../user/User.Helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const firebaseHelper = require("../firebase/firebase.helper.js");

const studentPreferenceModel = require("../../models/Student.Preferences.Model.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const UserModels = require("../../models/User.Models.js");
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const EducationAnnuaireModel = require("../../models/EducationAnnuaire.Model.js");
const filterHelper = require("../../utils/filter.js");
const { sessionsStatus } = require("../../utils/constants/sessions.constants.js");

//create new parent user
exports.createParentIS = async (parentUser) => {
  try {
    const userId = userHelper.generateNewUserId();

    const parentPreference = parentUser.parentPreference;
    //delete preference from parent user
    delete parentUser.parentPreference;

    const user = new userModel({
      userId: userId,
      userRole: userRoleConstants.ROLE_PARENT,
      hasChangedPassword: true,
      ...parentUser,
    });

    const parentUserDocument = await user.save();

    if (!parentUserDocument) {
      return { status: false, message: "Error in creating parent", data: null };
    }
    //create parent preferences
    const parentPreferenceDocument = new parentPreferenceModel({
      userId: userId,
      currentVideo: 1,
      ...parentPreference,
    });

    const parentPreferenceDocumentSaved = await parentPreferenceDocument.save();

    if (!parentPreferenceDocumentSaved) {
      return { status: false, message: "Error in creating parent", data: null };
    }
    const combineData = {
      ...parentUserDocument._doc,
      parentPreference: parentPreferenceDocumentSaved._doc,
    };
    return {
      status: true,
      message: "Parent created successfully",
      data: combineData,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in creating parent", data: null };
  }
};
// create preferenses parent
exports.createParentPreferences = async (
  userId,
  howDidYouHearAboutUs,
  department,
  socioProfessionalCategory,
  club,
  program,
  mentor
) => {
  // Création des préférences parent
  let parentPreferences = new parentPreferenceModel({
    userId: userId,
    howDidYouHearAboutUs: howDidYouHearAboutUs,
    department: department,
    socioProfessionalCategory: socioProfessionalCategory,
    club: club,
    currentVideo: 1,
    program: program,
    mentor: mentor,
  });

  try {
    // Sauvegarde des préférences
    await parentPreferences.save();
  } catch (error) {
    console.error("Erreur lors de la sauvegarde des préférences :", error);
    throw new Error("Unable to save user preferences");
  }
};
//update parent user
exports.updateParentIS = async (parentUser) => {
  try {
    const parentPreference = parentUser.parentPreference;
    //delete preference from parent user
    delete parentUser.parentPreference;

    const user = await userModel.findOne({ userId: parentUser.userId }).exec();

    const userEmail = user.contactDetails.email;
    const parentId = user.userId;
    const parentStatus = parentUser.status;
    //update student status in the student preference model collection when parent status is updated

    // Update student statuses in STUDENTPREFERENCE collection
    await studentPreferenceModel.updateMany(
      { parentUserId: parentId },
      { userStatus: parentStatus }
    );

    // Update child statuses in USERS collection in bulk
    const children = await studentPreferenceModel
      .find({ parentUserId: parentId })
      .exec();
      if (children && children.length > 0) {
        const childUserIds = children.map((child) => child.userId);

        // Use bulkWrite to update child statuses in USERS collection
        const userModelUpdates = childUserIds.map((childUserId) => ({
          updateOne: {
            filter: { userId: childUserId },
            update: { status: parentStatus },
          },
        }));
        await userModel.bulkWrite(userModelUpdates, { ordered: false });
      
        if (parentStatus === "deactivated") {
          const dateNow = dateTimeHelper.getCurrentDateInParisZone();
      
          // Find all sessions to cancel for all children
          const sessionsToCancel = await sessionModel.find({
            "students.userId": { $in: childUserIds },
            "sessionDate.startDate": { $gte: dateNow },
            status: { $ne: sessionsStatus.CANCELED },
          });
      
          if (sessionsToCancel.length > 0) {
            const sessionIds = sessionsToCancel.map((session) => session.sessionId);
            const tutorIds = [...new Set(sessionsToCancel.map((session) => session.tutors[0].userId))];
      
            // Cancel sessions
            const updateSessionsPromise = sessionModel.updateMany(
              { sessionId: { $in: sessionIds } },
              { status: sessionsStatus.CANCELED }
            );
      
            // Update TutorPreferencesModel for matched students and sessions
            const unmatchTutorPromise = TutorPreferencesModel.updateMany(
              { userId: { $in: tutorIds }, "matching.sessionId": { $in: sessionIds } },
              {
                $pull: { matchedStudents: { userId: { $in: childUserIds } } },
                $set: { "matching.$[elem].status": sessionsStatus.CANCELED },
              },
              { arrayFilters: [{ "elem.sessionId": { $in: sessionIds } }] }
            );
      
            // Update StudentPreferencesModel for matched tutors and sessions
            const unmatchStudentPromise = studentPreferenceModel.updateMany(
              { userId: { $in: childUserIds }, "matching.sessionId": { $in: sessionIds } },
              {
                $set: {
                  "matching.$[elem].status": sessionsStatus.CANCELED,
                  matchedTutors: [],
                },
              },
              { arrayFilters: [{ "elem.sessionId": { $in: sessionIds } }] }
            );
      
            // Execute all updates concurrently
            await Promise.all([updateSessionsPromise, unmatchTutorPromise, unmatchStudentPromise]);
          }
        }
      }
    //update tutor in User in firebase

    const responseFirebase = await firebaseHelper.resetEmail(
      userEmail,
      parentUser.contactDetails.email
    );
    if (!responseFirebase) {
      return {
        status: false,
        message: "Email parent not found in firebase",
        data: null,
      };
    }
    parentUser.firebaseIds.push({
      identifier: parentUser.contactDetails.email,
    });
    //update parent user
    const parentUserDocument = await userModel
      .findOneAndUpdate({ userId: parentUser.userId }, parentUser, {
        new: true,
      })
      .exec();
    if (!parentUserDocument) {
      return { status: false, message: "Parent not found", data: null };
    }
    //update parent preferences
    const parentPreferenceDocument = await parentPreferenceModel
      .findOneAndUpdate({ userId: parentUser.userId }, parentPreference, {
        new: true,
      })
      .exec();
    if (!parentPreferenceDocument) {
      return {
        status: false,
        message: "Parent preferences not found",
        data: null,
      };
    }

    const combineData = {
      ...parentUserDocument._doc,
      parentPreference: parentPreferenceDocument._doc,
    };

    return {
      status: true,
      message: "Parent updated successfully",
      data: combineData,
    };
  } catch (error) {
    console.log("Error in updating parent", error )
    return { status: false, message: "Error in updating parent", data: null };
  }
};
//update parent status
exports.updateParentAndChildStatus = async (userId, status) => {
  // const session = await userModel.startSession();
  // session.startTransaction();
  try {
    const parentUserDocument = await userModel
      .findOneAndUpdate({ userId: userId }, { status: status }, { new: true })
      .exec();
    if (!parentUserDocument) {
      // await session.abortTransaction();

      return { status: false, message: "Parent not found", data: null };
    }
    const parentId = parentUserDocument.userId;
    //update student status in the student preference model collection when parent status is updated
    const children = await studentPreferenceModel
      .find({ parentUserId: parentId })
      .select("userId")
      .exec();
    if (children.length === 0) {
      return {
        status: false,
        message: "No children found for the parent",
        data: null,
      };
    }
    const childrenUserIds = children.map((child) => child.userId);

    // Update child statuses in USERS collection in bulk
    if (childrenUserIds.length > 0) {
      for (childUserId of childrenUserIds) {
        await userModel
          .findOneAndUpdate({ userId: childUserId }, { status: status })
          .exec();
      }
    }
    await studentPreferenceModel.updateMany(
      {
        parentUserId: parentId,
      },
      { userStatus: status }
    );

    return {
      status: true,
      message: "Parent status updated successfully",
      data: parentUserDocument,
    };
  } catch (error) {
    return {
      status: false,
      message: "Error in updating parent status",
      data: null,
    };
  }
};
//get parent user by parent id
exports.getParentIS = async (parentId) => {
  try {
    const userDocument = await userModel.findOne({ userId: parentId }).exec();

    if (!userDocument) {
      return { status: false, message: "Parent not found", data: null };
    }

    //get parent preferences
    const parentPreferenceDocument = await parentPreferenceModel
      .findOne({ userId: parentId })
      .exec();

    const parentPreference = parentPreferenceDocument
      ? parentPreferenceDocument._doc
      : null;

    const combineData = {
      ...userDocument._doc,
      parentPreference: parentPreference,
    };
    return { status: true, message: "Parent", data: combineData };
  } catch (error) {
    return { status: false, message: "Error in getting parent", data: null };
  }
};

//get parent preferences by parent id
exports.getParentPreferences = async (parentId) => {
  try {
    const parentPreference = await parentPreferenceModel
      .findOne({ userId: parentId })
      .exec();
    if (parentPreference) {
      return {
        status: true,
        message: "Parent preferences",
        data: parentPreference,
      };
    } else {
      return { status: true, message: "Parent preferences", data: null };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in getting parent preferences",
      data: null,
    };
  }
};
const deleteUsersFromFirebase = async (firebaseIds) => {
  for (let i = 0; i < firebaseIds.length; i++) {
    const firebaseId = firebaseIds[i];
    await firebaseHelper.deleteUserFromAuth(firebaseId.identifier);
  }
};
//delete parent user Document and parent preferences document
exports.deleteParentIS = async (parentId) => {
  try {
    //find  user
    const userDocument = await userModel.findOne({ userId: parentId });
    if (!userDocument) {
      return { status: false, message: "Parent not found", data: null };
    }
    const firebaseIds = userDocument.firebaseIds;

    //delete user from database
    await userDocument.delete();

    //delete student from Student Preferences Model
    const parentPreference = await parentPreferenceModel.findOne({
      userId: parentId,
    });
    if (parentPreference) {
      //delete parentPreference from database
      await parentPreference.delete();
    }

    if (firebaseIds.length > 0) {
      //delete parent from firebase
      await deleteUsersFromFirebase(firebaseIds);
    }

    const childrenDocument = await studentPreferenceModel.find({
      parentUserId: parentId,
    });

    if (childrenDocument.length > 0) {
      const childrenUserIds = childrenDocument
        .map((doc) => doc.userId)
        .filter((userId) => userId); // Filter out any undefined or falsy values

      if (childrenUserIds.length > 0) {
        const userStudentsDocuments = await UserModels.find({
          userId: { $in: childrenUserIds },
        });

        await studentPreferenceModel.deleteMany({ parentUserId: parentId });

        //delete children from firebase
        const firebaseChildrenIds = userStudentsDocuments.flatMap(
          (user) => user.firebaseIds
        );
        if (firebaseChildrenIds.length > 0) {
          //delete children from firebase
          await deleteUsersFromFirebase(firebaseChildrenIds);
        }

        //delete children from DB
        await UserModels.deleteMany({ userId: { $in: childrenUserIds } });
        return {
          status: true,
          message: "Parent and children deleted successfully",
          data: null,
        };
      }
    }
    return { status: true, message: "Parent deleted successfully", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in deleting parent", data: null };
  }
};

//get list of all users base on user type $parent
exports.geISParentDashboard = async (payload) => {
  try {
    let { page, pageSize, filter, sortBy, userId, userRole } = payload;
    pageSize = buildPageSize(pageSize);
    page = buildPage(page);
    
    let item = {};
    let partner = {};
    if (isJSON(filter)) {
      item = JSON.parse(filter);
      if (item.partner) {
        partner = { partner: item.partner };
      }
    }
    
    const filterData = buildFilter(filter);
    const sortByData = buildSortBy(sortBy);
    
    // Utilisation des index et filtrage précoce
    if (userId && userRole && [userRolesConstants.COORDINATOR, userRolesConstants.VSC].includes(userRole)) {
      // Récupération des préférences admin avec projection pour limiter les données
      const userAdminDetails = await UserAdministrationModel.findOne(
        { userId },
        { "administrationPreferences.program": 1, "administrationPreferences.establishment": 1, "administrationPreferences.department": 1 }
      ).lean();
      
      const pref = userAdminDetails?.administrationPreferences;
      
      if (!pref || !pref.program?.length) {
        return {
          status: true,
          message: "Parents",
          data: [],
          totalNumberOfParents: 0,
          page: page,
          pageSize: pageSize,
        };
      }
      
      // Construction optimisée des filtres
      const studentPreferencesFilter = {};
      const program = pref.program[0];
      const programIds = pref.program.map(prog => prog.programId);
      
      // Ajout du programme au filtre
      studentPreferencesFilter["program.programId"] = { $in: programIds };
      
      // Optimisation: éviter les vérifications redondantes de programme
      if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
        const establishmentIds = pref.establishment?.map(est => est.establishmentId) || [];
        
        if (establishmentIds.length === 0) {
          return {
            status: true,
            message: "Parents",
            data: [],
            totalNumberOfParents: 0,
            page: page,
            pageSize: pageSize,
          };
        }
        
        studentPreferencesFilter["assignment.establishments.establishmentId"] = { $in: establishmentIds };
      } else if ([HOME_CLASSES.programId, CLASSSES_HOME.programId, ZUPDEFOOT.programId].includes(program.programId)) {
        const departmentIds = pref.department?.map(dep => dep.departmentId) || [];
        
        if (departmentIds.length === 0) {
          return {
            status: true,
            message: "Parents",
            data: [],
            totalNumberOfParents: 0,
            page: page,
            pageSize: pageSize,
          };
        }
        
        studentPreferencesFilter["assignment.department.departmentId"] = { $in: departmentIds };
        studentPreferencesFilter["program.programId"] = program.programId;
      }
      
      let filterDep = [];
      if (filter && isJSON(filter)) {
        const filterItem = JSON.parse(filter);
        if (filterItem.department) {
          filterDep = [filterItem.department];
        }
      }
      
      // Construction optimisée du filtre utilisateur
      const userFilter = filterData?.data && Object.keys(filterData?.data).length 
        ? filterData.data 
        : { userRole: userRoleConstants.ROLE_PARENT };
      
      // Projection pour limiter les données récupérées
      const userProjection = {
        userId: 1,
        "contactDetails.firstName": 1,
        "contactDetails.lastName": 1,
        "contactDetails.email": 1,
        userRole: 1,
        status: 1,
        "address.city": 1,
        establishmentName: 1,
        createdAt: 1,
        iAcceptToBeContactedForParticipatingInAStudy: 1
      };
      
      const studentPreferencesProjection = {
        parentUserId: 1
      };
      
      const parentPreferencesProjection = {
        userId: 1,
        onBoardingStep: 1,
        department: 1
      };
      
      // Exécution parallèle des requêtes avec projection
      const [parents, studentPreferences, parentPreferences] = await Promise.all([
        userModel.find(userFilter, userProjection).lean(),
        
        studentPreferenceModel.find(
          { ...studentPreferencesFilter },
          studentPreferencesProjection
        ).lean(),
        
        parentPreferenceModel.find(
          { ...partner },
          parentPreferencesProjection
        ).lean()
      ]);
      
      // Création de Maps pour accélérer les recherches
      const studentMap = new Map();
      studentPreferences.forEach(pref => {
        const parentId = pref.parentUserId.toString();
        if (!studentMap.has(parentId)) {
          studentMap.set(parentId, []);
        }
        studentMap.get(parentId).push(pref);
      });
      
      const parentPrefMap = new Map();
      parentPreferences.forEach(pref => {
        parentPrefMap.set(pref.userId.toString(), pref);
      });
      
      // Fusion des données avec Maps au lieu de .filter() multiples
      let filteredParents = parents
        .filter(parent => {
          const parentId = parent.userId.toString();
          const parentPref = parentPrefMap.get(parentId);
          const students = studentMap.get(parentId) || [];
          return parentPref && students.length > 0;
        })
        .map(parent => {
          const parentId = parent.userId.toString();
          const parentPref = parentPrefMap.get(parentId);
          
          return {
            ...parent,
            onBoardingStep: parentPref?.onBoardingStep || "1",
            department: parentPref?.department || {}
          };
        });
      
      // Filtrage par département
      if (filterDep.length > 0) {
        filteredParents = filteredParents.filter(parent => 
          filterDep.includes(parent.department?.departmentId)
        );
      }
      
      // Ajout du tri par createdAt ou autre champ spécifié
      if (sortByData && Object.keys(sortByData).length > 0) {
        filteredParents.sort((a, b) => {
          // Parcourir tous les champs de tri
          for (const [field, order] of Object.entries(sortByData)) {
            // Déterminer les valeurs à comparer
            let valueA, valueB;
            
            if (field === "contactDetails.firstName") {
              valueA = a.contactDetails?.firstName || "";
              valueB = b.contactDetails?.firstName || "";
            } else if (field === "contactDetails.lastName") {
              valueA = a.contactDetails?.lastName || "";
              valueB = b.contactDetails?.lastName || "";
            } else if (field === "contactDetails.email") {
              valueA = a.contactDetails?.email || "";
              valueB = b.contactDetails?.email || "";
            } else if (field === "department.departmentId") {
              valueA = a.department?.departmentId || "";
              valueB = b.department?.departmentId || "";
            } else if (field === "createdAt") {
              valueA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
              valueB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
            } else {
              valueA = a[field] || "";
              valueB = b[field] || "";
            }
            
            // Comparer et retourner si nécessaire
            if (valueA !== valueB) {
              return order === 1 ? (valueA > valueB ? 1 : -1) : (valueA < valueB ? 1 : -1);
            }
          }
          return 0;
        });
      } else {
        // Tri par défaut si aucun tri n'est spécifié
        filteredParents.sort((a, b) => {
          const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
          const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
          return dateB - dateA; // Tri descendant par défaut
        });
      }
      
      // Calcul total une seule fois
      const totalCount = filteredParents.length;
      
      // Application de la pagination
      const paginatedResults = filteredParents.slice(
        (page - 1) * pageSize,
        page * pageSize
      );
      
      // Transformation finale directe sans .map() supplémentaire quand possible
      const PARENTS = paginatedResults.map(parent => ({
        userId: parent.userId,
        firstName: parent.contactDetails?.firstName,
        lastName: parent.contactDetails?.lastName,
        userRole: parent.userRole,
        email: parent.contactDetails?.email,
        status: parent.status,
        city: parent?.address?.city,
        department: parent?.department?.departmentName
          ? extractCityName(parent?.department?.departmentName) + ` ${parent?.department?.departmentId}`
          : "",
        establishmentName: parent.establishmentName,
        createdAt: parent.createdAt,
        iAcceptToBeContactedForParticipatingInAStudy: parent.iAcceptToBeContactedForParticipatingInAStudy,
        onBoardingStep: parent?.onBoardingStep || "1",
      }));
      
      return {
        status: true,
        message: "Parents",
        data: PARENTS,
        totalNumberOfParents: totalCount,
        page: page,
        pageSize: pageSize,
      };
      
    } else {
      // Optimisation de l'agrégation en divisant en 2 pour éviter le coûteux $facet
      // Pipeline commun
      const matchStage = {
        $match: {
          userRole: userRoleConstants.ROLE_PARENT,
          ...filterData.data
        }
      };
      
      const lookupStage = {
        $lookup: {
          from: "parentpreferences",
          let: { userId: "$userId" },
          pipeline: [
            { 
              $match: { 
                $expr: { $eq: ["$userId", "$$userId"] },
                ...partner
              } 
            },
            {
              $project: {
                onBoardingStep: 1,
                department: 1,
                userId: 1
              }
            }
          ],
          as: "parentPreferences"
        }
      };
      
      const filterStage = {
        $match: {
          "parentPreferences.0": { $exists: true }
        }
      };
      
      const addFieldsStage = {
        $addFields: {
          onBoardingStep: { $arrayElemAt: ["$parentPreferences.onBoardingStep", 0] },
          department: { $arrayElemAt: ["$parentPreferences.department", 0] }
        }
      };
      
      const projectStage = {
        $project: {
          userId: 1,
          "contactDetails.firstName": 1,
          "contactDetails.lastName": 1,
          "contactDetails.email": 1,
          userRole: 1,
          status: 1,
          "address.city": 1,
          department: 1,
          establishmentName: 1,
          createdAt: 1,
          iAcceptToBeContactedForParticipatingInAStudy: 1,
          onBoardingStep: 1
        }
      };
      
      // Exécution des requêtes en parallèle
      const [countResult, parents] = await Promise.all([
        userModel.aggregate([
          matchStage,
          lookupStage,
          filterStage,
          { $count: "count" }
        ]),
        userModel.aggregate([
          matchStage,
          lookupStage,
          filterStage,
          addFieldsStage,
          projectStage,
          { $sort: sortByData },
          { $skip: (page - 1) * pageSize },
          { $limit: pageSize }
        ])
      ]);
      
      const totalCount = countResult[0]?.count || 0;
      
      // Transformation finale 
      const PARENTS = parents.map(parent => ({
        userId: parent.userId,
        firstName: parent.contactDetails?.firstName,
        lastName: parent.contactDetails?.lastName,
        userRole: parent.userRole,
        email: parent.contactDetails?.email,
        status: parent.status,
        city: parent?.address?.city,
        department: parent?.department?.departmentName
          ? extractCityName(parent?.department?.departmentName) + ` ${parent?.department?.departmentId}`
          : "",
        establishmentName: parent.establishmentName,
        createdAt: parent.createdAt,
        iAcceptToBeContactedForParticipatingInAStudy: parent.iAcceptToBeContactedForParticipatingInAStudy,
        onBoardingStep: parent?.onBoardingStep || "1",
      }));
      
      return {
        status: true,
        message: "Parents",
        data: PARENTS,
        totalNumberOfParents: totalCount,
        page: page,
        pageSize: pageSize,
      };
    }
    
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Une erreur s'est produite lors de la récupération des parents",
      data: [],
      totalNumberOfParents: 0,
      page: 1,
      pageSize: 10,
    };
  }
};
function extractCityName(input) {
  // Utilise une expression régulière pour extraire le nom de la ville, en éliminant les codes éventuels
  const match = input.match(/^(.+?)(?:\s*\(\d{2}\))?$/);

  if (match) {
    // Extrait et retourne le nom de la ville sans les codes
    return match[1];
  }

  // Retourne l'entrée d'origine si aucune correspondance n'est trouvée
  return input;
}
function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null) {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null) {
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }

  return page;
}

function buildFilter(filter) {
  let parsedFilter = {};

  //check if the filter is json or not
  if (filter) {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      parsedFilter = JSON.parse(filter);
    }
  }

  //add userRole to filter

  //filter with value string RegExp
  for (const [key, value] of Object.entries(parsedFilter)) {
    if (key == "lastName") {
      //update email key to contactDetails.email
      parsedFilter["contactDetails.lastName"] = value;
      delete parsedFilter[key];
    } else if (key == "firstName") {
      parsedFilter["contactDetails.firstName"] = value;
      delete parsedFilter[key];
    } else if (key == "email") {
      parsedFilter["contactDetails.email"] =
        filterHelper.createEmailRegex(value);
      delete parsedFilter[key];
    } else if (key == "study") {
      parsedFilter["iAcceptToBeContactedForParticipatingInAStudy"] = value;
      delete parsedFilter[key];
    } else if (key == "city") {
      parsedFilter["address.city"] = value;
      delete parsedFilter[key];
    } else if (key == "onBoardingStep") {
      parsedFilter["onBoardingStep"] = value;
      // delete parsedFilter[key];
    } else if (key === "firstAndLastName") {
      parsedFilter["$or"] = [
        { "contactDetails.firstName": { $regex: value, $options: "i" } },
        { "contactDetails.lastName": { $regex: value, $options: "i" } },
      ];
      delete parsedFilter[key];
    } else if (key === "department") {
      parsedFilter["department.departmentId"] = value;
      delete parsedFilter[key];
    } else if (key === "partner") {
      delete parsedFilter[key];
    }

    //filter with value string RegExp
    for (const [key, value] of Object.entries(parsedFilter)) {
      if (typeof value == "string") {
        let regex = new RegExp(value, "i");
        parsedFilter[key] = regex;
      }
    }
  }

  parsedFilter["userRole"] = userRoleConstants.ROLE_PARENT;

  return { status: true, data: parsedFilter };
}

//search if parent has child, base on parentId. Return true if parent has child
exports.searchParentHasChild = async (parentId) => {
  try {
    const student = await studentPreferenceModel
      .find({ parentUserId: parentId })
      .exec();
    if (student.length > 0) {
      return { status: true, message: "Parent has child", data: true };
    } else {
      return { status: true, message: "Parent has child", data: false };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in searching parent has child",
      data: null,
    };
  }
};

function buildSortBy(sortByObject) {
  let sortBy = {};
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);

    //check if sortBy is empty or not
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        sortBy["contactDetails.lastName"] = value;
      } else if (key == "firstName") {
        sortBy["contactDetails.firstName"] = value;
      } else if (key == "email") {
        sortBy["contactDetails.email"] = value;
      } else if (key == "department") {
        sortBy["department.departmentId"] = value;
      } else if (key == "createdAt") {
        sortBy["createdAt"] = value;
      }
    }
  } else {
    sortBy = { createdAt: -1 };
  }

  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

//update user Admin with invitationCode
exports.updateUserAdminWithInvitationCode = async (
  userId,
  invitationCode,
  invitationAccepted
) => {
  try {
    let userDocument = await parentPreferenceModel
      .findOne({ userId: userId })
      .exec();

    if (!userDocument) {
      console.log("User not found");
      return { status: false, message: "User not found", data: null };
    }

    if (invitationAccepted) {
      userDocument.invitation.invitationAccepted = true;
      userDocument.invitation.invitationAcceptedDate = new Date();
    } else {
      //setup invitation object
      let invitationObject = {
        invitationCode: invitationCode,
        invitationAccepted: false,
        invitationDate: new Date(),
        invitationAcceptedDate: null,
      };

      //add invitation object to userDocument
      userDocument.invitation = invitationObject;
    }

    //save userDocument
    let updatedUserDocument = await userDocument.save();

    return {
      status: true,
      message: "Invitation code updated successfully",
      data: updatedUserDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};
exports.updateParentEmail = async (oldEmail, newEmail) => {
  await firebaseHelper.resetEmail(oldEmail, newEmail);
};

exports.updateParentPreferences = async (userId, parentPreferences) => {
  try {
    const parentPreferenceDocument = await parentPreferenceModel
      .findOneAndUpdate({ userId: userId }, parentPreferences, {
        new: true,
      })
      .exec();
    if (!parentPreferenceDocument) {
      return {
        status: false,
        message: "Parent preferences not found",
        data: null,
      };
    }
    return {
      status: true,
      message: "Parent preferences updated successfully",
      data: parentPreferenceDocument,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in updating parent preferences",
      data: null,
    };
  }
};
exports.getParentStatus = async (parentId) => {
  try {
    const parentStatus = await userModel
      .findOne({ userId: parentId })
      .select("status")
      .exec();
    if (!parentStatus) {
      return { status: false, message: "Parent not found", data: null };
    }
    return {
      status: true,
      message: "Parent status",
      data: parentStatus,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in getting parent status",
      data: null,
    };
  }
};
