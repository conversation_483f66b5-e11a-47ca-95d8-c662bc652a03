const { MongoClient, ObjectId } = require("mongodb");
const fs = require("fs");
const path = require("path");
const moment = require("moment");
const gettypeOfEstablishments = require("./typeOfEstab.js");

const uri =
  "mongodb+srv://zupdeco-dev-db:<EMAIL>/zupdeco-dev-db?retryWrites=true&w=majority";
const dbName = "zupdeco-dev-db";
const collectionName = "educationannuaires";
const sectorCollectionName = "sectors";

const jsonFilePath = path.join(__dirname, "./Lycee.json");

// Fonction pour trouver le secteur basé sur le code postal
// async function findSectorIdByPostalCode(client, postalCode) {

//     const sectorCollection = client.db(dbName).collection(sectorCollectionName);
//     const sector = await sectorCollection.findOne({ code: postalCode.slice(2, 4) });
//     if (sector) {
//         return { id: sector._id.toHexString(), name: sector.name };
//     }
//     return { id: null, name: "" };
// }

async function main() {
  const client = new MongoClient(uri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });

  try {
    await client.connect();
    console.log("Connected to MongoDB");

    const db = client.db(dbName);
    const collection = db.collection(collectionName);
    console.log("collection", collection);

    const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, "utf8"));

    const establishments = await Promise.all(
      jsonData.map(async (data) => {
        const typeOfEstablishment = gettypeOfEstablishments(
          data.type_etablissement
        );
        // const sector = await findSectorIdByPostalCode(client, data.dep_id);
        return {
          _id: ObjectId(),
          typeOfEstablishment: {
            typeOfEstablishmentId: typeOfEstablishment.id,
            typeOfEstablishmentName: typeOfEstablishment.name
              .toLowerCase()
              .normalize("NFC"),
          },
          establishmentName: data.nom_etablissement,
          createdAt: moment().toDate(),
          generalInformation: {
            sector: {
              sectorId: null,
              sectorName: "",
            },
            address: {
              address: "",
              postalCode: "",
              city: "",
              country: "France",
            },
            category: "",
            status: data.secteur_d_etablissement || "",
            telePhone: data.numero_telephone_uai
              ? data.numero_telephone_uai
                  .replace(/\D/g, "")
                  .replace(/^0/, "+33")
              : "",
          },
          partnership: {
            startOfPartnership: null,
            participationInTheClassCouncil: false,
            zupDeCoLineInTheNewsLetter: false,
          },
          session: {
            numberOfActiveTutoredStudentsWhole: 0,
            pupilsWaiting: 0,
          },
          detailInformationSchool: {
            enCategory: [],
            numberActiveN1: 0,
            numberActiveN2: 0,
          },
          // establishmentName: data.nom_etablissement,
          contact: [
            {
              email: "",
              phoneNumber: data.numero_telephone_uai
                ? data.numero_telephone_uai
                    .replace(/\D/g, "")
                    .replace(/^0/, "+33")
                : "",
            },
          ],
          class: [],
        };
      })
    );

    const result = await collection.insertMany(establishments);
    console.log(`${result.insertedCount} establishments inserted`);
  } catch (err) {
    console.error(
      "Error connecting to MongoDB or inserting establishments:",
      err
    );
  } finally {
    await client.close();
  }
}

main().catch(console.error);
