# To dump a MongoDB database from a remote server and restore it to a local MongoDB instance

To dump a MongoDB database from a remote server and restore it to your local MongoDB instance, you can follow these steps:

1. Ensure MongoDB Tools are Installed: Make sure you have mongodump and mongorestore tools installed on your system. These tools come with MongoDB Database Tools.

2. Dump the Remote Database: Use mongodump to create a backup of your remote database. You can do this by running the following command in your terminal:
    ```bash
    $ mongodump --uri="*********************************************************************************************************************" --out=".\Desktop\backup"
    ```
3. Create a database in your local mongodb instance with the same name as the remote database. example : `zupdeco-your_env-db`
   
4. Restore the Dump to Local MongoDB: Use mongorestore to restore the dumped database to your local MongoDB instance. Assuming your local MongoDB instance is running with default settings, the command will look like this:
    ```bash
    $ mongorestore --uri="mongodb://localhost:27017" ".\Desktop\backup\zupdeco-your_env-db"
    ```
