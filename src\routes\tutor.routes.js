const express = require("express");
const router = express.Router();
const User = require("../models/User.Models.js");
const crReportsModel = require("../models/Cr.Report.Model.js");
const tutorPreferences = require("../models/Tutor.Preferences.Model.js");

const userAdmin = require("../models/User.Administration.Model.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import formidable
const formidable = require("formidable");

//import  logger
const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("tutor.routes.js");

//Sendgrid
const sendGrid = require("@sendgrid/mail");

//import useerHelper
const userHelper = require("../controllers/user/User.Helper.js");

//import tutorHelper
const tutorHelper = require("../controllers/tutor/tutor.helper.js");

//import studentHelper
const studentHelper = require("../controllers/students/student.helpers.js");

//import firebaseFileHelper
const firebaseFileHelper = require("../controllers/firebase/FirebaseFile.Helper.js");
const firebaseHelper = require("../controllers/firebase/firebase.helper.js");
const firebaseConstants = require("../utils/constants/FirebaseConstants.js");

var _array = require("lodash/array");
var _ = require("lodash/core");

const { DateTime } = require("luxon");

const appConstants = require("../utils/constants/app.constants.js");

const zupdecodateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const userRoleConstants = require("../utils/constants/userRolesConstants.js");

const userModel = require("../models/User.Models.js");

const sessionStudentHelper = require("../controllers/sessions/sessions.student.helper.js");

const sessionTutorHelper = require("../controllers/sessions/sessions.tutor.helper.js");

const programConstants = require("../utils/constants/program.constants.js");

const tutorConstants = require("../utils/constants/tutor.constants.js");

const reportConstants = require("../utils/constants/reports.constants.js");

const reportsModel = require("../models/Cr.Report.Model.js");

const mongoose = require("mongoose");

const { empty } = require("uuidv4");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");
const admin = require("firebase-admin");

const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../utils/constants/program.constants.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  ROLE_TUTOR,
  SUPER_ADMINISTRATOR,
  VSC,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");
const {
  statusOfTutors,
  situationOfTutor,
} = require("../utils/constants/tutor.constants.js");
const { tutor } = require("../services/logger/loggerType.js");
const { constant } = require("lodash");
let onlineProg = [
  HOME_CLASSES.programId,
  ZUPDEFOOT.programId,
  CLASSSES_HOME.programId,
];
const tutorSitution = [
  situationOfTutor.FOR_RENEWAL,
  situationOfTutor.PAUSE,
  situationOfTutor.STOP,
  situationOfTutor.NO_FURTHER_ACTION,
];
//Save user in the mongoDB :)
/**
 * @swagger
 * /api/v1/user/tutor:
 *   post:
 *     summary: Create a new tutor user
 *     description: Create a new tutor user with the provided details.
 *     tags:
 *       - Tutor
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firebaseUserId:
 *                 type: string
 *                 description: Firebase user ID of the tutor.
 *               provider:
 *                 type: string
 *                 description: Authentication provider used (e.g., Google, Facebook).
 *               firebaseIdentifier:
 *                 type: string
 *                 description: Unique identifier of the tutor in Firebase.
 *               profilePic:
 *                 type: string
 *                 description: URL of the tutor's profile picture.
 *               tutorType:
 *                 type: string
 *                 description: Type of tutoring program (e.g., Home Classes, Devoirs Faits).
 *               phoneNumber:
 *                 type: string
 *                 description: The phoneNumber.
 *               email:
 *                 type: string
 *                 description: The email address.
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: Date of birth of the tutor.
 *               statusOfTutor:
 *                 type: string
 *                 description: Status of the tutor (e.g., Active, Inactive).
 *     responses:
 *       200:
 *         description: Tutor user created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User is created successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: 12345
 *                     documentId:
 *                       type: string
 *                       example: abcdef1234567890
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: FirebaseUserId is null
 *                 error:
 *                   type: string
 *                   example: FirebaseUserId is required, please provide a valid userId
 *       500:
 *         description: Error creating tutor user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while saving user
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.post(
  "/user/tutor",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const {
        firebaseUserId,
        provider,
        firebaseIdentifier,
        profilePic,
        tutorType,
        dateOfBirth,
        statusOfTutor,
        firstName,
        lastName,
        phoneNumber,
        email,
        iAgreeToReceiveInformationAndUpdates,
        iAcceptTermsAndPolicyOfConfidentiality,
        iAcceptToBeContactedForParticipatingInAStudy,
      } = req.body;
      //userId, email, tutorType edhe signInProvider

      // userId is generated by  uuidv4() and is unique for each user
      const userId = userHelper.generateNewUserId();

      const contactDetails = {
        firstName: firstName,
        lastName: lastName,
        phoneNumber: phoneNumber,
        email: email,
      };

      const firebaseIds = {
        identifier: firebaseIdentifier,
        firebaseUserId: firebaseUserId,
        provider: provider,
      };

      //check if userId is present in the request
      if (!firebaseUserId) {
        let response = apiResponse.response(
          false,
          `FirebaseUserId is null: ${userId}`,
          `FirebaseUserId is required, please provide a valid userId`
        );
        return res.status(400).json(response);
      }

      //check if tutorType is present in the request
      if (!tutorType) {
        let response = apiResponse.response(
          false,
          `TutorType is null:  ${tutorType}`,
          `TutorType is required, please provide a valid tutorType`
        );
        return res.status(400).json(response);
      }

      //Check if user is already present in the database
      let userData = await userHelper.checkIfUserExists(firebaseIdentifier);
      if (userData) {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User already exists with this identifier ${firebaseIdentifier}`,
          { userId: userData.userId },
          apiResponse.apiConstants.USER_ALREADY_EXISTS
        );
        return res.status(200).json(response);
      } else {
        //create new user
        let user = new User({
          userId: userId,
          firebaseIds: firebaseIds,
          contactDetails: contactDetails,
          userRole: userRoleConstants.ROLE_TUTOR,
          dateOfBirth: dateOfBirth,
          iAgreeToReceiveInformationAndUpdates:
            iAgreeToReceiveInformationAndUpdates,
          iAcceptTermsAndPolicyOfConfidentiality:
            iAcceptTermsAndPolicyOfConfidentiality,
          iAcceptToBeContactedForParticipatingInAStudy:
            iAcceptToBeContactedForParticipatingInAStudy,
          hasChangedPassword: true,
        });
        //save user
        const newUserDocument = await user.save();
        //create new tutor preferences

        //update tutorType to array
        let program = {};
        let tutoringType = [];
        if (tutorType == programConstants.HOME_CLASSES.programId) {
          program = {
            programId: programConstants.HOME_CLASSES.programId,
            programName: programConstants.HOME_CLASSES.name,
          };
          tutoringType.push(tutorConstants.tutoringType.TUTORING_TYPE_PRIVATE);
        } else if (tutorType == programConstants.DEVOIRS_FAITS.programId) {
          program = {
            programId: programConstants.DEVOIRS_FAITS.programId,
            programName: programConstants.DEVOIRS_FAITS.name,
          };
          tutoringType.push(tutorConstants.tutoringType.TUTORING_TYPE_PRIVATE);
          tutoringType.push(tutorConstants.tutoringType.TUTORING_TYPE_GROUP);
        }
        let userPreferences = new tutorPreferences({
          userId: userId,
          contactDetails: newUserDocument.contactDetails,
          program: program,
          assignment: {
            tutoringType: tutoringType,
          },
          activity: {
            tutorActivityStatus: statusOfTutor ? statusOfTutor : "",
          },
        });
        //save preferences
        await userPreferences.save();
        const token = generateAccessToken(userId);
        const refreshToken = generateRefreshToken(userId);

        let response = apiResponse.responseWithStatusCode(
          true,
          `User is created successfully identifier: ${firebaseIdentifier}`,
          {
            userId: userId,
            documentId: user._id,
            token,
          },
          apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
        );
        res.cookie("refreshToken", refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
        });
        return res.status(200).json(response);
      }
    } catch (error) {
      let response = apiResponse.response(
        false,
        `Error while saving user`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//Save user in the mongoDB :)
/**
 * @swagger
 * /api/v1/user/tutor/last-step:
 *   put:
 *     summary: Update tutor user information
 *     description: Update the last step of tutor user information with the provided details.
 *     tags:
 *       - Tutor
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: Unique ID of the tutor user.
 *               firstName:
 *                 type: string
 *                 description: First name of the tutor user.
 *               lastName:
 *                 type: string
 *                 description: Last name of the tutor user.
 *               iAgreeToReceiveInformationAndUpdates:
 *                 type: boolean
 *                 description: Whether the tutor user agrees to receive information and updates.
 *               iAcceptTermsAndPolicyOfConfidentiality:
 *                 type: boolean
 *                 description: Whether the tutor user accepts the terms and policy of confidentiality.
 *               iAcceptToBeContactedForParticipatingInAStudy:
 *                 type: boolean
 *                 description: Whether the tutor user accepts to be contacted for participating in a study.
 *               firebaseUserId:
 *                 type: string
 *                 description: Firebase user ID of the tutor user.
 *               firebaseIdentifier:
 *                 type: string
 *                 description: Unique identifier of the tutor user in Firebase.
 *               provider:
 *                 type: string
 *                 description: Authentication provider used (e.g., Google, Facebook).
 *     responses:
 *       200:
 *         description: Tutor user information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User is updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: 12345
 *                     isFirebaseIdentifierExists:
 *                       type: boolean
 *                       example: false
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: UserId is null
 *                 error:
 *                   type: string
 *                   example: UserId is required, please provide a valid userId
 *       500:
 *         description: Error updating tutor user information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while saving user
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.put(
  "/user/tutor/last-step",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    try {
      const {
        userId,
        firstName,
        lastName,
        iAgreeToReceiveInformationAndUpdates,
        iAcceptTermsAndPolicyOfConfidentiality,
        iAcceptToBeContactedForParticipatingInAStudy,
        firebaseUserId,
        firebaseIdentifier,
        provider,
      } = req.body;
      //check if userId is present in the request
      if (!userId) {
        let response = apiResponse.response(
          false,
          `UserId is null: ${userId}`,
          `UserId is required, please provide a valid userId`
        );
        return res.status(200).json(response);
      }

      let isFirebaseIdentifierExists =
        await userHelper.checkIfTheFirebaseIdentifierExist(firebaseIdentifier);
      if (isFirebaseIdentifierExists.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User already exists with this identifier ${firebaseIdentifier}`,
          {
            userId: isFirebaseIdentifierExists.data.userId,
            isFirebaseIdentifierExists: true,
          },
          apiResponse.apiConstants.USER_ALREADY_EXISTS
        );
        return res.status(200).json(response);
      }

      //Check if user is already present in the database
      let userDocument = await userHelper.getUserDetailsByUserId(userId);
      if (!userDocument) {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User does not exists with this userId ${userId}`,
          `Please provide a valid userId`,
          apiResponse.apiConstants.USER_DOES_NOT_EXISTS
        );
        return res.status(200).json(response);
      } else {
        let firebaseIds = userDocument.firebaseIds;
        if (firebaseUserId && provider && firebaseIdentifier) {
          let newFirebaseIds = {
            identifier: firebaseIdentifier,
            firebaseUserId: firebaseUserId,
            provider: provider,
          };
          firebaseIds.push(newFirebaseIds);

          let newContactDetails = await userHelper.getContactDetailsObject(
            firebaseIdentifier,
            userDocument.contactDetails
          );
          userDocument.contactDetails.email = newContactDetails.email;
          userDocument.contactDetails.phoneNumber =
            newContactDetails.phoneNumber;
        }
        //create new user
        userDocument.firebaseIds = firebaseIds;
        userDocument.contactDetails.firstName = firstName;
        userDocument.contactDetails.lastName = lastName;
        userDocument.iAgreeToReceiveInformationAndUpdates =
          iAgreeToReceiveInformationAndUpdates;
        userDocument.iAcceptTermsAndPolicyOfConfidentiality =
          iAcceptTermsAndPolicyOfConfidentiality;
        userDocument.iAcceptToBeContactedForParticipatingInAStudy =
          iAcceptToBeContactedForParticipatingInAStudy;

        //update document
        let tutorSaved = await userDocument.save();

        //update tutor preferences
        let tutorPreferencesDocument = await tutorPreferences.findOne({
          userId: userId,
        });
        if (tutorPreferencesDocument) {
          tutorPreferencesDocument.contactDetails = tutorSaved.contactDetails;

          //save preferences
          await tutorPreferencesDocument.save();
        }
        if (!tutorSaved) {
          let response = apiResponse.responseWithStatusCode(
            false,
            `User is not updated successfully identifier: ${firebaseIdentifier}`,
            "User is not updated successfully",
            apiResponse.apiConstants.USER_IS_NOT_REGISTERED
          );
          return res.status(200).json(response);
        }
        const token = generateAccessToken(userId);
        const refreshToken = generateRefreshToken(userId);

        let response = apiResponse.responseWithStatusCode(
          true,
          `User is updated successfully identifier: ${firebaseIdentifier}`,
          {
            userId: userId,
            isFirebaseIdentifierExists: false,
            token,
          },
          apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
        );
        res.cookie("refreshToken", refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
        });
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log("Error while saving user", error);
      let response = apiResponse.response(
        false,
        `Error while saving user`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//onboarding tutor
/**
 * @swagger
 * /api/v1/tutor/onboarding:
 *   put:
 *     summary: Update tutor onboarding information
 *     description: Update the onboarding information of a tutor with the provided details.
 *     tags:
 *       - Tutor
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               tutorPreferencesData:
 *                 type: string
 *                 format: binary
 *                 description: JSON string containing tutor preferences data.
 *     responses:
 *       200:
 *         description: Tutor onboarding information updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: 'Tutor preferences updated successfully for userId: 12345'
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: UserId is null
 *                 error:
 *                   type: string
 *                   example: UserId is required, please provide a valid userId
 *       500:
 *         description: Error updating tutor onboarding information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while creating/updating tutor preferences
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.put(
  "/tutor/onboarding",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      //User id from the request
      try {
        const tutorPreferencesData = JSON.parse(fields.tutorPreferencesData);
        // console.log("\n\n#####################################################################");

        // console.log("tutor/onboarding", tutorPreferencesData);

        // console.log("\n\n#####################################################################");

        //remove _id from the object
        if (tutorPreferencesData?._id) {
          delete tutorPreferencesData._id;
        }

        const userId = tutorPreferencesData?.userId;

        //check if userId is present in the request
        if (!userId) {
          let response = apiResponse.response(
            false,
            `UserId is null: ${userId}`,
            `UserId is required, please provide a valid userId`
          );
          return res.status(400).json(response);
        }

        //check if user exists
        const user = await userHelper.getUserDetailsByUserId(userId);

        if (!user) {
          let response = apiResponse.response(
            false,
            `User not found: ${userId}`,
            `User not found, please provide a valid userId`
          );
          return res.status(400).json(response);
        }

        //check if tutorPreferencesData.gender is empty or null get from user.gender and update same filed in user model
        if (tutorPreferencesData.gender) {
          user.gender = tutorPreferencesData.gender;
        } else {
          user.gender = user.gender;
        }

        //check if tutorPreferencesData.dateOfBirth is empty or null get from user.dateOfBirth and update same filed in user model
        if (tutorPreferencesData.dateOfBirth) {
          user.dateOfBirth = tutorPreferencesData.dateOfBirth;
        } else {
          user.dateOfBirth = user.dateOfBirth;
        }

        //check if tutorPreferencesData.address is empty or null get from user.address and update same filed in user model
        if (tutorPreferencesData.address) {
          user.address = tutorPreferencesData?.address;
        } else {
          user.address = user?.address;
        }

        //check if tutorPreferencesData.phoneNumber is empty or null get from user.contactDetails.phoneNumber and update same filed in user model
        if (tutorPreferencesData.phoneNumber) {
          user.contactDetails.phoneNumber = tutorPreferencesData.phoneNumber;
        } else {
          user.contactDetails.phoneNumber = user.contactDetails.phoneNumber;
        }
        if (tutorPreferencesData.email) {
          user.contactDetails.email = tutorPreferencesData.email;
          //update the identifier in the user's firebaseIds
          user.firebaseIds[0].identifier = tutorPreferencesData.email;
          //update firebase email
          await firebaseHelper.updateFirebaseEmail(
            user.firebaseIds[0].firebaseUserId,
            tutorPreferencesData.email
          );
        } else {
          user.contactDetails.email = user.contactDetails.email;
          //update the identifier in the user's firebaseIds
          user.firebaseIds[0].identifier = user.contactDetails.email;
          //update firebase email
          await firebaseHelper.updateFirebaseEmail(
            user.firebaseIds[0].firebaseUserId,
            user.contactDetails.email
          );
        }
        //check if tutorPreferencesData.firstName is empty or null get from user.contactDetails.firstName and update same filed in user model
        if (tutorPreferencesData.firstName) {
          user.contactDetails.firstName = tutorPreferencesData.firstName;
        } else {
          user.contactDetails.firstName = user.contactDetails.firstName;
        }
        //check if tutorPreferencesData.lastName is empty or null get from user.contactDetails.lastName and update same filed in user model
        if (tutorPreferencesData.lastName) {
          user.contactDetails.lastName = tutorPreferencesData.lastName;
        } else {
          user.contactDetails.lastName = user.contactDetails.lastName;
        }

        //check if tutorPreferencesData.iAgreeToReceiveInformationAndUpdates is empty or null get from user.iAgreeToReceiveInformationAndUpdates and update same filed in user model
        if (tutorPreferencesData.iAgreeToReceiveInformationAndUpdates) {
          user.iAgreeToReceiveInformationAndUpdates =
            tutorPreferencesData.iAgreeToReceiveInformationAndUpdates;
        } else {
          user.iAgreeToReceiveInformationAndUpdates =
            user.iAgreeToReceiveInformationAndUpdates;
        }
        if (tutorPreferencesData.iAcceptTheCommitmentLetter) {
          user.iAcceptTheCommitmentLetter = true;
        } else {
          user.iAcceptTheCommitmentLetter = user.iAcceptTheCommitmentLetter;
        }
        if (tutorPreferencesData.iAcceptToBeContactedForParticipatingInAStudy) {
          user.iAcceptToBeContactedForParticipatingInAStudy =
            tutorPreferencesData.iAcceptToBeContactedForParticipatingInAStudy;
        } else {
          user.iAcceptToBeContactedForParticipatingInAStudy =
            user.iAcceptToBeContactedForParticipatingInAStudy;
        }

        const userUpdatedDocument = await user.save();
        //check if existing tutor preferences for the userId
        const existingTutorPreferences = await tutorPreferences.findOne({
          userId: userId,
        });
        if (existingTutorPreferences) {
          //update existing tutor preferences
          let tutorPreferencesDocuments = await tutorPreferences.findOne({
            userId: userId,
          });
          tutorPreferencesDocuments.contactDetails =
            userUpdatedDocument.contactDetails;
          //update tutor preferences
          tutorPreferencesDocuments.assignment.numberOfStudentsToSupport =
            tutorPreferencesData.numberOfStudentsToSupport
              ? tutorPreferencesData.numberOfStudentsToSupport
              : tutorPreferencesDocuments.assignment.numberOfStudentsToSupport;
          tutorPreferencesDocuments.assignment.strongAreas =
            tutorPreferencesData.strongAreas
              ? tutorPreferencesData.strongAreas
              : tutorPreferencesDocuments.assignment.strongAreas;
          tutorPreferencesDocuments.attendInformationMeeting =
            tutorPreferencesData.attendInformationMeeting;
          tutorPreferencesDocuments.attendInformationMeetingDate =
            tutorPreferencesData.attendInformationMeetingDate;

          tutorPreferencesDocuments.facilityName =
            tutorPreferencesData.facilityName
              ? tutorPreferencesData.facilityName
              : tutorPreferencesDocuments.facilityName;

          if (
            tutorPreferencesData.tutorActivityStatus ||
            tutorPreferencesData.company ||
            tutorPreferencesData.fieldOfActivity
          ) {
            tutorPreferencesDocuments.activity.tutorActivityStatus =
              tutorPreferencesData.tutorActivityStatus;
            tutorPreferencesDocuments.activity.company =
              tutorPreferencesData.company;
            tutorPreferencesDocuments.activity.fieldOfActivity =
              tutorPreferencesData.fieldOfActivity;
          }
          if (tutorPreferencesData.ufrCurriculum) {
            if (
              tutorPreferencesDocuments.activity &&
              tutorPreferencesDocuments.activity.ufrCurriculum
            ) {
              tutorPreferencesDocuments.activity.ufrCurriculum.length = 0; // clear the array
            } else {
              tutorPreferencesDocuments.activity = { ufrCurriculum: [] };
            }
            const ufrCurriculumArray = Array.isArray(
              tutorPreferencesData.ufrCurriculum
            )
              ? tutorPreferencesData.ufrCurriculum
              : [tutorPreferencesData.ufrCurriculum];
            tutorPreferencesDocuments.activity.ufrCurriculum =
              ufrCurriculumArray;
          }

          tutorPreferencesDocuments.availability =
            tutorPreferencesData.availability
              ? tutorPreferencesData.availability
              : tutorPreferencesDocuments.availability;
          tutorPreferencesDocuments.subjects = tutorPreferencesData.subjects
            ? tutorPreferencesData.subjects
            : tutorPreferencesDocuments.subjects;
          tutorPreferencesDocuments.activity.comments =
            tutorPreferencesData.comments
              ? tutorPreferencesData.comments
              : tutorPreferencesDocuments.activity.comments;
          // tutorPreferencesDocuments.activity.suiviCoordo =
          //   tutorPreferencesData.suiviCoordo
          //     ? tutorPreferencesData.suiviCoordo
          //     : tutorPreferencesDocuments.activity.suiviCoordo;
          if (tutorPreferencesData?.commitment?.endDate) {
            tutorPreferencesDocuments.commitment.endDate = tutorPreferencesData.commitment.endDate
          }
          tutorPreferencesDocuments.homeDepartment =
            tutorPreferencesData.homeDepartment;
          tutorPreferencesDocuments.engagementLetter =
            tutorPreferencesData.engagementLetter
              ? tutorPreferencesData.engagementLetter
              : tutorPreferencesDocuments.engagementLetter;
          tutorPreferencesDocuments.howDidYouHearAboutUs =
            tutorPreferencesData.howDidYouHearAboutUs
              ? tutorPreferencesData.howDidYouHearAboutUs
              : tutorPreferencesDocuments.howDidYouHearAboutUs;

          tutorPreferencesDocuments.howDidYourHeardAboutUsDetails =
            tutorPreferencesData.howDidYourHeardAboutUsDetails
              ? tutorPreferencesData.howDidYourHeardAboutUsDetails
              : tutorPreferencesDocuments.howDidYourHeardAboutUsDetails;

          tutorPreferencesDocuments.isYourCommitmentValuedByYourInstitution =
            tutorPreferencesData.isYourCommitmentValuedByYourInstitution;
          tutorPreferencesDocuments.iAcceptToBeContactedForParticipatingInAStudy =
            tutorPreferencesData.iAcceptToBeContactedForParticipatingInAStudy;
          tutorPreferencesDocuments.subjectToTeach =
            tutorPreferencesData.subjectToTeach
              ? tutorPreferencesData.subjectToTeach
              : tutorPreferencesDocuments.subjectToTeach;
          tutorPreferencesDocuments.gender =
            tutorPreferencesData.gender || tutorPreferencesDocuments.gender;
          tutorPreferencesDocuments.onBoardingStatus =
            tutorPreferencesData.onBoardingStatus
              ? tutorPreferencesData.onBoardingStatus
              : tutorPreferencesDocuments.onBoardingStatus;
          tutorPreferencesDocuments.onBoardingStep =
            tutorPreferencesData.onBoardingStep ||
            tutorPreferencesDocuments.onBoardingStep;
          tutorPreferencesDocuments.quizScore =
            tutorPreferencesData.quizScore ||
            tutorPreferencesDocuments.quizScore;
          tutorPreferencesDocuments.attempts =
            tutorPreferencesData.attempts || tutorPreferencesDocuments.attempts;
          tutorPreferencesDocuments.currentVideo =
            tutorPreferencesData.currentVideo ||
            tutorPreferencesDocuments.currentVideo;
          if (
            tutorPreferencesData?.education &&
            tutorPreferencesData?.education.length > 0 &&
            tutorPreferencesData?.education[0].fieldOfStudy
          ) {
            if (!tutorPreferencesDocuments.education) {
              tutorPreferencesDocuments.education = [];
            }

            if (tutorPreferencesDocuments.education.length > 0) {
              tutorPreferencesDocuments.education[0].fieldOfStudy =
                tutorPreferencesData?.education[0].fieldOfStudy;
            } else {
              let fieldOfStudy =
                tutorPreferencesData?.education[0].fieldOfStudy;

              let newEducation = {
                school: "",
                degree: "",
                fieldOfStudy: fieldOfStudy,
                from: null,
                to: null,
                current: false,
                description: "",
              };
              tutorPreferencesDocuments.education.push(newEducation);
            }
          }
          // else {
          //   console.log("tutorPreferencesData education is null");
          // }
          if (
            tutorPreferencesData?.activity?.student
              ?.higherEducationEstablishment?.length
          ) {
            tutorPreferencesDocuments.activity.student.higherEducationEstablishment =
              tutorPreferencesData?.activity?.student?.higherEducationEstablishment[0];
          }

          if (tutorPreferencesData?.activity?.company?.length) {
            tutorPreferencesDocuments.activity.company = [];
            tutorPreferencesDocuments.activity.company.push(
              tutorPreferencesData?.activity?.company[0]
            );
          }
          //update establishment
          if (tutorPreferencesData.establishments) {
            const listOfEstablishments = tutorPreferencesData.establishments;
            let newListOfEstablishments = [];
            if (listOfEstablishments.length > 0) {
              listOfEstablishments.forEach(
                ({ establishmentId, establishmentName }) => {
                  let newEstablishment = {
                    establishmentId: establishmentId,
                    establishmentName: establishmentName,
                  };
                  newListOfEstablishments.push(newEstablishment);
                }
              );
            }
            if (newListOfEstablishments.length > 0) {
              tutorPreferencesDocuments.assignment.establishments =
                newListOfEstablishments;
            }
          }
          if (tutorPreferencesData.department) {
            tutorPreferencesDocuments.assignment.department =
              tutorPreferencesData.department;
          }
          //update level
          if (tutorPreferencesData.level) {
            const listOfLevels = tutorPreferencesData.level;
            let newListOfLevels = [];
            if (listOfLevels.length > 0) {
              listOfLevels.forEach((level) => {
                let newLevel = {
                  levelId: level,
                  levelName: "",
                };
                newListOfLevels.push(newLevel);
              });

              tutorPreferencesDocuments.assignment.level = newListOfLevels;
            }
          }

          await tutorPreferencesDocuments.save();
          // //add gender and birthDate from userModel to tutorPreferencesDocuments object
          // let genderAndBirthDate = {
          //      gender: user.gender,
          //      dateOfBirth: user.dateOfBirth,
          //      address: user.address,
          //      phoneNumber: user.contactDetails.phoneNumber,
          //      firstName: user.contactDetails.firstName,
          //      lastName: user.contactDetails.lastName,
          //      iAgreeToReceiveInformationAndUpdates: user.iAgreeToReceiveInformationAndUpdates,
          //      iAcceptTermsAndPolicyOfConfidentiality: user.iAcceptTermsAndPolicyOfConfidentiality,
          // };

          // const tutorPreferencesDocumentsData = tutorPreferencesDocumentsUpdate._doc;

          // const tutorPrefSomeData = {
          //      numberOfStudentsToSupport: tutorPreferencesDocumentsData.assignment.numberOfStudentsToSupport ? tutorPreferencesDocumentsData.assignment.numberOfStudentsToSupport : 0,
          //      level: tutorPreferencesDocumentsData.assignment.levelOfStudentsToSupported ? tutorPreferencesDocumentsData.assignment.levelOfStudentsToSupported : "",
          //      education: tutorPreferencesDocumentsData.education ? tutorPreferencesDocumentsData.education : "",
          //      attendInformationMeeting: tutorPreferencesDocumentsData.attendInformationMeeting ? tutorPreferencesDocumentsData.attendInformationMeeting : false,
          //      facilityName: tutorPreferencesDocumentsData.facilityName ? tutorPreferencesDocumentsData.facilityName : "",
          //      ufrCurriculum: tutorPreferencesDocumentsData.activity.ufrCurriculum ? tutorPreferencesDocumentsData.activity.ufrCurriculum : [],
          //      attendInformationMeeting: tutorPreferencesDocumentsData.attendInformationMeeting ? tutorPreferencesDocumentsData.attendInformationMeeting : false,
          //      availability: tutorPreferencesDocumentsData.availability ? tutorPreferencesDocumentsData.availability : [],
          //      subjects: tutorPreferencesDocumentsData.subjects ? tutorPreferencesDocumentsData.subjects : [],
          //      comments: tutorPreferencesDocumentsData.activity.comments ? tutorPreferencesDocumentsData.activity.comments : "",
          //      engagementLetter: tutorPreferencesDocumentsData.engagementLetter ? tutorPreferencesDocumentsData.engagementLetter : "",
          //      howDidYouHearAboutUs: tutorPreferencesDocumentsData.howDidYouHearAboutUs ? tutorPreferencesDocumentsData.howDidYouHearAboutUs : "",
          //      isYourCommitmentValuedByYourInstitution: tutorPreferencesDocumentsData.isYourCommitmentValuedByYourInstitution
          //           ? tutorPreferencesDocumentsData.isYourCommitmentValuedByYourInstitution
          //           : false,
          // };

          // //combine data from tutorPreferencesDocuments and genderAndBirthDate
          // let combineData = { ...tutorPrefSomeData, ...genderAndBirthDate };

          let response = apiResponse.response(
            true,
            `Tutor preferences updated successfully for userId: ${userId}`,
            `Tutor preferences updated successfully for userId: ${userId}`
          );
          return res.status(200).json(response);
        } else {
          //create new tutor preferences
          let tutorPreferencesDocuments = await tutorPreferences.create(
            tutorPreferencesData
          );
          tutorPreferencesDocuments.contactDetails =
            userUpdatedDocument.contactDetails;

          let response = apiResponse.response(
            true,
            `Tutor preferences created successfully for userId: ${userId}`,
            tutorPreferencesDocuments
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log(error);
        let errorMessage = `Error while onboarding tutor: ${error}`;
        console.log(errorMessage);
        let response = apiResponse.response(
          false,
          `Error while creating/updating tutor preferences`,
          errorMessage
        );
        return res.status(500).json(response);
      }
    });
  }
);

/**
 * @swagger
 * /api/v1/tutor/change-password:
 *   patch:
 *     summary: Change tutor's password
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to update password
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *                 description: New password
 *     responses:
 *       '200':
 *         description: Account password successfully updated
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.patch(
  "/tutor/change-password",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "UserId is required, please provide a valid userId",
          400
        );
        return res.status(400).json(response);
      }
      const user = await userHelper.getUserDetailsByUserId(userId);
      if (!user || !user.contactDetails.email) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found: ${userId}, please provide a valid userId`,
          400
        );
        return res.status(400).json(response);
      }
      const { password } = req.body;
      if (!password) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New password is empty`,
          400
        );
        return res.status(400).json(response);
      }

      const userRecord = await firebaseHelper.changePassword(
        user.contactDetails.email,
        password
      );

      if (!userRecord) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating password`,
          400
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Account password successfully updated`
      );
      return res.status(200).json(response);
    } catch (error) {
      let errorMessage = `Error while updating password: ${error}`;
      console.log(errorMessage);
      let response = apiResponse.response(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Error while getting tutor preferences",
        errorMessage
      );
      return res.status(500).json(response);
    }
  }
);

//get tutor preferences by userId and gender and birthDate from userModel
/**
 * @swagger
 * /api/v1/tutor/onboarding:
 *   get:
 *     summary: Get tutor onboarding details
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the user to get onboarding details
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor onboarding details
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/onboarding",
  // [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      //check if userId is present in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "UserId is required, please provide a valid userId",
          400
        );
        return res.status(400).json(response);
      }

      //get user details by userId
      const user = await userHelper.getUserDetailsByUserId(userId);
      //check if user exists
      if (!user) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found: ${userId}, please provide a valid userId`,
          400
        );
        return res.status(400).json(response);
      }

      //get user preferences by userId
      const tutorPreferencesDocuments = await tutorPreferences.findOne({
        userId: userId,
      });
      console.log(
        "tutorPreferencesDocuments",
        tutorPreferencesDocuments.assignment.establishments
      );
      const establishmentList = tutorPreferencesDocuments.assignment
        .establishments
        ? tutorPreferencesDocuments.assignment.establishments
        : [];
      let establishmentIdList = [];

      if (establishmentList.length > 0) {
        //return only establishmentId
        establishmentList.forEach((establishment) => {
          establishmentIdList.push(establishment.establishmentId);
        });
      }

      const levelList = tutorPreferencesDocuments.assignment.level
        ? tutorPreferencesDocuments.assignment.level
        : [];
      let levelIdList = [];
      if (levelList.length > 0) {
        levelIdList = levelList.map((level) => {
          return level.levelId;
        });
      }

      const tutorPrefSomeData = {
        numberOfStudentsToSupport: tutorPreferencesDocuments.assignment
          .numberOfStudentsToSupport
          ? tutorPreferencesDocuments.assignment.numberOfStudentsToSupport
          : 0,
        level: levelIdList,
        education: tutorPreferencesDocuments.education
          ? tutorPreferencesDocuments.education
          : "",
        attendInformationMeeting:
          tutorPreferencesDocuments.attendInformationMeeting
            ? tutorPreferencesDocuments.attendInformationMeeting
            : false,
        attendInformationMeetingDate:
          tutorPreferencesDocuments.attendInformationMeetingDate
            ? tutorPreferencesDocuments.attendInformationMeetingDate
            : null,
        facilityName: tutorPreferencesDocuments.facilityName
          ? tutorPreferencesDocuments.facilityName
          : "",
        ufrCurriculum: tutorPreferencesDocuments.activity.ufrCurriculum
          ? tutorPreferencesDocuments.activity.ufrCurriculum
          : [],
        attendInformationMeeting:
          tutorPreferencesDocuments.attendInformationMeeting
            ? tutorPreferencesDocuments.attendInformationMeeting
            : false,
        availability: tutorPreferencesDocuments.availability
          ? tutorPreferencesDocuments.availability
          : [],
        subjects: tutorPreferencesDocuments.subjects
          ? tutorPreferencesDocuments.subjects
          : [],
        comments: tutorPreferencesDocuments.activity.comments
          ? tutorPreferencesDocuments.activity.comments
          : "",
        // suiviCoordo: tutorPreferencesDocuments.activity.suiviCoordo
        //   ? tutorPreferencesDocuments.activity.suiviCoordo
        //   : "",
        homeDepartment: { ...tutorPreferencesDocuments.homeDepartment },
        engagementLetter: tutorPreferencesDocuments.engagementLetter
          ? tutorPreferencesDocuments.engagementLetter
          : "",
        howDidYouHearAboutUs: tutorPreferencesDocuments.howDidYouHearAboutUs
          ? tutorPreferencesDocuments.howDidYouHearAboutUs
          : "",
        howDidYourHeardAboutUsDetails:
          tutorPreferencesDocuments.howDidYourHeardAboutUsDetails
            ? tutorPreferencesDocuments.howDidYourHeardAboutUsDetails
            : "",
        isYourCommitmentValuedByYourInstitution:
          tutorPreferencesDocuments.isYourCommitmentValuedByYourInstitution,
        attendInformationMeetingDate:
          tutorPreferencesDocuments.attendInformationMeetingDate,
        onBoardingStatus: tutorPreferencesDocuments.onBoardingStatus,
        onBoardingStep: tutorPreferencesDocuments.onBoardingStep,
        quizScore: tutorPreferencesDocuments.quizScore,
        attempts: tutorPreferencesDocuments.attempts,
        establishments: establishmentIdList,
        status: tutorPreferencesDocuments.status,
        subjectToTeach: tutorPreferencesDocuments.subjectToTeach
          ? tutorPreferencesDocuments.subjectToTeach
          : "",
      };

      //add gender and birthDate from userModel to tutorPreferencesDocuments object
      let genderAndBirthDate = {
        gender: user.gender,
        dateOfBirth: user.dateOfBirth,
        address: user.address,
        phoneNumber: user.contactDetails.phoneNumber,
        firstName: user.contactDetails.firstName,
        lastName: user.contactDetails.lastName,
        iAgreeToReceiveInformationAndUpdates:
          user.iAgreeToReceiveInformationAndUpdates,
        iAcceptTermsAndPolicyOfConfidentiality:
          user.iAcceptTermsAndPolicyOfConfidentiality,
        iAcceptTheCommitmentLetter: user.iAcceptTheCommitmentLetter,
      };

      //combine data from tutorPreferencesDocuments and genderAndBirthDate
      let tutorPreferencesData;
      if (tutorPreferencesDocuments) {
        tutorPreferencesData = Object.assign(
          tutorPreferencesDocuments._doc,
          tutorPrefSomeData,
          genderAndBirthDate
        );
        if (tutorPreferencesData.documents) {
          tutorPreferencesData.documents = tutorHelper.encodeBase64(
            tutorPreferencesData.documents
          );
        }
      } else {
        tutorPreferencesData = genderAndBirthDate;
      }

      //return response
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Tutor preferences fetched successfully for userId: ${userId}`,
        tutorPreferencesData
      );

      return res.status(200).json(response);
    } catch (error) {
      let errorMessage = `Error while getting tutor preferences: ${error}`;
      console.log(errorMessage);
      let response = apiResponse.response(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Error while getting tutor preferences",
        errorMessage
      );
      return res.status(500).json(response);
    }
  }
);

//#region TUTOR DOCUMENTS

//upload documents for tutor
router.put(
  "/tutor/documents",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const form = new formidable.IncomingForm();
      form.parse(req, async (err, fields, files) => {
        if (err) {
          next(err);
          let response = apiResponse.response(false, err, null);
          return res.status(400).json(response);
        }
        //User id from the request
        const userId = fields.userId;
        const documentType = fields.documentType;
        const document = files?.document;

        //get document type from files object
        const fileType = document?.mimetype;

        //get document name from files object
        const documentName = document.originalFilename;

        //get document size from files object
        const fileSize = document.size;

        //check if userId is present in the request
        if (!userId) {
          let response = apiResponse.response(
            false,
            `UserId is null: ${userId}`,
            `UserId is required, please provide a valid userId`
          );
          return res.status(400).json(response);
        }

        //check if document is present in the request
        if (!document) {
          let response = apiResponse.response(
            false,
            `Document is null: ${document}`,
            `Document is required, please provide a valid document`
          );
          return res.status(400).json(response);
        }

        //check if document type is present in the request
        if (!documentType) {
          let response = apiResponse.response(
            false,
            `Document type is null: ${documentType}`,
            `Document type is required, please provide a valid document type`
          );
          return res.status(400).json(response);
        }

        //start uploading the document to firebase
        const firebaseFileUploadResponse = await firebaseFileHelper.uploadFile(
          document,
          firebaseConstants.BUCKET_TUTOR_DOCUMENTS
        );
        //check if the document is uploaded to firebase
        if (firebaseFileUploadResponse.status) {
          let uploadDocument = firebaseFileUploadResponse.data;
          let documentData = {
            documentName: documentName,
            downloadUrl: uploadDocument.download_url,
            documentType: documentType,
            documentId: uploadDocument.file_id,
            previewUrl: uploadDocument.preview_url,
            fileType: fileType,
            fileSize: fileSize,
          };

          //get the tutor preferences from the database
          const tutorPreferencesDocuments = await tutorPreferences
            .findOne({ userId: userId })
            .exec();

          //check if the tutor preferences are present in the database
          if (tutorPreferencesDocuments) {
            //check if the documents are present in the tutor preferences
            if (tutorPreferencesDocuments.documents) {
              //push the document to the documents array
              tutorPreferencesDocuments.documents.push(documentData);
              await tutorPreferences.findOneAndUpdate(
                {
                  userId: userId,
                },
                {
                  documents: tutorPreferencesDocuments.documents,
                },
                {
                  upsert: true,
                  new: true,
                }
              );
            } else {
              console.log("Adding document to new documents");
              tutorPreferencesDocuments.documents = [documentData];
              //update the tutor preferences
              await tutorPreferences
                .findOneAndUpdate({
                  userId: userId,
                })
                .updateOne({
                  documents: tutorPreferencesDocuments.documents,
                });
            }

            let response = apiResponse.response(
              true,
              `Document uploaded successfully`,
              documentData
            );
            return res.status(200).json(response);
          } else {
            let response = apiResponse.response(
              false,
              `Tutor preferences not found for userId: ${userId}`,
              null
            );
            return res.status(400).json(response);
          }
        }
      });
    } catch (err) {
      let apiData = apiResponse.response(
        false,
        `Error in uploading documents for tutor: ${err}`,
        ""
      );
      return res.status(400).json(apiData);
    }
  }
);

//get all documents for tutor by userId
router.get(
  "/tutor/documents",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is present in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null: ${userId}`,
          `UserId is required, please provide a valid userId`
        );
        return res.status(400).json(response);
      }

      //get the tutor preferences from the database
      const tutorPreferencesDocuments = await tutorPreferences
        .findOne({ userId: userId })
        .select("documents")
        .exec();
      const docs = tutorHelper.encodeBase64(
        tutorPreferencesDocuments.documents
      );
      //check if the tutor preferences are present in the database
      if (!tutorPreferencesDocuments) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No documents found for userId: ${userId}`,
          `Please upload documents for userId: ${userId}`
        );

        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Documents found for userId: ${userId}`,
        docs
      );

      return res.status(200).json(response);
    } catch (error) {
      response;
      let apiData = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting tutor documents: ${error}`,
        ""
      );
      return res.status(500).json(apiData);
    }
  }
);

//delete document for tutor
router.delete(
  "/tutor/documents",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const form = new formidable.IncomingForm();
      form.parse(req, async (err, fields, files) => {
        if (err) {
          next(err);
          let response = apiResponse.response(false, err, null);
          return res.status(400).json(response);
        }
        //User id from the request
        const userId = fields.userId;
        const documentId = fields.documentId;

        //check if userId is present in the request
        if (!userId) {
          let response = apiResponse.response(
            false,
            `UserId is null: ${userId}`,
            `UserId is required, please provide a valid userId`
          );
          return res.status(400).json(response);
        }

        //check if document id is present in the request
        if (!documentId) {
          let response = apiResponse.response(
            false,
            `Document id is null: ${documentId}`,
            `Document id is required, please provide a valid document id`
          );
          return res.status(400).json(response);
        }

        //get the tutor preferences from the database
        const tutorPreferencesDocuments = await tutorPreferences
          .findOne({
            userId: userId,
          })
          .exec();

        //check if the tutor preferences are present in the database
        if (tutorPreferencesDocuments) {
          //check if the documents are present in the tutor preferences
          if (tutorPreferencesDocuments.documents) {
            //filter the document from the documents array

            let isDeletedFile = await firebaseFileHelper.deleteFile(
              firebaseConstants.BUCKET_TUTOR_DOCUMENTS,
              documentId
            );
            if (isDeletedFile.status) {
              let listOfDocuments = tutorPreferencesDocuments.documents;

              //delete the document from the documents array
              listOfDocuments.forEach((document, index) => {
                if (document.documentId == documentId) {
                  listOfDocuments.splice(index, 1);
                }
              });

              tutorPreferencesDocuments.documents = listOfDocuments;

              await tutorPreferencesDocuments.save();

              let response = apiResponse.response(
                true,
                `Document is deleted successfully`,
                `Don't worry, you can upload the document again ;)`
              );
              return res.status(200).json(response);
            } else {
              let response = apiResponse.response(
                false,
                `Error in deleting document`,
                `Document is not deleted, no such document found in the database with this documentId: ${documentId}`
              );
              return res.status(400).json(response);
            }
          } else {
            let response = apiResponse.response(
              false,
              `Documents not found for userId: ${userId}`,
              null
            );
            return res.status(400).json(response);
          }
        } else {
          let response = apiResponse.response(
            false,
            `Tutor preferences not found for userId: ${userId}`,
            null
          );
          return res.status(400).json(response);
        }
      });
    } catch (err) {
      let apiData = apiResponse.response(
        false,
        `Error in deleting documents for tutor: ${err}`,
        ""
      );
      return res.status(400).json(apiData);
    }
  }
);

//#endregion

//get combined dashboard data for tutor
/**
 * @swagger
 * /api/v1/tutor/dashboard:
 *   get:
 *     summary: Get tutor dashboard data
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the tutor to get dashboard data
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor dashboard data
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/dashboard",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    try {
      const tutorUserId = req.query.userId;

      //check if userId is present in the request
      if (!tutorUserId) {
        let response = apiResponse.response(
          false,
          `UserId is null: ${tutorUserId}`,
          `UserId is required, please provide a valid userId`
        );
        return res.status(400).json(response);
      }

      //get next session
      const nextSession = await sessionTutorHelper.getTop3Sessions(tutorUserId);
      console.log("nextSession", nextSession);
      //get active students
      const activeStudents =
        await sessionTutorHelper.getActiveStudentsBaseOnTutor(tutorUserId);
      console.log("activeStudents", activeStudents);
      //get reports
      // const reports = await tutorHelper.getTutorReports(tutorUserId);

      //get the right Report & information
      const numberOfSession =
        await sessionTutorHelper.getAllSessionForTutorAsNumber(tutorUserId);
      const numberOfActiveStudents = activeStudents.data?.length
        ? activeStudents.data.length
        : 0;
      const reportDashboard =
        await sessionTutorHelper.reportDataForTutorDashboard(tutorUserId);

      let tutorRightReports = {
        sessions: numberOfSession.data,
        students: numberOfActiveStudents,
        myTrainingPath: "",
        reportDashboard: reportDashboard.data,
      };

      //combine all the data
      let dashboardData = {
        activeStudents: activeStudents.data,
        nextSession: nextSession.data,
        rightReport: tutorRightReports,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Tutor dashboard data `,
        dashboardData
      );

      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let apiData = apiResponse.response(
        false,
        `Error in getting dashboard data for tutor: ${error}`,
        ""
      );
      return res.status(400).json(apiData);
    }
  }
);

//get tutor by tutorId
/**
 * @swagger
 * /api/v1/tutor/detail:
 *   get:
 *     summary: Get tutor detail
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: tutorId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the tutor to get detail
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor detail
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/detail",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([ROLE_TUTOR, VSC, SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res) => {
    try {
      const tutorId = req.query.tutorId;
      let tutorPreferences = await TutorPreferencesModel.findOne({
        userId: tutorId,
      });
      const tutor = tutorPreferences.toObject();
      if (tutor.documents) {
        delete tutor.documents; // Supprime la propriété 'documents'
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `tutor Detail.`,
        tutor
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting tutor detail.`,
        500
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

// Get all students for tutor active and inactive
/**
 * @swagger
 * /api/v1/tutor/myStudents:
 *   get:
 *     summary: Get tutor's students
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the tutor to get students
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor's students
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/myStudents",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR,SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC])],
  async (req, res) => {
    try {
      const tutorUserId = req.query.userId;

      //check if userId is present in the request
      if (!tutorUserId) {
        let responseData = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null or empty: ${tutorUserId}`,
          `UserId is required, please provide a valid userId`,
          400
        );
        return res.status(400).json(responseData);
      }

      //get active students
      const activeStudents =
        await sessionTutorHelper.getActiveStudentsBaseOnTutor(tutorUserId);
      //get inactive students
      const studentsFollowed = [];

      //#region Right Report
      let nextSession = await sessionTutorHelper.getTop3Sessions(tutorUserId);

      let numberOfSession =
        await sessionTutorHelper.getAllSessionForTutorAsNumber(tutorUserId);

      let numberOfActiveStudents = activeStudents?.data?.length;

      const reportDashboard =
        await sessionTutorHelper.reportDataForTutorDashboard(tutorUserId);

      const rightReport = {
        nextSession: nextSession.data,
        numberOfSession: numberOfSession.data,
        numberOfActiveStudents: numberOfActiveStudents,
        trainingPath: "",
        reportDashboard: reportDashboard.data,
      };

      let combinedDataPerStudent = {
        activeStudents: activeStudents.data,
        studentsFollowed: studentsFollowed,
        rightReport: rightReport,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Dashboard data fetched successfully`,
        combinedDataPerStudent
      );
      return res.status(200).json(response);
    } catch (error) {
      let apiData = apiResponse.response(
        false,
        `Error in getting dashboard data for tutor: ${error}`,
        ""
      );
      return res.status(400).json(apiData);
    }
  }
);

//get all my sessions for tutor
/**
 * @swagger
 * /api/v1/tutor/mySessions:
 *   get:
 *     summary: Get tutor's sessions
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the tutor to get sessions
 *       - in: query
 *         name: startDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: Start date of the range to filter sessions (YYYY-MM-DD)
 *       - in: query
 *         name: endDate
 *         required: true
 *         schema:
 *           type: string
 *           format: date
 *         description: End date of the range to filter sessions (YYYY-MM-DD)
 *       - in: query
 *         name: range
 *         required: false
 *         schema:
 *           type: string
 *         description: Range to filter sessions (e.g., 'week', 'month', 'year')
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor's sessions
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/mySessions",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR,SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC])],
  async (req, res) => {
    try {
      const tutorUserId = req.query.userId;
      const startDate = req.query.startDate;
      const endDate = req.query.endDate;
      const range = req.query.range;
      const status = req.query.status; // Nouveau paramètre pour filtrer par statut

      //check if userId is present in the request
      if (!tutorUserId) {
        let responseData = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null or empty: ${tutorUserId}`,
          `UserId is required, please provide a valid userId`,
          400
        );
        return res.status(400).json(responseData);
      }

      //get all my sessions for tutor based on range in table StudentPreferences
      const allMySessions = await sessionTutorHelper.getAllSession(
        tutorUserId,
        startDate,
        endDate,
        status // Passer le statut à la fonction helper
      );

      //return response
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `All my sessions fetched successfully`,
        allMySessions.data,
        ""
      );
      return res.status(200).json(response);
    } catch (error) {
      let apiData = apiResponse.response(
        false,
        `Error in getting dashboard data for tutor: ${error}`,
        ""
      );
      console.log(apiData);
      return res.status(400).json(apiData);
    }
  }
);

//Tutor mySpace
/**
 * @swagger
 * /api/v1/tutor/mySpace:
 *   get:
 *     summary: Get tutor's space data
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: ID of the tutor to get space data
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor's space data
 *       '400':
 *         description: Bad request
 *       '404':
 *         description: Tutor space not found
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/mySpace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    try {
      const tutorUserId = req.query.userId;

      //check if userId is present in the request
      if (!tutorUserId) {
        let responseData = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null or empty: ${tutorUserId}`,
          `UserId is required, please provide a valid userId`,
          400
        );
        return res.status(400).json(responseData);
      }

      const tutorSpace = await tutorHelper.getTutorSpaceData(tutorUserId);
      if (tutorSpace.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Tutor mySpace fetched successfully`,
          tutorSpace.data,
          ""
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Tutor space not found`,
          "",
          404
        );
        return res.status(404).json(response);
      }
    } catch (error) {
      let apiData = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting Tutor mySpace: ${error}`,
        "",
        400
      );
      console.log(error);
      return res.status(400).json(apiData);
    }
  }
);

//Update Tutor mySpace
/**
 * @swagger
 * /api/v1/tutor/mySpace:
 *   put:
 *     summary: Update tutor's space data
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     consumes:
 *       - multipart/form-data
 *     parameters:
 *       - in: formData
 *         name: userId
 *         required: true
 *         type: string
 *         description: ID of the tutor to update space data
 *       - in: formData
 *         name: mySpace
 *         required: true
 *         type: string
 *         description: JSON string containing updated space data
 *     responses:
 *       '200':
 *         description: Successfully updated tutor's space data
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.put(
  "/tutor/mySpace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const mySpace = JSON.parse(fields.mySpace);

        //check if userId is present in the request
        if (!mySpace.userId) {
          let responseData = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `UserId is null or empty: ${mySpace.userId}`,
            `UserId is required, please provide a valid userId`,
            400
          );
          return res.status(400).json(responseData);
        }

        //Get user details
        const userDocument = await userHelper.getUserDetailsByUserId(
          mySpace.userId
        );

        //check if userDocument is null
        if (!userDocument) {
          let responseData = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User has no data`,
            `Please provide a valid userId`,
            400
          );
          return res.status(400).json(responseData);
        }

        userDocument.contactDetails.firstName = mySpace.firstName
          ? mySpace.firstName
          : userDocument.contactDetails.firstName;
        userDocument.contactDetails.lastName = mySpace.lastName
          ? mySpace.lastName
          : userDocument.contactDetails.lastName;
        userDocument.contactDetails.phoneNumber = mySpace.phoneNumber
          ? mySpace.phoneNumber
          : userDocument.contactDetails.phoneNumber;
        userDocument.dateOfBirth = mySpace.dateOfBirth
          ? mySpace.dateOfBirth
          : userDocument.dateOfBirth;
        userDocument.gender = mySpace.gender
          ? mySpace.gender
          : userDocument.gender;
        userDocument.contactDetails.phoneNumber = mySpace.phoneNumber
          ? mySpace.phoneNumber
          : userDocument.contactDetails.phoneNumber;
        userDocument.address = mySpace.address
          ? mySpace.address
          : userDocument.address.addressLine1;
        await userDocument.save();

        //get Tutor Preferences
        const tutorPreferencesDocument = await tutorHelper.getTutorPreferences(
          mySpace.userId
        );

        //update Tutor Preferences

        if (tutorPreferencesDocument.ufrCurriculum) {
          console.log(
            "ufrCurriculum is not null",
            tutorPreferencesDocument.ufrCurriculum
          );
          tutorPreferencesDocument.activity.ufrCurriculum.push(
            mySpace.ufrCurriculum
          );
        }
        if (!tutorPreferencesDocument.education) {
          let education = {
            fieldOfStudy: mySpace.fieldOfStudy ? mySpace.fieldOfStudy : "",
          };
          tutorPreferencesDocument.education = education;
        }
        tutorPreferencesDocument.facilityName = mySpace.facilityName
          ? mySpace.facilityName
          : tutorPreferencesDocument.facilityName
          ? tutorPreferencesDocument.facilityName
          : "";
        tutorPreferencesDocument.typeOfTutor = mySpace.typeOfTutor
          ? mySpace.typeOfTutor
          : tutorPreferencesDocument.typeOfTutor;

        tutorPreferencesDocument.activity.tutorActivityStatus =
          mySpace.tutorActivityStatus
            ? mySpace.tutorActivityStatus
            : tutorPreferencesDocument.activity.tutorActivityStatus;

        tutorPreferencesDocument.isYourCommitmentValuedByYourInstitution =
          mySpace.isYourCommitmentValuedByYourInstitution
            ? mySpace.isYourCommitmentValuedByYourInstitution
            : tutorPreferencesDocument.isYourCommitmentValuedByYourInstitution;
        tutorPreferencesDocument.howDidYouHearAboutUs =
          mySpace.howDidYouHearAboutUs
            ? mySpace.howDidYouHearAboutUs
            : tutorPreferencesDocument.howDidYouHearAboutUs;
        tutorPreferencesDocument.assignment.numberOfStudentsToSupport =
          mySpace.numberOfStudentsToSupport
            ? mySpace.numberOfStudentsToSupport
            : tutorPreferencesDocument.assignment.numberOfStudentsToSupport
            ? tutorPreferencesDocument.assignment.numberOfStudentsToSupport
            : "";

        tutorPreferencesDocument.availability = mySpace.availability
          ? mySpace.availability
          : tutorPreferencesDocument.availability;
        tutorPreferencesDocument.materials = mySpace.materials
          ? mySpace.materials
          : tutorPreferencesDocument.materials
          ? tutorPreferencesDocument.materials
          : "";
        tutorPreferencesDocument.activity.comments = mySpace.comments
          ? mySpace.comments
          : tutorPreferencesDocument.activity.comments
          ? tutorPreferencesDocument.activity.comments
          : "";
        // tutorPreferencesDocument.activity.suiviCoordo = mySpace.suiviCoordo
        //   ? mySpace.suiviCoordo
        //   : tutorPreferencesDocument.activity.suiviCoordo
        //   ? tutorPreferencesDocument.activity.suiviCoordo
        //   : "";
        await tutorPreferencesDocument.save();

        //get Tutor mySpace after update
        const tutorSpace = await tutorHelper.getTutorSpaceData(mySpace.userId);
        //return response
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Data updated successfully`,
          tutorSpace.data,
          ""
        );
        return res.status(200).json(response);
      } catch (error) {
        let apiData = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error in updating Tutor mySpace: ${error}`,
          "",
          400
        );
        console.log(error);
        return res.status(400).json(apiData);
      }
    });
  }
);

//Tutor autocomplete search
/**
 * @swagger
 * /api/v1/tutor/search:
 *   get:
 *     summary: Search for tutors
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         required: true
 *         schema:
 *           type: string
 *         description: Search query to find tutors by first name or last name
 *     responses:
 *       '200':
 *         description: Successfully fetched tutors matching the search query
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/search",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const search = req.query.search;
      const program = req.query.program;
      const userRole = req.userRole;
      const userId = req.userId;
      const admin = await userAdmin.find({ userId: userId });
      const estabid = admin.flatMap((user) =>
        user.administrationPreferences.establishment.map(
          (establishment) => establishment.establishmentId
        )
      );
      //check if search is provided
      if (!search) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Search is required.`,
          `Please provide search.`
        );
        return res.status(400).json(response);
      }
      if (!program) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Program is required.`,
          `Please provide program.`
        );
        return res.status(400).json(response);
      }
      const query =
        program === DEVOIRS_FAITS.programId
          ? {
              $and: [
                { "program.programId": program },

                userRole == COORDINATOR || userRole == VSC
                  ? {
                      "assignment.establishments": {
                        $elemMatch: {
                          establishmentId: { $in: estabid },
                        },
                      },
                    }
                  : {},
                {
                  $or: [
                    {
                      "contactDetails.firstName": {
                        $regex: search,
                        $options: "i",
                      },
                    },
                    {
                      "contactDetails.lastName": {
                        $regex: search,
                        $options: "i",
                      },
                    },
                  ],
                },
              ],
            }
          : {
              $and: [
                { "program.programId": { $in: onlineProg } },
                {
                  $or: [
                    {
                      "contactDetails.firstName": {
                        $regex: search,
                        $options: "i",
                      },
                    },
                    {
                      "contactDetails.lastName": {
                        $regex: search,
                        $options: "i",
                      },
                    },
                  ],
                  $expr: {
                    $gt: [
                      "$assignment.numberOfStudentsToSupport",
                      { $size: "$matchedStudents" },
                    ],
                  },
                },
              ],
            };
      query.status = statusOfTutors.TUTOR_ACTIVE;
      query.situation = { $nin: tutorSitution };
      const tutors = await tutorPreferences
        .find(query)
        .select(
          "contactDetails.firstName contactDetails.lastName userId userId"
        ) // select only firstName, lastName, and userId fields
        .exec();
      if (!tutors) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No tutors found.`,
          "Please provide a valid search."
        );
        return res.status(400).json(response);
      }

      const merged = tutors.map((tutors) => {
        const { firstName, lastName } = tutors.contactDetails;
        const userId = tutors.userId;
        return { firstName, lastName, userId };
      });

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Tutors found successfully.`,
        merged
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting tutors: ${error}`
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//#region REEPORTS
/**
 * @swagger
 * /api/v1/tutor/reports/dashboard:
 *   get:
 *     summary: Get dashboard reports for a tutor
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the tutor to fetch reports for
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: The page number for pagination
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *         description: The number of items per page for pagination
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for reports
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Field to sort by
 *     responses:
 *       '200':
 *         description: Successfully fetched tutor reports
 *       '400':
 *         description: Bad request
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/tutor/reports/dashboard",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      let page = apiResponse.buildPage(req.query.page);
      let pageSize = apiResponse.buildPageSize(req.query.pageSize);
      let filter = req.query.filter;
      let sortBy = req.query.sortBy;
      let listOfAllSessions;
      //check if userId is present in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null: ${userId}`,
          `UserId is required, please provide a valid userId`
        );
        return res.status(200).json(response);
      }

      //get reports from helper
      let listOfSessions = await sessionTutorHelper.getAllPastSessions(
        userId,
        page,
        pageSize,
        filter,
        sortBy
      );

      if (!listOfSessions.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No reports found for userId: ${userId}`,
          `Please provide a valid userId`
        );
        return res.status(200).json(response);
      }
      listOfAllSessions = listOfSessions?.data;

      let listOfReports = [];
      //for each report, get the student details
      for (let session of listOfAllSessions) {
        const tutor = session.tutors.find(
          (item) => String(item.userId) === String(userId)
        );

        let reportStatus =
          reportConstants.crScReportsStatus.toBeEntered.crScReportsStatusId;
        let reportId = "";
        let entry_date = "";

        if (tutor) {
          reportStatus = tutor.report?.status || reportStatus;
          reportId = tutor.report?.reportId || "";

          if (reportId) {
            const reportItem = await reportsModel.findById(reportId).exec();
            entry_date = reportItem?.entry_date;
          }
        }

        const newItem = {
          sessionId: session.sessionId,
          sessionType: session.sessionType,
          entry_date: entry_date,
          session_date: session.sessionDate.startDate,
          reportStatus: reportStatus,
          reportId: reportId,
          students: session.students,
          vsc: session.vsc,
        };

        if (
          newItem.reportStatus ===
          reportConstants.crScReportsStatus.toBeEntered.crScReportsStatusId
        ) {
          listOfReports.push(newItem);
        }
      }
      const result = await crReportsModel.aggregate([
        {
          $lookup: {
            from: "sessions", // Le nom de la collection "sessions"
            localField: "sessionId", // Le champ dans "crReports" qui référence la session
            foreignField: "sessionId", // Le champ dans "sessions" qui correspond à "sessionId"
            as: "sessionData", // Le nom du champ où les données de la session seront stockées
          },
        },
        {
          $match: {
            "sessionData.0": { $exists: true },
            "tutors.userId": userId, // Filtre pour ne garder que les comptes-rendus avec une session correspondante
          },
        },
        {
          $facet: {
            paginatedResults: [
              // { $skip: skip }, // Appliquer le saut
              // { $limit: pageSize } // Appliquer la limite
            ],
            totalCount: [
              { $count: "count" }, // Compter le nombre total de sessions
            ],
          },
        },
      ]);
      const listOfReportsEntredConfirmed = result[0]?.paginatedResults;
      listOfAllSessions = listOfSessions?.data;
      for (let report of listOfReportsEntredConfirmed) {
        const newItem = {
          sessionId: report.sessionId,
          sessionType: report.sessionType,
          entry_date: report.entry_date,
          session_date: report.session_date,
          reportStatus: report.status,
          reportId: report._id,
          students: report.students,
          vsc: report.vsc,
        };
        listOfReports.push(newItem);
      }
      const totalOfPastSessions = listOfReports.length;
      //return response
      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of reports for Tutor: ${userId}`,
        listOfReports,
        1,
        pageSize,
        totalOfPastSessions
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting reports: ${error}`
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//Tutor Saved Date
/**
 * @swagger
 * /api/v1/tutor/save-dates:
 *   put:
 *     summary: Update start and end dates for tutor commitment
 *     tags: [Tutor]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the tutor
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               startDate:
 *                 type: string
 *                 format: date
 *                 description: The start date for the tutor's commitment
 *               endDate:
 *                 type: string
 *                 format: date
 *                 description: The end date for the tutor's commitment
 *     responses:
 *       '200':
 *         description: Successfully updated tutor commitment dates
 *       '400':
 *         description: Bad request
 *       '404':
 *         description: Tutor space not found
 *       '500':
 *         description: Internal server error
 */
router.put(
  "/tutor/save-dates",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const { startDate, endDate } = req.body;
      //check if userId is present in the request
      if (!userId) {
        let responseData = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is null or empty: ${userId}`,
          `UserId is required, please provide a valid userId`,
          400
        );
        return res.status(400).json(responseData);
      }

      // let tutorPreferencesDocument = await tutorPreferences.findOne({ userId: userId });
      const result = await tutorPreferences.updateOne(
        { userId: userId },
        {
          $set: {
            "commitment.endDate": endDate,
            "commitment.startDate": startDate,
          },
        }
      );

      if (result.matchedCount === 0) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Tutor space not found`,
          "",
          404
        );
        return res.status(404).json(response);
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Tutor date successfull updated`
      );
      return res.status(200).json(response);
    } catch (error) {
      let apiData = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting Tutor mySpace: ${error}`,
        "",
        400
      );
      console.log(error);
      return res.status(400).json(apiData);
    }
  }
);
router.post(
  "/tutor/send-invitation",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, VSC]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields) => {
      try {
        const { email, userRole } = fields;
        // Check if email is present in the request
        if (!email) {
          return res
            .status(400)
            .json(
              apiResponse.response(
                false,
                `Email is null: ${email}`,
                `Email is required, please provide a valid email`
              )
            );
        }

        // Check if userRole is present in the request
        if (!userRole) {
          return res
            .status(200)
            .json(
              apiResponse.response(
                false,
                `userRole is null: ${userRole}`,
                `userRole is required, please provide a valid userRole`
              )
            );
        }

        // Set userHelper and the error message based on the userRole

        const userHelperMethod = userHelper.getUserDetailsFromEmail;

        // Get user details from email
        let userDetails = await userHelperMethod(email);
        if (!userDetails) {
          return res
            .status(200)
            .json(
              apiResponse.responseWithStatusCode(
                false,
                `User is not found with email ${email}`,
                `Please provide a valid email`,
                apiResponse.apiConstants.USER_NOT_FOUND
              )
            );
        }
        // Send invitation email to userAdministration
        const setupInvitation = await tutorHelper.sendInvitationEmailToTutor(
          userDetails
        );
        try {
          await sendGrid.send(setupInvitation.setupInvitation);
          return res
            .status(200)
            .json(
              apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_SUCCESS,
                `Invitation sent successfully`,
                `Invitation sent successfully to ${setupInvitation.email}, please notify user-admin to check email. Invitation code: ${setupInvitation.invitationCode}`,
                apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESS
              )
            );
        } catch (error) {
          console.error(error);
          return res
            .status(500)
            .json(
              apiResponse.responseWithStatusCode(
                false,
                `Error while sending invitation email`,
                error,
                apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
              )
            );
        }
      } catch (error) {
        console.error("Error while sending invitation email", error);
        return res
          .status(500)
          .json(
            apiResponse.responseWithStatusCode(
              false,
              `Error while sending invitation email`,
              error,
              apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
            )
          );
      }
    });
  }
);

//#endregion

//export router
module.exports = router;