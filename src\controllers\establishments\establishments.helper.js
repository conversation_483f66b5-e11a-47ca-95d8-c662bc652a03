//import userConstants

const educationAnnuaireSchema = require("../../models/EducationAnnuaire.Model");

const mongoose = require("mongoose");
const EducationAnnuaireModel = require("../../models/EducationAnnuaire.Model");

//get establishment name from establishment id
exports.getEstablishmentName = async (establishmentId) => {
  try {
    const objectId = mongoose.Types.ObjectId(establishmentId);

    const establishment = await EducationAnnuaireModel.findOne({
      _id: objectId,
    }).select("establishmentName generalInformation.address.city");
    if (establishment) {
      const establishmentNameWithCity = `${establishment.establishmentName} (${establishment.generalInformation.address.city})`;
      return establishmentNameWithCity;
    } else {
      return null;
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

exports.getNumberOfHigherInstitutionsPerSector = async (sectorId) => {
  try {
    //convert sectorId from ObjectId to string
    sectorId = sectorId.toString();
    //count total number of students  per sector
    const numberOfInstitutions = await educationAnnuaireSchema
      .aggregate([
        {
          $match: {
            "generalInformation.sector.sectorId": sectorId,
            "typeOfEstablishment.typeOfEstablishmentName": {
              $in: [
                "business-school",
                "engineering-school",
                "iut",
                "university",
                "ufr",
              ],
            },
          },
        },
        {
          $count: "totalInstitutions",
        },
      ])
      .exec();

    return {
      status: true,
      message: "Number of students per sector",
      data: numberOfInstitutions.length
        ? numberOfInstitutions[0].totalInstitutions.toString()
        : "0",
    };
  } catch (error) {
    console.log("studentHelper.getNumberOfStudentsPerSector", error);
    return {
      status: false,
      message: `Error in getting number of students per sector ${error.message}`,
      data: 0,
    };
  }
};

exports.getNumberOfCollege = async (sectorId) => {
  try {
    sectorId = sectorId.toString();
    const numberCollege = await educationAnnuaireSchema
      .aggregate([
        {
          $match: {
            "generalInformation.sector.sectorId": sectorId,
            "typeOfEstablishment.typeOfEstablishmentName": {
              $in: ["elementary-school", "middle-school", "high-school"],
            },
          },
        },
        {
          $count: "totalColleges",
        },
      ])
      .exec();
    return numberCollege.length
      ? numberCollege[0].totalColleges.toString()
      : "0";
  } catch (error) {
    console.log("studentHelper.getNumberOfCollege", error);
    return {
      status: false,
      message: `Error in getting number of college per sector ${error.message}`,
      data: 0,
    };
  }
};

exports.getNumberOfStructure = async (sectorId) => {
  try {
    sectorId = sectorId.toString();
    const numberStructure = await educationAnnuaireSchema
      .aggregate([
        {
          $match: {
            "generalInformation.sector.sectorId": sectorId,
            "typeOfEstablishment.typeOfEstablishmentName": {
              $in: ["company", "association", "other"],
            },
          },
        },
        {
          $count: "totalStructures",
        },
      ])
      .exec();
    return numberStructure.length
      ? numberStructure[0].totalStructures.toString()
      : "0";
  } catch (error) {
    console.log("studentHelper.getNumberOfCollege", error);
    return {
      status: false,
      message: `Error in getting number of college per sector ${error.message}`,
      data: 0,
    };
  }
};
