const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("publicHolidays.routes.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import user Role Model
const userRoleModel = require("../models/UserRoles.Model.js");

//import userRoleHelper.js from path src/controllers/user/userRole.Helper.js
const userRoleHelper = require("../controllers/user/userRole.Helper.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");
const { SUPER_ADMINISTRATOR } = require("../utils/constants/userRolesConstants.js");


/**
 * @swagger
 * /api/v1/userRole:
 *   post:
 *     summary: Create a new user role
 *     description: Endpoint to create a new user role.
 *     tags:
 *       - User Roles
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userRole:
 *                 type: string
 *                 description: JSON string representing the user role data.
 *     responses:
 *       200:
 *         description: New user role created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: New user role saved successfully.
 *                 data:
 *                   type: object
 *                   description: Newly created user role data.
 *       '400':
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 400
 *                 message:
 *                   type: string
 *                   example: User role is required to perform this action. Please provide a valid user role.
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: Failed to save new user role.
 */
router.post("/userRole", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR])], async (req, res, next) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const userRoleJson = fields.userRole;

               //check if user role is provided
               if (!userRoleJson) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `User role is required to perform this action. Please provide a valid user role.`);
                    return res.status(400).json(response);
               }

               const userRole = JSON.parse(userRoleJson);

               //check if user role already exists
               const userRoleExists = await userRoleHelper.checkIfUserRoleExists(userRole);
               if (userRoleExists) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `User role already exists. Please provide a valid user role.`);
                    return res.status(400).json(response);
               }

               const mongoDBModelList = mongodbModelConstants.getModelNameAsObject();
               console.log(mongoDBModelList);
               let accessListData = [];
               mongoDBModelList.forEach((mongoDBModel) => {
                    if (userRole.userRole === "super_administrator") {
                         let item = { name: mongoDBModel, accessPrivilege: "RW" };
                         accessListData.push(item);
                    } else {
                         let item = { name: mongoDBModel, accessPrivilege: "N" };
                         accessListData.push(item);
                    }
               });

               //save new user role
               const newUserRole = await userRoleModel.create({
                    userRole: userRole.userRole,
                    userRoleDescription: userRole.userRoleDescription,
                    userRoleStatus: userRole.userRoleStatus,
                    accessList: accessListData,
               });

               let userRoleSaved = await newUserRole.save();

               if (userRoleSaved) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `New user role saved successfully.`, userRoleSaved);
                    return res.status(200).json(response);
               } else {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Failed to save new user role.`);
                    return res.status(500).json(response);
               }
          } catch (error) {
               console.log(error);
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Failed to save new user role.`, JSON.stringify(error));
               logger.newLog(logger.getLoggerTypeConstants().saveNewUserRole).error(response);
               return res.status(500).json(response);
          }
     });
});

//export router
module.exports = router;
