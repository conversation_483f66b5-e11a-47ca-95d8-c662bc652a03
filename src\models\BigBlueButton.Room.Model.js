const mongoose = require("mongoose");

//import dateTime helper from utils/aarifiDateTimeHelper/zupDeco.dateTimeHelper.js
const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const bigBlueButtonRoomSchema = new mongoose.Schema(
     {
          meetingID: String,
          internalMeetingID: String,
          name: String,
          attendeePW: String,
          moderatorPW: String,
          welcome: String,
          moderatorOnlyMessage: String,
          maxParticipants: {
               type: Number,
               default: 100,
          },
          record: {
               type: Boolean,
               default: true,
          },
          allowStartStopRecording: {
               type: Boolean,
               default: true,
          },
          autoStartRecording: {
               type: Boolean,
               default: false,
          },
          duration: {
               type: Number,
               default: 525600,
          },
          createdAt: {
               type: Date,
               default: dateTimeHelper.getCurrentDateTimeInParisZone(),
          },
     },
     { versionKey: false }
);

module.exports = mongoose.model("bigBlueButtonRoom", bigBlueButtonRoomSchema);
