const sessionModel = require("../../models/Sessions.Model.js");

const sessionsConstants = require("../../utils/constants/sessions.constants.js");

const userHelper = require("../user/User.Helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const sectorHelper = require("../sectors/sectors.helper.js");

const studentHelper = require("../students/student.helpers.js");

const tutorHelper = require("../tutor/tutor.helper.js");

const sessionsStudentHelper = require("./sessions.student.helper.js");

//get next session for student based on  userId, just 3 sessions
exports.getNextSessionsPerStudent = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return { status: false, message: "Error while getting next sessions" };
    }

    //get current timestamp in paris zone
    const currentTimestamp = dateTimeHelper.getCurrentDateInParisZone();

    //get next session for student, next session is a session that is scheduled for today or in the future  but not before the current time
    let listOfNextSession = [];
    for (let i = 0; i < studentsList.data.length; i++) {
      const student = studentsList.data[i];
      const userId = student.userId;

      console.log("userId Parent", userId);

      // zu-3a80a094-b4bd-415b-8e81-375a4b57e345

      //get next session for student
      const nextSession = await sessionModel
        .find({
          students: {
            $elemMatch: {
              userId: userId,
              // , absence: false
            },
          },
          "sessionDate.startDate": { $gt: currentTimestamp },
          parentSessionId: { $ne: null },
        })
        .sort({
          "sessionDate.startDate": 1,
        })
        .limit(3)
        .exec();

      if (!nextSession) {
        continue;
      }

      for (sessionFound of nextSession) {
        //get tutor profile for session
        const tutorUserId = sessionFound.tutors[0].userId;
        const tutorProfile = await tutorHelper.getTutorProfile(tutorUserId);

        const studentProfile = await userHelper.getStudentProfileAndLevel(
          userId
        );

        //get coordinator profile for session
        const coordinatorProfile =
          await sessionsStudentHelper.getCoordinatorProfilePerStudent(userId);

        //setup session object
        const session = {
          sessionId: sessionFound.sessionId,
          sessionType: sessionFound.sessionType,
          program: sessionFound.program,
          status: sessionFound.status,
          tutorProfile: tutorProfile.data,
          school: sessionFound.school,
          studentProfile: studentProfile.data,
          referent: coordinatorProfile.data ? coordinatorProfile.data : [],
          sessionLink: sessionFound.sessionLink,
          placeOrRoom: sessionFound.placeOrRoom,
          sessionDate: sessionFound.sessionDate,
          sessionRecurrence: sessionFound.recurrence,
        };

        listOfNextSession.push(session);
      }
    }

    return { status: true, data: listOfNextSession };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error while getting next sessions" };
  }
};

//get name of students and the link of maxicours for each student
exports.getStudentsNameAndMaxicoursLink = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return {
        status: false,
        message: "Error while getting students name and maxicours link",
      };
    }

    let listOfStudents = [];
    studentsList.data.forEach((student) => {
      const studentObject = {
        firstName: student.contactDetails.firstName,
        lastName: student.contactDetails.lastName,
        maxicoursLink: "https://www.maxicours.com/",
      };

      listOfStudents.push(studentObject);
    });

    return { status: true, data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error while getting students name and maxicours link",
    };
  }
};

//get the Session as number of sessions per student
exports.getNumberOfSessionsPerStudent = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return {
        status: false,
        data: "Error while getting number of sessions per student",
      };
    }

    let listOfStudents = [];
    for (let i = 0; i < studentsList.data.length; i++) {
      const student = studentsList.data[i];
      const userId = student.userId;

      //get number of sessions for student
      const numberOfSessions = await sessionModel.countDocuments({
        students: {
          $elemMatch: {
            userId: userId,
            // , absence: false
          },
        },
        parentSessionId: { $ne: null },
      });

      const studentProfile = await userHelper.getStudentProfile(userId);

      const studentObject = {
        studentProfile: studentProfile.data,
        numberOfSessions: numberOfSessions,
      };

      listOfStudents.push(studentObject);
    }

    return { status: true, data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error while getting number of sessions per student",
    };
  }
};

//get absences for student
exports.getAbsencesPerStudent = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return {
        status: false,
        data: "Error while getting absences per student",
      };
    }

    let listOfStudents = [];
    for (let i = 0; i < studentsList.data.length; i++) {
      const student = studentsList.data[i];
      const userId = student.userId;

      //get number of absences for student
      const numberOfAbsences = await sessionModel.countDocuments({
        students: { $elemMatch: { userId: userId, absence: true } },
        parentSessionId: { $ne: null },
      });

      const studentProfile = await userHelper.getStudentProfile(userId);

      const studentObject = {
        studentProfile: studentProfile.data,
        numberOfAbsences: numberOfAbsences,
      };

      listOfStudents.push(studentObject);
    }

    return { status: true, data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error while getting absences per student",
    };
  }
};

//get list of coordinators for student
exports.getCoordinatorsPerStudent = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return {
        status: false,
        data: "Error while getting coordinators per student",
      };
    }

    let listOfCoordinators = [];
    for (let i = 0; i < studentsList.data.length; i++) {
      const student = studentsList.data[i];
      const userId = student.userId;

      const coordinatorProfile =
        await sessionsStudentHelper.getCoordinatorProfilePerStudent(userId);
      if (coordinatorProfile.status) {
        listOfCoordinators.push(...coordinatorProfile.data);
      }
    }
    if (listOfCoordinators.length === 0) {
      return { status: true, data: [] };
    }
    //remove duplicates
    if (listOfCoordinators.length > 1) {
      listOfCoordinators = listOfCoordinators.filter(
        (v, i, a) => a.findIndex((t) => t.userId === v.userId) === i
      );
    }

    return { status: true, data: listOfCoordinators };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error while getting coordinators per student",
      data: null,
    };
  }
};

//get all sessions for all students
exports.getAllSessionsPerStudent = async (studentsList) => {
  try {
    if (!studentsList.status) {
      return {
        status: false,
        data: "Error while getting all sessions per student",
      };
    }

    let listOfSessions = [];
    for (let i = 0; i < studentsList.data.length; i++) {
      const student = studentsList.data[i];
      const userId = student.userId;

      //get all sessions for student
      const sessions = await sessionModel.find({
        students: {
          $elemMatch: {
            userId: userId,
            // , absence: false
          },
        },
        parentSessionId: { $ne: null },
      });

      for (let j = 0; j < sessions.length; j++) {
        const nextSession = sessions[j];
        //get tutor profile for session
        const tutorUserId = nextSession.tutors[0].userId;
        const tutorProfile = await tutorHelper.getTutorProfile(tutorUserId);

        const studentProfile = await userHelper.getStudentProfileAndLevel(
          userId
        );

        //get coordinator profile for session
        const coordinatorProfile =
          await sessionsStudentHelper.getCoordinatorProfilePerStudent(userId);

        //setup session object
        const sessionItem = {
          sessionId: nextSession.sessionId,
          sessionType: nextSession.sessionType,
          program: nextSession.program,
          status: nextSession.status,
          tutorProfile: tutorProfile.data,
          school: nextSession.school,
          studentProfile: studentProfile.data,
          referent: coordinatorProfile.data ? coordinatorProfile.data : [],
          sessionLink: nextSession.sessionLink,
          placeOrRoom: nextSession.placeOrRoom,
          sessionDate: nextSession.sessionDate,
          sessionRecurrence: nextSession.recurrence,
        };

        listOfSessions.push(sessionItem);
      }
    }

    return { status: true, data: listOfSessions };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error while getting all sessions per student",
      data: null,
    };
  }
};
