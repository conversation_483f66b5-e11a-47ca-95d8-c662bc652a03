const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const crHelper = require("../controllers/reports/cr.helper.js");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const formidable = require("formidable");

const reportConstants = require("../utils/constants/reports.constants.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
  ROLE_TUTOR,
  ROLE_STUDENT,
  VSC,
} = require("../utils/constants/userRolesConstants.js");
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");
const SessionsModel = require("../models/Sessions.Model.js");

//This is a route for Reports CR and SC
/**
 * @swagger
 * /api/v1/crscreport:
 *   post:
 *     summary: Create a SC report
 *     description: Create a SC (Social Credit) report.
 *     tags: [CR Report]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The ID of the user creating the report.
 *         required: true
 *         schema:
 *           type: string
 *       - in: formData
 *         name: reportData
 *         description: The data for the report in JSON format.
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reportData:
 *                 type: object
 *                 description: The data for the report.
 *     responses:
 *       '200':
 *         description: Report created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Report created successfully"
 *                 data:
 *                   type: object
 *                   example: {}
 *       '500':
 *         description: Failed to create report.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to create report"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
//create new SC or CR report
router.post(
  "/crscreport",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      ROLE_TUTOR,
      ROLE_STUDENT,
    ]),
  ],
  async (req, res) => {
    const form = formidable({ multiples: true });
    const userId = req.query.userId;
    form.parse(req, async (err, fields, files) => {
      let reportData = fields.reportData;

      reportData = JSON.parse(reportData);
      const session = await SessionsModel.findOne ({sessionId : reportData.sessionId})
      reportData.vsc = session?.vsc;
      console.log('sessionVsc =>', session?.vsc)
      let createdReport = await crHelper.createScReport(userId, reportData);

      //check if the report created successfully
      if (createdReport.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report created successfully`,
          createdReport
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report not created`,
          createdReport
        );
        return res.status(500).json(response);
      }
    });
  }
);

//get the CR or SC report by _id
/**
 * @swagger
 * /api/v1/crscreport:
 *   get:
 *     summary: Get a SC report by ID
 *     description: Retrieve a SC (Social Credit) report by its ID.
 *     tags: [CR Report]
 *     parameters:
 *       - in: query
 *         name: reportId
 *         description: The ID of the report to retrieve.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Report found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Report found"
 *                 data:
 *                   type: object
 *                   description: The SC report data.
 *       '500':
 *         description: Report not found or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Report not found"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/crscreport",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const sessionId = req.query.sessionId;

      //check if the reportId not empty or null
      if (sessionId == null || sessionId == "") {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report id is empty or null`,
          `Please provide the report id`
        );
        return res.status(200).json(response);
      }

      const report = await crHelper.getScCrReportBySessionId(sessionId);

      //check if the report found
      if (report.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report found`,
          report.data
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report not found`,
          report.message
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting the report`,
        error.message
      );
      return res.status(500).json(response);
    }
  }
);

//delete the CR or SC report by _id
/**
 * @swagger
 * /api/v1/crscreport:
 *   delete:
 *     summary: Delete a SC report by ID
 *     description: Delete a SC (Social Credit) report by its ID.
 *     tags: [CR Report]
 *     parameters:
 *       - in: query
 *         name: reportId
 *         description: The ID of the report to delete.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Report deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Report deleted successfully"
 *       '500':
 *         description: Report not deleted or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Report not deleted"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.delete(
  "/crscreport",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const reportId = req.query.reportId;

      //check if the reportId not empty or null
      if (reportId == null || reportId == "") {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report id is empty or null`,
          `Please provide the report id`
        );
        return res.status(200).json(response);
      }

      const deletedReport = await crHelper.deleteScCrReportById(reportId);

      //check if the report deleted
      if (deletedReport.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report deleted successfully`,
          `Don't be sad, you can create a new one`
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report not deleted`,
          deletedReport.message
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting the report`,
        error.message
      );
      return res.status(500).json(response);
    }
  }
);

//update the CR or SC report
/**
 * @swagger
 * /api/v1/crscreport:
 *   put:
 *     summary: Update a SC report
 *     description: Update a SC (Social Credit) report with new data.
 *     tags: [CR Report]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The ID of the user updating the report.
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               reportData:
 *                 type: object
 *                 description: The updated report data.
 *     responses:
 *       '200':
 *         description: Report updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Report updated successfully"
 *                 data:
 *                   type: object
 *                   description: The updated report data.
 *       '500':
 *         description: Report not updated or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Report not updated"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.put(
  "/crscreport",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      ROLE_TUTOR,
      ROLE_STUDENT,
      VSC
    ]),
  ],
  async (req, res) => {
    const userId = req.query.userId;

    try {
      const form = formidable({ multiples: true });
      form.parse(req, async (err, fields, files) => {
        let reportData = fields.reportData;

        //check if the reportData not empty or null
        if (reportData == null || reportData == "") {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Report data is empty or null`,
            `Please provide the report data`
          );
          return res.status(200).json(response);
        }

        //parse the reportData to JSON
        reportData = JSON.parse(reportData);

        //update the report
        let updatedReport = await crHelper.updateScCrReport(userId, reportData);

        //check if the report updated successfully
        if (updatedReport.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Report updated successfully`,
            updatedReport.data
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Report not updated`,
            updatedReport
          );
          return res.status(500).json(response);
        }
      });
    } catch (error) {
      console.log(error);
    }
  }
);

// Route to update student appreciation in a crReport
/**
 * @swagger
 * /api/v1/crscreport/student/appreciation:
 *   patch:
 *     summary: Update student appreciation in a CR report
 *     description: Update student appreciation in a CR report.
 *     tags:
 *       - CR Report
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user making the request.
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - appreciation
 *               - _id
 *             properties:
 *               _id:
 *                 type: string
 *                 description: The ID of the report to be updated.
 *               appreciation:
 *                 type: string
 *                 description: The appreciation text to be added to the report.
 *               note:
 *                 type: string
 *                 description: The note text to be added to the report.
 *               rating:
 *                 type: integer
 *                 description: The rating to be added to the report.
 *                 example: 5
 *     responses:
 *       200:
 *         description: Report updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Report updated successfully
 *                 data:
 *                   type: object
 *                   description: Updated report data
 *       400:
 *         description: Appreciation and Report ID are required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Appreciation and Report ID are required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 error:
 *                   type: string
 *                   example: Error message
 */
router.patch(
  "/crscreport/student/appreciation",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    const userId = req.query.userId;
    const { appreciation, rating, note, _id } = req.body;
    try {
      if (!_id || !appreciation) {
        return res.status(400).json({
          status: "error",
          message: "Appreciation and Report ID are required",
        });
      }

      const reportObject = {
        _id,
        appreciation,
        rating: rating ? parseInt(rating) : 0,
        note,
      };

      const result = await crHelper.updateStudentAppreciation(
        userId,
        reportObject
      );

      if (result.status) {
        return res.status(200).json({
          status: "success",
          message: "Report updated successfully",
          data: result.data,
        });
      } else {
        return res.status(500).json({
          status: "error",
          message: "Report not updated",
          error: result.message,
        });
      }
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);
// Route to update tutor appreciation in a crReport
/**
 * @swagger
 * /api/v1/crscreport/tutor/appreciation:
 *   patch:
 *     summary: Update tutor appreciation in a CR report
 *     description: Update tutor appreciation in a CR report.
 *     tags:
 *       - CR Report
 *     security:
 *       - ApiKeyAuth: []   # Add security schema to this endpoint
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user making the request.
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - appreciation
 *               - _id
 *             properties:
 *               _id:
 *                 type: string
 *                 description: The ID of the report to be updated.
 *               appreciation:
 *                 type: string
 *                 description: The appreciation text to be added to the report.
 *               rating:
 *                 type: integer
 *                 description: The rating to be added to the report.
 *                 example: 5
 *     responses:
 *       200:
 *         description: Report updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Report updated successfully
 *                 data:
 *                   type: object
 *                   description: Updated report data
 *       400:
 *         description: Appreciation and Report ID are required
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Appreciation and Report ID are required
 *       500:
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Internal server error
 *                 error:
 *                   type: string
 *                   example: Error message
 */
router.patch(
  "/crscreport/tutor/appreciation",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_TUTOR])],
  async (req, res) => {
    const userId = req.query.userId;
    const { appreciation, rating, _id } = req.body;

    try {
      if (!_id || !appreciation) {
        return res.status(400).json({
          status: "error",
          message: "Appreciation and Report ID are required",
        });
      }

      const reportObject = {
        _id,
        appreciation,
        rating: rating ? parseInt(rating) : 0,
      };

      const result = await crHelper.updateTutorAppreciation(
        userId,
        reportObject
      );

      if (result.status) {
        return res.status(200).json({
          status: "success",
          message: "Report updated successfully",
          data: result.data,
        });
      } else {
        return res.status(500).json({
          status: "error",
          message: "Report not updated",
          error: result.message,
        });
      }
    } catch (error) {
      return res.status(500).json({
        status: "error",
        message: "Internal server error",
        error: error.message,
      });
    }
  }
);

//report the CR or SC report dashboard
/**
 * @swagger
 * /api/v1/crscreport/dashboard:
 *   get:
 *     summary: Get SC/CR Report Dashboard
 *     description: Retrieve the dashboard for SC (Social Credit) or CR (Credit Report) reports based on the specified report type.
 *     tags: [CR Report]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The ID of the user accessing the dashboard.
 *         required: true
 *         schema:
 *           type: string
 *       - in: query
 *         name: typeOfReport
 *         description: The type of report (SC or CR).
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Report Dashboard retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Report Dashboard for this type: SC"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                             description: The ID of the report.
 *                           reportName:
 *                             type: string
 *                             description: The name of the report.
 *                           reportType:
 *                             type: string
 *                             description: The type of the report (SC or CR).
 *                     page:
 *                       type: number
 *                       description: The current page number.
 *                     pageSize:
 *                       type: number
 *                       description: The number of items per page.
 *                     total:
 *                       type: number
 *                       description: The total number of reports.
 *       '500':
 *         description: Report Dashboard not retrieved or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Report not found"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/crscreport/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      let typeOfReport = req.query.typeOfReport;
      let page = apiResponse.buildPage(req.query.page);
      let pageSize = apiResponse.buildPageSize(req.query.pageSize);
      let filter = req.query.filter;
      let sortBy = req.query.sortBy;

      //check if the typeOfReport not empty or null
      if (typeOfReport == null || typeOfReport == "") {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report type is empty or null`,
          `Please provide the report type`
        );
        return res.status(200).json(response);
      }

      let listOfReports;
      if (
        typeOfReport ==
        reportConstants.crScReportsTypes.crReport.crScReportsTypeId
      ) {
        listOfReports = await crHelper.crReportDashboard({
          userId: req.userId,
          userRole: req.userRole,
          typeOfReport,
          page,
          pageSize,
          filter,
          sortBy,
        });
      } else if (
        typeOfReport ==
        reportConstants.crScReportsTypes.scReport.scScReportsTypeId
      ) {
        listOfReports = await crHelper.reportDashboard(
          typeOfReport,
          page,
          pageSize,
          filter,
          sortBy
        );
      }

      //check if the report found
      if (listOfReports.status) {
        let response = apiResponse.responseWithPagination(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report Dashboard for this type: ${typeOfReport}`,
          listOfReports.data,
          page,
          pageSize,
          listOfReports.total
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Report not found`,
          listOfReports.message
        );
        return res.status(500).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting the report`,
        error.message
      );
      return res.status(500).json(response);
    }
  }
);

router.get(
  "/studentAttendance/:userId",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, MANAGER, COORDINATOR, VSC]),
  ],
  crHelper.getStudentAttendance
);

router.get(
  "/tutorAttendance/:userId",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, MANAGER, COORDINATOR, VSC]),
  ],
  crHelper.getTutorAttendance
);

//export default router;
module.exports = router;
