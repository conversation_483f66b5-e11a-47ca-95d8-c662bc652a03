const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("scholarYear Routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import ScholarYear Model
const ScholarYear = require("../models/ScholarYear.Model.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");
const { SUPER_ADMINISTRATOR, COORDINATOR, MANAGER } = require("../utils/constants/userRolesConstants.js");
const moment = require("moment");
router.use((req, res, next) => {
  verifyUserRole.checkUserRoleAndAccessLevel(
    req,
    res,
    next,
    mongodbModelConstants.modelName.SCHOLAR_YEARS
  );
});

//save new ScholarYear
router.post("/scholarYear", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
  const form = formidable({ multiples: true });
  form.parse(req, async (err, fields, files) => {
    try {
      // const userId = req.header("userId");
      const scholarYear = JSON.parse(fields.scholarYear);
      //check if scholarYear is provided
      if (!scholarYear) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `scholarYear is required to perform this action. Please provide a valid scholarYear.`
        );
        return res.status(400).json(response);
      }


      const newScholarYear = new ScholarYear({
        year: scholarYear.year,
        startDate: moment(scholarYear.startDate, "DD/MM/YYYY").format("YYYY-MM-DD"),
        endDate: moment(scholarYear.endDate, "DD/MM/YYYY").format("YYYY-MM-DD"),
      });
      await newScholarYear.save();

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "New ScholarYear saved successfully",
        newScholarYear
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to save new ScholarYear",
        error
      );
      return res.status(500).json(response);
    }
  });
});

//get all ScholarYear
router.get("/scholarYear/all", async (req, res) => {
  try {
    const scholarYear = await ScholarYear.find();

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "All ScholarYear",
      scholarYear
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get all ScholarYear",
      error
    );
    return res.status(500).json(response);
  }
});

//get ScholarYear by id
router.get("/scholarYear", async (req, res) => {
  try {
    const scholaryearId = req.query.scholarYearId;

    //check if scholaryearId is provided
    if (!scholaryearId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `scholaryearId is required to perform this action. Please provide a valid scholaryearId.`
      );
      return res.status(400).json(response);
    }

    const scholarYear = await ScholarYear.findOne({ _id: scholaryearId });

    //check if scholarYear is found
    if (!scholarYear) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `scholarYear not found.`
      );
      return res.status(400).json(response);
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "ScholarYear data",
        scholarYear
      );
      return res.status(200).json(response);
    }
  } catch (error) { }
});

//update ScholarYear
router.put("/scholarYear",[verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
  const form = formidable({ multiples: true });
  form.parse(req, async (err, fields, files) => {
    try {
      const scholarYear = JSON.parse(fields.scholarYear);

      //check if scholarYear is provided
      if (!scholarYear) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `scholarYear data is required to update. Please provide a valid scholarYear.`
        );
        return res.status(400).json(response);
      }

      const scholarYearId = scholarYear._id;

      //check if scholarYearId is provided
      if (!scholarYearId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `_id is required to update scholarYear. Please provide a valid _id.`
        );
        return res.status(400).json(response);
      }

      const scholarYearToUpdate = await ScholarYear.findOneAndUpdate(
        { _id: scholarYearId },
        scholarYear,
        { new: true }
      );
      console.log("scholarYearToUpdate", scholarYearToUpdate);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "ScholarYear updated successfully",
        scholarYearToUpdate
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to update ScholarYear",
        error
      );
      return res.status(500).json(response);
    }
  });
});

// delete ScholarYear
router.delete("/scholarYear",[verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
  try {
    const scholarYearId = req.query.scholarYearId;

    //check if scholarYearId is provided
    if (!scholarYearId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `scholarYearId is required to delete. Please provide a valid scholarYearId.`
      );
      return res.status(400).json(response);
    }

    const scholarYear = await ScholarYear.findOneAndDelete({
      _id: scholarYearId,
    });

    //check if scholarYear is found
    if (!scholarYear) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `scholarYear not found.`
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "ScholarYear deleted successfully",
      `The scholarYear with id: ${scholarYearId} has been deleted successfully.`
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to delete ScholarYear",
      error
    );
    return res.status(500).json(response);
  }
});

//export router
module.exports = router;
