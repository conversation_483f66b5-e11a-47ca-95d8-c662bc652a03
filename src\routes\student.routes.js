//setup express
const express = require("express");
const router = express.Router();
const User = require("../models/User.Models.js");
const jwt = require("jsonwebtoken");
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const firebaseHelper = require("../controllers/firebase/firebase.helper.js");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import formidable
const formidable = require("formidable");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");

//import userRole  constants
const userRoleConstants = require("../utils/constants/userRolesConstants.js");

//import  logger
const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("parent.routes.js");

//import student helper
const studentHelper = require("../controllers/students/student.helpers.js");
const matchingHelper = require("../controllers/matching/matching.helper.js");

const getDataWithPaginationAndFilterAndOrder = require("../controllers/mongoDb/get.Data.Filter.Order.js");

const crscreportsModel = require("../models/Cr.Report.Model.js");
const sessionModel = require("../models/Sessions.Model.js");

const sessionStudentHelper = require("../controllers/sessions/sessions.student.helper.js");
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const {
  userStatus,
  studentStatus,
} = require("../utils/constants/student.constants");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  ROLE_STUDENT,
  SUPER_ADMINISTRATOR,
  VSC,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");
const {
  HOME_CLASSES,
  DEVOIRS_FAITS,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../utils/constants/program.constants.js");
const ParentPreferencesModel = require("../models/Parent.Preferences.Model.js");
//Signup Student
/**
 * @swagger
 * /api/v1/user/student:
 *   post:
 *     summary: Create a new student account
 *     tags:
 *       - Student
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firebaseUserId:
 *                 type: string
 *                 description: The Firebase user ID.
 *                 example: "1234567890abcdef"
 *               provider:
 *                 type: string
 *                 description: The provider of the Firebase ID.
 *                 example: "google"
 *               firebaseIdentifier:
 *                 type: string
 *                 description: The Firebase identifier.
 *                 example: "firebase_identifier"
 *               firstName:
 *                 type: string
 *                 description: The first name of the student.
 *                 example: "John"
 *               lastName:
 *                 type: string
 *                 description: The last name of the student.
 *                 example: "Doe"
 *     responses:
 *       200:
 *         description: Successful operation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   description: Status of the operation.
 *                 message:
 *                   type: string
 *                   description: Description of the response.
 *                 data:
 *                   type: object
 *                   description: Additional data if any.
 *                 code:
 *                   type: string
 *                   description: API response code.
 *       400:
 *         description: Bad Request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   description: Status of the operation.
 *                 message:
 *                   type: string
 *                   description: Description of the error.
 *                 code:
 *                   type: string
 *                   description: API response code.
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   description: Status of the operation.
 *                 message:
 *                   type: string
 *                   description: Description of the error.
 *                 code:
 *                   type: string
 *                   description: API response code.
 *     security:
 *       - apiKeyAuth: []
 */
router.post(
  "/user/student",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const {
        firebaseUserId,
        provider,
        firebaseIdentifier,
        firstName,
        lastName,
      } = req.body;

      // userId is generated by  uuidv4() and is unique for each user
      const userId = userHelper.generateNewUserId();
      //check if firebaseIdentifier is provided in the request
      if (
        firebaseIdentifier === undefined ||
        firebaseIdentifier === null ||
        firebaseIdentifier === ""
      ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase Identifier is required`,
          `Please provide firebase identifier, it is required to create a student account`
        );
        res.status(200).json(response);
      }

      //check if firebaseUserId is provided in the request
      if (
        firebaseUserId === undefined ||
        firebaseUserId === null ||
        firebaseUserId === ""
      ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase User Id is required`,
          `Please send firebase user id, it is required to create a student account`
        );

        res.status(200).json(response);
      }

      //check if provider is provided in the request
      if (provider === undefined || provider === null || provider === "") {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Provider is required`,
          `Please send provider, it is required to create a student account`
        );

        res.status(200).json(response);
      }

      //check if user already exists based on firebaseIdentifier
      let userDocument = await userHelper.getUserDetailsFromFirebaseIdentifier(
        firebaseIdentifier
      );

      //if user not found, create new user document in database otherwise return user exists
      if (userDocument) {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User already exists with this identifier ${firebaseIdentifier}`,
          { userId: userDocument.userId },
          apiResponse.apiConstants.USER_ALREADY_EXISTS
        );
        res.status(200).json(response);
      } else {
        const contactDetails = await userHelper.getContactDetailsObject(
          firebaseIdentifier,
          null
        );

        contactDetails.firstName = firstName;
        contactDetails.lastName = lastName;

        //create new user document
        let user = new User({
          userId: userId,
          firebaseIds: {
            firebaseUserId: firebaseUserId,
            provider: provider,
            identifier: firebaseIdentifier,
          },
          hasChangedPassword: true,
          userRole: userRoleConstants.ROLE_STUDENT,
          contactDetails: contactDetails,
        });

        try {
          const savedUser = await user.save();
          const studentPreferenceData =
            await studentHelper.createStudentPreferences({
              userId,
              contactDetails,
            });
          const token = generateAccessToken(savedUser.userId);
          const refreshToken = generateRefreshToken(savedUser.userId);

          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "User created successfully",
            {
              ...savedUser,
              studentPreferences: studentPreferenceData._doc,
              token,
            },
            apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
          );
          res.cookie("refreshToken", refreshToken, {
            httpOnly: true,
            secure: process.env.NODE_ENV === "production",
            path: "/",
          });
          return res.status(200).json(response);
        } catch (err) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            "Error while saving user document",
            `There was an error while saving user document. Error: ${err}`
          );
          return res.status(400).json(response);
        }
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while creating a student account`,
        500
      );
      res.status(500).json(response);
    }
  }
);
//save student in database
/**
 * @swagger
 * /api/v1/user/student:
 *   put:
 *     summary: Update or create a student user
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               firebaseUserId:
 *                 type: string
 *                 description: The Firebase user ID
 *               provider:
 *                 type: string
 *                 description: The provider of authentication (e.g., Google, Facebook)
 *               firebaseIdentifier:
 *                 type: string
 *                 description: The Firebase identifier
 *               firstName:
 *                 type: string
 *                 description: The first name of the student
 *               lastName:
 *                 type: string
 *                 description: The last name of the student
 *               userId:
 *                 type: string
 *                 description: The ID of the user
 *               iAcceptToBeContactedForParticipatingInAStudy:
 *                 type: boolean
 *                 description: Whether the student accepts to be contacted for participating in a study
 *     responses:
 *       '200':
 *         description: Successfully updated or created student user
 *       '400':
 *         description: Bad request
 *       '404':
 *         description: User not found
 *       '500':
 *         description: Internal server error
 */
router.put(
  "/user/student",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    const {
      firebaseUserId,
      provider,
      firebaseIdentifier,
      firstName,
      lastName,
      userId,
      iAcceptToBeContactedForParticipatingInAStudy,
    } = req.body;
    // const userId = userHelper.generateNewUserId();
    try {
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User id is required.`,
          `Pleas provide user id.`,
          apiResponse.apiConstants.USER_DOES_NOT_EXISTS
        );
        return res.status(400).json(response);
      }

      if (!firebaseUserId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `FirebaseUserId is required.`,
          `Pleas provide firebaseUserId.`
        );
        return res.status(400).json(response);
      }

      if (!provider) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Provider is required.`,
          `Pleas provide provider.`
        );
        return res.status(400).json(response);
      }

      if (!firebaseIdentifier) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `FirebaseIdentifier is required.`,
          `Pleas provide firebaseIdentifier.`
        );
        return res.status(400).json(response);
      }
      const contactDetails = await userHelper.getContactDetailsObject(
        firebaseIdentifier,
        null
      );

      //create new user document
      // let user = new User({
      //   userId: userId,
      //   firebaseIds: {
      //     firebaseUserId: firebaseUserId,
      //     provider: provider,
      //     identifier: firebaseIdentifier,
      //   },
      //   userRole: userRoleConstants.ROLE_STUDENT,
      //   contactDetails: { ...contactDetails, firstName, lastName },
      //   iAcceptToBeContactedForParticipatingInAStudy:
      //     iAcceptToBeContactedForParticipatingInAStudy,
      // });
      // //get user from database
      // //     let user = await userHelper.getUserDetailsByUserId(userId);
      // user.save((err, user) => {
      //   if (err) {
      //     let response = apiResponse.responseWithStatusCode(
      //       apiResponse.apiConstants.API_REQUEST_FAILED,
      //       "Error while saving user document",
      //       `There was an error while saving user document. Error: ${err}`
      //     );
      //     res.status(400).json(response);
      //   } else {
      //     let response = apiResponse.responseWithStatusCode(
      //       apiResponse.apiConstants.API_REQUEST_SUCCESS,
      //       "User created successfully",
      //       user,
      //       apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
      //     );
      //     res.status(200).json(response);
      //   }
      // });
      let user = await userHelper.getUserDetailsByUserId(userId);

      if (!user) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found.`,
          `Please provide valid user id.`,
          apiResponse.apiConstants.USER_DOES_NOT_EXISTS
        );
        return res.status(404).json(response);
      }

      // check if user is already a student with same firebaseIdentifier
      let isUserAlreadyStudent = false;
      for (let i = 0; i < user.firebaseIds.length; i++) {
        if (user.firebaseIds[i].identifier == firebaseIdentifier) {
          isUserAlreadyStudent = true;
          break;
        }
      }

      if (isUserAlreadyStudent) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User is already registered with same firebaseIdentifier.`,
          { userId: user.userId, firebaseIdentifier: firebaseIdentifier },
          apiResponse.apiConstants.USER_ALREADY_EXISTS
        );
        return res.status(400).json(response);
      }

      //update user details

      const firebaseIds = {
        identifier: firebaseIdentifier,
        firebaseUserId: firebaseUserId,
        provider: provider,
      };

      user.firebaseIds = firebaseIds;
      user.contactDetails.email =
        contactDetails.email == null
          ? user.contactDetails.email
          : contactDetails.email;
      user.contactDetails.mobile =
        contactDetails.phoneNumber == null
          ? user.contactDetails.mobile
          : contactDetails.phoneNumber;
      user.iAcceptToBeContactedForParticipatingInAStudy =
        iAcceptToBeContactedForParticipatingInAStudy;

      //save user
      let userSaved = await user.save();

      if (!userSaved) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error in saving user.`,
          `Please try again later.`,
          500
        );
        return res.status(500).json(response);
      } else {
        const token = generateAccessToken(userSaved.userId);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User saved successfully.`,
          { ...userSaved._doc, token },
          200
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in saving student.`,
        500
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

//accept invite from parent
/**
 * @swagger
 * /api/v1/student/accept:
 *   post:
 *     summary: Accept student invitation
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               invitationCode:
 *                 type: string
 *                 description: The invitation code
 *               firebaseIdentifier:
 *                 type: string
 *                 description: The Firebase identifier
 *     responses:
 *       '200':
 *         description: Invitation code accepted successfully
 *       '400':
 *         description: Bad request
 *       '404':
 *         description: User not found or invalid Firebase identifier
 *       '500':
 *         description: Internal server error
 */
router.post(
  "/student/accept",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      ROLE_STUDENT,
      SUPER_ADMINISTRATOR,
      VSC,
      COORDINATOR,
      MANAGER,
    ]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      const { invitationCode, firebaseIdentifier } = fields;
      if (!invitationCode) {
        let response = apiResponse.response(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is required.`,
          null,
          400
        );
        return res.status(400).json(response);
      }

      if (!firebaseIdentifier) {
        let response = apiResponse.response(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase identifier is required.`,
          `Please provide firebase identifier, it is required to accept invitation.`,
          400
        );
        return res.status(400).json(response);
      }

      let invitationCodeExtracted =
        studentHelper.extractInvitationCode(invitationCode);

      let userId = await userHelper.getUserIdFromFirebaseIdentifier(
        firebaseIdentifier
      );

      if (!userId) {
        let response = apiResponse.response(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found.`,
          `Please provide valid firebase identifier.`,
          404
        );
        return res.status(404).json(response);
      }

      //check if invitation code is valid
      if (!studentHelper.checkInvitationCode(invitationCode, userId)) {
        let response = apiResponse.response(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is not valid.`,
          `Please provide valid invitation code.`,
          404
        );
        return res.status(404).json(response);
      }

      //accept invitation code, update invitationAccepted to true
      let acceptInvitationCodeResponse =
        await studentHelper.acceptInvitationCode(invitationCode);
      console.log("acceptInvitationCodeResponse", acceptInvitationCodeResponse);
      if (!acceptInvitationCodeResponse.status) {
        let response = apiResponse.response(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error in accepting invitation code.`,
          acceptInvitationCodeResponse.message,
          500
        );
        return res.status(500).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code accepted successfully.`,
          `User with id ${userId} has accepted invitation code ${invitationCode} successfully.`,
          apiResponse.apiConstants.INVITATION_CODE_ACCEPTED_SUCCESSFULLY
        );
        return res.status(200).json(response);
      }
    });
  }
);

/**
 * @swagger
 * /api/v1/student/change-password:
 *   patch:
 *     summary: Change student password
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the student user
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *                 description: The new password for the student account
 *     responses:
 *       '200':
 *         description: Account password successfully updated
 *       '400':
 *         description: Bad request, missing userId or new password
 *       '404':
 *         description: User not found or missing email
 *       '500':
 *         description: Internal server error
 */
router.patch(
  "/student/change-password",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "UserId is required, please provide a valid userId",
          400
        );
        return res.status(400).json(response);
      }
      const user = await userHelper.getUserDetailsByUserId(userId);
      if (!user || !user.contactDetails.email) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found: ${userId}, please provide a valid userId`,
          400
        );
        return res.status(400).json(response);
      }
      const { password } = req.body;
      if (!password) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New password is empty`,
          400
        );
        return res.status(400).json(response);
      }

      const userRecord = await firebaseHelper.changePassword(
        user.contactDetails.email,
        password
      );

      if (!userRecord) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating password`,
          400
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Account password successfully updated`
      );
      return res.status(200).json(response);
    } catch (error) {
      let errorMessage = `Error while updating password: ${error}`;
      console.log(errorMessage);
      let response = apiResponse.response(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Error while getting tutor preferences",
        errorMessage
      );
      return res.status(500).json(response);
    }
  }
);

//Update parent data, my space
/**
 * @swagger
 * /api/v1/student/myspace:
 *   put:
 *     summary: Update student personal information
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The ID of the student user
 *               firstName:
 *                 type: string
 *                 description: The first name of the student
 *               lastName:
 *                 type: string
 *                 description: The last name of the student
 *               dateOfBirth:
 *                 type: string
 *                 format: date
 *                 description: The date of birth of the student (YYYY-MM-DD)
 *               gender:
 *                 type: string
 *                 description: The gender of the student
 *               telephone:
 *                 type: string
 *                 description: The telephone number of the student
 *               address:
 *                 type: string
 *                 description: The JSON string representing the address object
 *     responses:
 *       '200':
 *         description: Student data updated successfully
 *       '400':
 *         description: Bad request, missing userId or other required fields
 *       '404':
 *         description: User not found
 *       '500':
 *         description: Internal server error
 */
router.put(
  "/student/myspace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      console.log("fields", fields);
      try {
        const userId = fields.userId;
        const firstName = fields.firstName;
        const lastName = fields.lastName;
        const dateOfBirth = fields.dateOfBirth;
        const gender = fields.gender;
        const telephone = fields.telephone;
        const address = fields.address;
        console.log("address==>", address);
        //check if userId is provided in the request
        if (!userId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User Id is required`,
            `Please provide userId, it is required to update parent data`
          );
          res.status(200).json(response);
        }

        //Get user data from database
        let userDocument = await userHelper.getUserDetailsByUserId(userId);

        //check if user is found
        if (!userDocument) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User not found`,
            `User not found, please provide valid userId`
          );
          res.status(200).json(response);
        }
        //update user details
        const updatedUser = await User.findOneAndUpdate(
          { userId: userId },
          {
            $set: {
              "contactDetails.firstName": firstName,
              "contactDetails.lastName": lastName,
              "contactDetails.telephone": telephone,
              dateOfBirth: dateOfBirth,
              gender: gender,
              address: JSON.parse(address),
            },
          },
          { new: true }
        );
        const updateStudentPreferences =
          await StudentPreferencesModel.findOneAndUpdate(
            {
              userId: userId,
            },
            {
              $set: {
                "contactDetails.firstName": firstName,
                "contactDetails.lastName": lastName,
                "contactDetails.telephone": telephone,
                dateOfBirth: dateOfBirth,
                gender: gender,
              },
            },
            { new: true }
          );

        if (updatedUser || updateStudentPreferences) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Student data updated successfully`,
            updatedUser,
            apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
          );
          res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error while updating parent data`,
            `There was an error while updating student data. Error: ${err}`
          );
          res.status(400).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while updating parent data`,
          `There was an error while updating student data. Error: ${err}`
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
        res.status(500).json(response);
      }
    });
  }
);

//get mySpace
/**
 * @swagger
 * /api/v1/student/myspace:
 *   get:
 *     summary: Get student personal information
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the student user
 *     responses:
 *       '200':
 *         description: MySpace data fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   description: The ID of the student user
 *                 firstName:
 *                   type: string
 *                   description: The first name of the student
 *                 lastName:
 *                   type: string
 *                   description: The last name of the student
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *                   description: The date of birth of the student (YYYY-MM-DD)
 *                 gender:
 *                   type: string
 *                   description: The gender of the student
 *                 phoneNumber:
 *                   type: string
 *                   description: The phone number of the student
 *                 address:
 *                   type: string
 *                   description: The address of the student
 *       '400':
 *         description: Bad request, missing userId
 *       '404':
 *         description: User not found
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/student/myspace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is required.`,
          `Please provide UserId.`,
          400
        );
        return res.status(400).json(response);
      }

      //Get userDetails
      const userDocument = await userHelper.getUserDetailsByUserId(userId);
      console.log("userDocumentM", userDocument);
      //check if user is found
      if (!userDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found`,
          `User not found, please provide valid userId`
        );
        return res.status(200).json(response);
      }

      const mySpace = {
        userId: userDocument.userId,
        firstName: userDocument.contactDetails.firstName,
        lastName: userDocument.contactDetails.lastName,
        dateOfBirth: userDocument.dateOfBirth,
        gender: userDocument.gender,
        phoneNumber: userDocument.contactDetails.phoneNumber,
        address: userDocument.address,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `MySpace data fetched successfully.`,
        mySpace,
        200
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting mySpace`,
        `There was an error while getting mySpace. Error: ${err}`
      );
    }
  }
);

//#region student DASHBOARD

//get student dashboard
/**
 * @swagger
 * /api/v1/student/dashboard:
 *   get:
 *     summary: Get student dashboard information
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the student user
 *     responses:
 *       '200':
 *         description: Student dashboard data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 nextSession:
 *                   type: array
 *                   description: Array of upcoming sessions for the student
 *                   items:
 *                     type: object
 *                     properties:
 *                       sessionId:
 *                         type: string
 *                         description: The ID of the session
 *                       sessionType:
 *                         type: string
 *                         description: The type of session
 *                       sessionDate:
 *                         type: string
 *                         format: date-time
 *                         description: The date and time of the session
 *                 maxiCourses:
 *                   type: string
 *                   description: The link to maxiCourses
 *                 rightReports:
 *                   type: array
 *                   description: Array of right reports for the student
 *                   items:
 *                     type: object
 *                     properties:
 *                       sessionId:
 *                         type: string
 *                         description: The ID of the session
 *                       sessionType:
 *                         type: string
 *                         description: The type of session
 *                       reportStatus:
 *                         type: string
 *                         description: The status of the report
 *       '400':
 *         description: Bad request, missing userId
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/student/dashboard",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_STUDENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is required.`,
          `Please provide UserId.`,
          400
        );
        return res.status(400).json(response);
      }

      //Get one of the next Session for the student
      let nextSession = await sessionStudentHelper.getNextSessionsPerStudent(
        userId
      );

      //Right Reports
      let rightReports = await sessionStudentHelper.getRightReportsPerStudent(
        userId
      );
      //combine all the data
      let studentDashboard = {
        nextSession: nextSession.data,
        maxiCourses: `https://www.maxicours.com/`,
        rightReports: rightReports.data,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Student dashboard.`,
        studentDashboard
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting student dashboard.`,
        500
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

//get student by studentId
/**
 * @swagger
 * /api/v1/student/detail:
 *   get:
 *     summary: Get student detail information
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: studentId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the student user
 *     responses:
 *       '200':
 *         description: Student detail data retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 _id:
 *                   type: string
 *                   description: The ID of the student preferences document
 *                 userId:
 *                   type: string
 *                   description: The ID of the student user
 *                 contactDetails:
 *                   type: object
 *                   description: Contact details of the student
 *                   properties:
 *                     firstName:
 *                       type: string
 *                       description: The first name of the student
 *                     lastName:
 *                       type: string
 *                       description: The last name of the student
 *                     email:
 *                       type: string
 *                       description: The email of the student
 *                     mobile:
 *                       type: string
 *                       description: The mobile number of the student
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *                   description: The date of birth of the student
 *                 gender:
 *                   type: string
 *                   description: The gender of the student
 *       '400':
 *         description: Bad request, missing studentId
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/student/detail",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([ROLE_STUDENT, SUPER_ADMINISTRATOR, COORDINATOR, VSC]),
  ],
  async (req, res) => {
    try {
      const studentId = req.query.studentId;
      let studentPreferences = await StudentPreferencesModel.findOne({
        userId: studentId,
      });
      let parentPreferences = await ParentPreferencesModel.findOne({
        userId: studentPreferences?.parentUserId,
      });
      let parentData = await userHelper.getUserDetailsByUserId(
        studentPreferences?.parentUserId
      );
      console.log("parentPreferences", parentPreferences);
      studentPreferences.availability =
        await matchingHelper.checkTutorStudentAvailability(
          studentPreferences?.availability,
          studentPreferences.userId,
          false
        );
      let sessionPassesNbre = 0;
      let nbrCr = 0;
      let note = 0;
      let lastSession = {};
      let futurSessions = {};
      // if(studentPreferences.matchedTutors.length > 0){
      const sessionPasses = await sessionModel
        .find({
          status: "session-0-to-be-scheduled",
          "students.userId": studentPreferences.userId,
          // "tutors.userId": studentPreferences?.matchedTutors[0]?.userId,
          "sessionDate.startDate": { $lte: new Date() },
          parentSessionId: { $ne: null },
        })
        .sort({ "sessionDate.endDate": -1 });
      sessionPassesNbre = sessionPasses?.length || 0;
      if (sessionPassesNbre !== 0) {
        lastSession = sessionPasses[0];
      }
      futurSessions = await sessionModel
        .findOne({
          status: "session-0-to-be-scheduled",
          "students.userId": studentPreferences.userId,
          // "tutors.userId": studentPreferences?.matchedTutors[0]?.userId,
          "sessionDate.startDate": { $gte: new Date() },
          parentSessionId: { $ne: null },
        })
        .sort({ "sessionDate.endDate": 1 });
      nbrCr = await crscreportsModel.countDocuments({
        status: { $ne: "to-be-entered" },
        "students.userId": studentPreferences.userId,
        // "tutors.userId": studentPreferences?.matchedTutors[0]?.userId,
      });
      const aggregationResult = await crscreportsModel.aggregate([
        {
          $match: {
            status: { $ne: "to-be-entered" },
            "students.userId": studentPreferences.userId,
            // "tutors.userId": studentPreferences.matchedTutors[0].userId,
          },
        },
        {
          $group: {
            _id: null,
            sumGrade: { $sum: "$grade" },
            count: { $sum: 1 },
          },
        },
      ]);
      const SumGradeOptimized =
        aggregationResult.length > 0 ? aggregationResult[0].sumGrade : 0;
      note = nbrCr > 0 ? Math.floor(SumGradeOptimized / nbrCr) : 0;

      // }

      // Convertir studentPreferences en objet JavaScript standard
      const studentPreferencesObj = studentPreferences.toObject();

      // Ajouter les propriétés supplémentaires à l'objet
      studentPreferencesObj.nbrSession = sessionPassesNbre;
      studentPreferencesObj.nbrCr = nbrCr;
      studentPreferencesObj.note = note;
      studentPreferencesObj.pourcentageCr =
        (nbrCr / sessionPassesNbre) * 100 || 0;
      studentPreferencesObj.lastSession = lastSession;
      studentPreferencesObj.futurSessions = futurSessions;

      // S'assurer que parentPreferences est un objet JavaScript avant de l'ajouter
      if (parentPreferences) {
        studentPreferencesObj.parent = parentPreferences.toObject();
      }
      if (parentData) {
        studentPreferencesObj.parent.contactDetails = parentData.contactDetails;
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Student Detail.`,
        studentPreferencesObj
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting student detail.`,
        500
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

//Student List of availability
/**
 * @swagger
 * /api/v1/student/session:
 *   get:
 *     summary: Get all sessions for a student
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the student user
 *     responses:
 *       '200':
 *         description: All sessions per student retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   sessionId:
 *                     type: string
 *                     description: The ID of the session
 *                   sessionType:
 *                     type: string
 *                     description: The type of the session
 *                   startDate:
 *                     type: string
 *                     format: date-time
 *                     description: The start date and time of the session
 *                   endDate:
 *                     type: string
 *                     format: date-time
 *                     description: The end date and time of the session
 *                   tutors:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         userId:
 *                           type: string
 *                           description: The ID of the tutor
 *                         firstName:
 *                           type: string
 *                           description: The first name of the tutor
 *                         lastName:
 *                           type: string
 *                           description: The last name of the tutor
 *                   students:
 *                     type: array
 *                     items:
 *                       type: object
 *                       properties:
 *                         userId:
 *                           type: string
 *                           description: The ID of the student
 *                         firstName:
 *                           type: string
 *                           description: The first name of the student
 *                         lastName:
 *                           type: string
 *                           description: The last name of the student
 *       '400':
 *         description: Bad request, missing userId
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/student/session",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      ROLE_STUDENT,
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      VSC,
    ]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is provided
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is required.`,
          `Please provide UserId.`
        );
        return res.status(400).json(response);
      }
      //get student availability
      let session = await sessionStudentHelper.getAllSession(userId);

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `All sessions per student.`,
        session.data
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting student availability.`,
        "Please try again later."
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

//student auto complete search by name and lastname
/**
 * @swagger
 * /api/v1/student/search:
 *   get:
 *     summary: Search for students
 *     tags: [Student]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: search
 *         required: true
 *         schema:
 *           type: string
 *         description: The search query for students by first name or last name
 *       - in: query
 *         name: selectedStudents
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         description: An array of student IDs to be excluded from the search results
 *     responses:
 *       '200':
 *         description: Successfully retrieved the list of students
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   firstName:
 *                     type: string
 *                     description: The first name of the student
 *                   lastName:
 *                     type: string
 *                     description: The last name of the student
 *                   userId:
 *                     type: string
 *                     description: The ID of the student
 *       '400':
 *         description: Bad request, missing search parameter
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/student/search",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const search = req.query.search;
      const program = req.query.program;
      const selectedStudents = req.query.selectedStudents;
      let depIs = [];
      let estap = [];
      //check if search is provided
      if (!search) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Search is required.`,
          `Please provide search.`
        );
        return res.status(400).json(response);
      }
      if (!program) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Program is required.`,
          `Please provide program.`
        );
        return res.status(400).json(response);
      }
      if (req.query.establishments) {
        estap = JSON.parse(req.query.establishments);
      }
      if (req.query.department) {
        depIs = JSON.parse(req.query.department);
      }
      const querry = {
        $and: [
          program == DEVOIRS_FAITS.programId && estap.length > 0
            ? { "assignment.establishments.establishmentId": { $in: estap } }
            : [
                HOME_CLASSES.programId,
                ZUPDEFOOT.programId,
                CLASSSES_HOME.programId,
              ].includes(program) && depIs.length > 0
            ? { "assignment.department.departmentId": { $in: depIs } }
            : {},
          { "program.programId": program, matchedTutors: [] },
          {
            $or: [
              {
                "contactDetails.firstName": { $regex: search, $options: "i" },
              },
              {
                "contactDetails.lastName": { $regex: search, $options: "i" },
              },
            ],
          },
        ],
      };
      querry.userStatus = userStatus.ACTIVE;
      querry.situation = { $ne: studentStatus.STOP };
      const students = await StudentPreferencesModel.find(querry)
        .select(
          "contactDetails.firstName contactDetails.lastName userId userId"
        ) // select only firstName, lastName, and userId fields
        .exec();
      if (!students) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No students found.`,
          `No students found with the provided filter.`
        );
        return res.status(400).json(response);
      }

      //if selectedStudents is provided, remove them from the list
      if (selectedStudents) {
        const filteredStudents = students.filter(
          (student) => !selectedStudents.includes(student.userId)
        );
        const merged = filteredStudents.map((student) => {
          const { firstName, lastName } = student.contactDetails;
          const userId = student.userId;
          return { firstName, lastName, userId };
        });
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Students list.`,
          merged
        );
        return res.status(200).json(response);
      } else {
        const merged = students.map((student) => {
          const { firstName, lastName } = student.contactDetails;
          const userId = student.userId;
          return { firstName, lastName, userId };
        });

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Students list.`,
          merged
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while searching student`,
        `There was an error while searching student. Error: ${error}`
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//#endregion

module.exports = router;
