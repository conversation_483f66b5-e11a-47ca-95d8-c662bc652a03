const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("apiLogger.js");


//save one log
router.get("/save", async (req, res) => {
     //save log to db
     logger.newLog(logger.getLoggerTypeConstants().bigBlueButton).info(`This is a test log`);

     return res.status(200).json({ message: "Log saved" });
});

//export router
module.exports = router;
