const apiConstants = require("../../utils/constants/apiConstants");
const apiLoggerS = require("../../services/logger/LoggerService.js");

const logger = new apiLoggerS("api.js");
function response(status, message, data) {
  return {
    status: status,
    message: message,
    data: data,
  };
}

//function return internalServerError response
function internalServerError(data) {
  return {
    status: apiConstants.API_REQUEST_FAILED,
    message: `${apiConstants.INTERNAL_SERVER_ERROR}`,
    data: data,
  };
}

//return Response object with status,message,data and statusCode
function responseWithStatusCode(status, message, data, statusCode) {
  // const logMethod = statusCode == "200" ? "info" : "error";
  // logger.newLog(logger.getLoggerTypeConstants().api)[logMethod]({
  //   message: message,
  //   request: {
  //     status: status,
  //     data: JSON.stringify(data),
  //     statusCode: statusCode,
  //   },
  // });

  return {
    status: status,
    message: message,
    data: data,
    statusCode: statusCode,
  };
}

// return Response object with status,message,data,page,size,total
function responseWithPagination(
  status,
  message,
  data,
  pageNumber,
  pageSize,
  total
) {
  //check if totalCount is more than pageSize increase nextPage by 1 else set nextPage same as pageNumber

  let nextPage = 0;
  if (total > pageSize && pageNumber < total / pageSize) {
    //parseInt(pageNumber) + 1;
    nextPage = parseInt(pageNumber) + 1;
  } else {
    nextPage = pageNumber;
  }
  return {
    status: status,
    message: message,
    data: data,
    pageNumber: pageNumber,
    pageSize: pageSize,
    nextPage: nextPage,
    total: total,
  };
}

function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null || pageSize != "") {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null || page != "") {
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }
  return page;
}

module.exports = {
  response,
  responseWithPagination,
  responseWithStatusCode,
  apiConstants,
  internalServerError,
  buildPageSize,
  buildPage,
};
