const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("apiLogger.js");

//import axios
const axios = require("axios");

//import xml-js
const xmlJs = require("xml-js");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import dotenv

const initBigBlueButton = require("../middleware/bigbluebutton/Init.BigBlueButton.js");

//import BigBlueButton.helper.js
const bigBlueButtonHelper = require("../controllers/bigBlueButton/bigBlueButton.Helper.js");

//import Webhooks.Helper.js
const webhooksHelper = require("../controllers/webhooks/WebHooks.Helper.js");

//import xmlToJson Helpers from utils
const xmlToJsonHelper = require("../utils/xmlToJson/xmlToJson.helper.js");

const bigBlueButtonConstants = require("../utils/constants/bigbluebutton.constans.js");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");

//import webhook model
const { empty } = require("uuidv4");
const { webhook } = require("twilio/lib/webhooks/webhooks.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const bodyParser = require("body-parser");


// router.use((req, res, next) => {
//      verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.BIGBLUEBUTTON_WEBHOOKS);
// });

//api method POST for webhooks
/**
 * @swagger
 * /api/v1/bigBlueButton/webhooks:
 *   post:
 *     summary: Receive webhooks from BigBlueButton server
 *     description: Receive and process webhook notifications from the BigBlueButton server.
 *     tags: [BigBlueButton]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               event:
 *                 type: string
 *                 description: The event type of the webhook.
 *                 example: recording.started
 *               payload:
 *                 type: object
 *                 description: The payload containing information about the event.
 *     responses:
 *       '200':
 *         description: Webhook request received and processed successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Request is valid"
 *       '401':
 *         description: Unauthorized request due to invalid signature.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid signature"
 *       '500':
 *         description: Error occurred while processing the webhook or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post("/bigBlueButton/webhooks", bodyParser.raw({ type: "application/json" }), async (req, res) => {
     //TODO: check if the request is valid
     try {
          console.log("#############################################################\n");
          console.log(`New request received from BigBlueButton Server at ${new Date().toISOString()}, request body: ${JSON.stringify(req.body)},for url: ${req.url}`);
          console.log("\n#############################################################\n");

          //Parse the request data
          const payLoad = req.body;

          //get the meetingID from payLoad

          const signature = req.headers["x-bigbluebutton-signature"];

          //verify if request is valid
          const isValid = initBigBlueButton.verifyRequest(payLoad, signature);

          logger.newLog(logger.getLoggerTypeConstants().bbbWebHooks).info(req);

          if (!isValid) {
               res.status(401).json({ message: `Invalid signature` });
          } else {
               res.status(200).json({ message: "Request is valid" });
          }
     } catch (error) {
          console.log("error", error);
          logger.newLog(logger.getLoggerTypeConstants().bbbWebHooks).error(error);
          res.status(500).json({ message: "Internal server error" });
     }
});

//create webhooks for all events
/**
 * @swagger
 * /api/v1/bigBlueButton/webhooks/createWebhooks:
 *   post:
 *     summary: Create webhooks for BigBlueButton events
 *     description: Create webhooks for receiving notifications of BigBlueButton events.
 *     tags: [BigBlueButton]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               callbackURL:
 *                 type: string
 *                 description: The URL to which webhook notifications will be sent.
 *                 example: https://example.com/webhooks
 *     responses:
 *       '200':
 *         description: Webhooks created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Webhook created successfully"
 *                 hookID:
 *                   type: string
 *                   description: The ID of the created webhook.
 *       '400':
 *         description: Bad request due to missing or invalid parameters.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Callback URL is required"
 *       '500':
 *         description: Error occurred while creating the webhook or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post("/bigBlueButton/webhooks/createWebhooks", async (req, res) => {
     const form = new formidable.IncomingForm();

     form.parse(req, async (err, fields, files) => {
          const callbackURL = fields.callbackURL;

          const newWebHook = await bigBlueButtonHelper.createWebhooks(callbackURL);
          const newWebHookData = newWebHook.data;
          console.log("newWebHookData", newWebHookData);

          let messageKey;
          if (newWebHookData.messageKey) {
               messageKey = newWebHookData.messageKey._text;
          } else {
               messageKey = null;
          }

          if (messageKey === bigBlueButtonConstants.WEBHOOKS_CREATED_DUPLICATE) {
               //return response with a message that webhooks already exist for this callbackURL
               let response = apiResponse.responseWithStatusCode(
                    apiResponse.apiConstants.API_REQUEST_SUCCESS,
                    `Webhooks already exist for this callbackURL`,
                    `Webhooks already exist for this callbackURL: ${callbackURL}`
               );

               return res.status(200).json(response);
          } else {
               const hookID = newWebHookData.hookID._text;

               //save webhooks in mongodb
               const newWebHook = await webhooksHelper.createNewWebHook(hookID, callbackURL, newWebHookData.permanentHook._text, true);

               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Webhook created successfully`, newWebHook.data);
               return res.status(200).json(response);
          }
     });
});

//delete all webhooks
/**
 * @swagger
 * /api/v1/bigBlueButton/webhooks/deleteWebhook:
 *   delete:
 *     summary: Delete a webhook for BigBlueButton events
 *     description: Delete a webhook for receiving notifications of BigBlueButton events.
 *     tags: [BigBlueButton]
 *     responses:
 *       '200':
 *         description: Webhook deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Webhook deleted successfully"
 *                 deletedWebhook:
 *                   type: object
 *                   description: Information about the deleted webhook.
 *                   properties:
 *                     hookID:
 *                       type: string
 *                       description: The ID of the deleted webhook.
 *                     callbackURL:
 *                       type: string
 *                       description: The callback URL of the deleted webhook.
 *                     permanentHook:
 *                       type: boolean
 *                       description: Indicates whether the webhook is permanent.
 *                     isActive:
 *                       type: boolean
 *                       description: Indicates whether the webhook is active.
 *       '500':
 *         description: Error occurred while deleting the webhook or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.delete("/bigBlueButton/webhooks/deleteWebhook", async (req, res) => {
     //get all webhooks from the database
     const webhooks = await webhooksHelper.getAllWebHooks();

     // if (webhooks.length <= 0) {
     //      let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `No webhooks found`, `No webhooks found in the database`);
     //      return res.status(200).json(response);
     // }

     const hookID = webhooks[0].webHookId;

     //delete webhOOk from mongodb
     //  const deleteWebHook = await webhooksHelper.deleteWebHook(hookID);

     //delete all webhooks from the database
     const deletedHook = await bigBlueButtonHelper.deleteWebhook(hookID);

     let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Webhooks deleted successfully`, deletedHook.data);
     return res.status(200).json(response);
});

//Hooks list
/**
 * @swagger
 * /api/v1/bigBlueButton/webhooks/hooksList:
 *   get:
 *     summary: Get list of webhooks
 *     description: Retrieve a list of webhooks configured for receiving notifications of BigBlueButton events.
 *     tags: [BigBlueButton]
 *     responses:
 *       '200':
 *         description: Hooks list retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                   description: Indicates the status of the request.
 *                 data:
 *                   type: array
 *                   description: List of webhooks.
 *                   items:
 *                     type: object
 *                     properties:
 *                       hookID:
 *                         type: string
 *                         description: The ID of the webhook.
 *                       callbackURL:
 *                         type: string
 *                         description: The callback URL of the webhook.
 *                       permanentHook:
 *                         type: boolean
 *                         description: Indicates whether the webhook is permanent.
 *                       isActive:
 *                         type: boolean
 *                         description: Indicates whether the webhook is active.
 *       '500':
 *         description: Error occurred while retrieving the list of webhooks or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Failed to get hooks list"
 *                   description: Error message.
 */
router.get("/bigBlueButton/webhooks/hooksList", async (req, res) => {
     let hooksList = await bigBlueButtonHelper.getHooksList();

     if (hooksList.status) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Hooks list`, hooksList.data);
          return res.status(200).json(response);
     } else {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Failed to get hooks list`, hooksList.data);
          return res.status(200).json(response);
     }
});

//export router
module.exports = router;
