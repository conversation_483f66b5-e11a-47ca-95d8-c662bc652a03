const express = require("express");
const client = require("prom-client");
const app = express();

const restResponseTimeHistogram = new client.Histogram({
  name: "rest_response_time_duration_seconds",
  help: "REST API response time in seconds",
  labelNames: ["method", "route", "status_code"],
});

const databaseResponseTimeHistogram = new client.Histogram({
  name: "db_response_time_duration_seconds",
  help: "Database response time in seconds",
  labelNames: ["operation", "success"],
});

function startMetricsServer() {
  const collectDefaultMetrics = client.collectDefaultMetrics;

  collectDefaultMetrics();

  console.log("Metrics server is starting..."); // Add this log to verify

  app.get("/metrics", async (req, res) => {
    res.set("Content-Type", client.register.contentType);

    console.log("Metrics route hit"); // Log to see if the route is being hit
    return res.send(await client.register.metrics());
  });
}

module.exports = {
  restResponseTimeHistogram,
  databaseResponseTimeHistogram,
  startMetricsServer,
};
