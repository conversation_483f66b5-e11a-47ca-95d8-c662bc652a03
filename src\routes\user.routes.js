//setup express
const express = require("express");
const router = express.Router();
const User = require("../models/User.Models.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");
const {
  checkIfAuthenticated,
  generateAccessToken,
  checkIfAuthorized,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  ROLE_PARENT,
  ROLE_STUDENT,
  ROLE_TUTOR,
  VSC,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");
//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import formidable
const formidable = require("formidable");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");

const twilio = require("twilio");

const firebaseHelper = require("../controllers/firebase/firebase.helper.js");

const tutorUserStatusConstants = require("../utils/constants/tutor.constants.js");

const userRolesConstants = require("../utils/constants/userRolesConstants.js");

const parentHelper = require("../controllers/parent/parent.helper.js");
// const studentHelper = require('../controllers/students/student.helpers.js');
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const ParentPreferencesModel = require("../models/Parent.Preferences.Model.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");

/**
 * @swagger
 * /api/v1/twilio/sms:
 *   post:
 *     summary: Send SMS using Twilio
 *     description: Endpoint to send SMS using Twilio.
 *     tags:
 *       - Twilio
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               message:
 *                 type: string
 *                 description: Message to be sent via SMS.
 *     responses:
 *       200:
 *         description: SMS sent successfully
 *         content:
 *           application/xml:
 *             schema:
 *               type: string
 *               example: "<?xml version='1.0' encoding='UTF-8'?><Response><Message>The Robots are coming! Head for the hills!</Message></Response>"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 statusCode:
 *                   type: number
 *                   example: 500
 *                 message:
 *                   type: string
 *                   example: Failed to send SMS.
 */
router.post("/twilio/sms", async (req, res) => {
  //prit the request body
  const twiml = new twilio.twiml.MessagingResponse();
  twiml.message("The Robots are coming! Head for the hills!");
  res.writeHead(200, { "Content-Type": "text/xml" });
  res.end(twiml.trim());
});

//Get user by userId
/**
 * @swagger
 * /api/v1/user:
 *   get:
 *     summary: Get user by userId
 *     description: Retrieve user details by userId
 *     tags:
 *      - User
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the user to retrieve
 *     responses:
 *       200:
 *         description: Successful response with user data
 *       400:
 *         description: Bad request. UserId is missing or invalid.
 *       404:
 *         description: User not found with the provided userId.
 *       500:
 *         description: Internal server error.
 */
router.get("/user", [verifyApiKey, checkIfAuthenticated], async (req, res) => {
  try {
    const userId = req.query.userId;
    //check if userId is present in the request
    if (!userId) {
      let response = apiResponse.response(
        false,
        `UserId is null ${userId}`,
        `UserId is required, please provide a valid userId`
      );
      return res.status(400).json(response);
    }

    //Check if user is already present in the database
    let userData = await userHelper.getUserDetailsByUserId(userId);
    if (userData) {
      if (userData.userRole === userRolesConstants.ROLE_PARENT) {
        const searchParentHasChildren = await parentHelper.searchParentHasChild(
          userId
        );
        const parentHasChildren = searchParentHasChildren.data;

        //combine data from searchParentHasChildren and userData
        userData = { ...userData._doc, parentHasChildren };
      }
      // //if user is student and
      // if (userData.userRole === userRolesConstants.ROLE_STUDENT) {
      //      const childPref = await StudentPreferencesModel.findOne({ userId: userData.userId })
      //      if (childPref) {
      //           const searchChildrenHasParent = await studentHelper.searchChildrenHasParent(childPref.parentUserId);
      //           const childrenHasParent = searchChildrenHasParent.data
      //           userData = { ...userData._doc, childrenHasParent };
      //      }

      // }
      let response = apiResponse.response(
        true,
        `User data for userId ${userId}`,
        userData
      );
      return res.status(200).json(response);
    } else {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User is not found with userId ${userId}`,
        `Please provide a valid userId`,
        apiResponse.apiConstants.USER_NOT_FOUND
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    console.log("Error while getting user by userId", error);
    let response = apiResponse.response(
      false,
      `Error while getting user`,
      error
    );
    return res.status(500).json(response);
  }
});

//SigIn user, check if user is already present in the database
router.post(
  "/user/signIn",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const { identifier, token } = req.body;
      const { role } = req.query;
      const verifiedPayload = await userHelper.verifyToken(token);
      const identifierType = identifier.includes("@") ? "email" : "phone";
      console.log(
        "condition",
        identifier,
        verifiedPayload?.firebase?.identities?.email?.[0]
      );
      console.log("identifier", identifier);
      if (
        !identifier || identifierType === "email"
          ? identifier !== verifiedPayload?.firebase?.identities?.email?.[0]
          : identifier !== verifiedPayload?.firebase?.identities?.phone?.[0]
      ) {
        let response = apiResponse.response(
          false,
          `Identifier is required, please provide a valid identifier`
        );
        return res.status(200).json(response);
      }
      //Check if user is already present in the database
      let userData = await userHelper.getUserDetailsFromFirebaseIdentifier(
        identifier,
        role
      );
      //check if the user is present in the database
      if (userData) {
        //delete from object _id
        delete userData._id;
        if (
          userData.status !==
            tutorUserStatusConstants.statusOfTutors.TUTOR_ACTIVE &&
          userData.status !==
            tutorUserStatusConstants.statusOfTutors.TUTOR_ON_HOLD
        ) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `User is deactivated with identifier ${identifier}`,
            `Please contact admin to activate your account`,
            apiResponse.apiConstants.USER_IS_NOT_ACTIVE
          );

          return res.status(403).json(response);
        }
        let userHasPreferences;
        if (userData.userRole === "parent") {
          userHasPreferences = await ParentPreferencesModel.findOne({
            userId: userData.userId,
          });
          userData.department = userHasPreferences.department;
          userData.program = userHasPreferences.program;
        }
        if (userData.userRole === "tutor") {
          userHasPreferences = await TutorPreferencesModel.findOne({
            userId: userData.userId,
          });
        }
        if (userData.userRole === "student") {
          userHasPreferences = await StudentPreferencesModel.findOne({
            userId: userData.userId,
          });
        }
        if (
          !userHasPreferences ||
          (userData.userRole === "tutor" &&
            !userHasPreferences?.contactDetails?.firstName)
        ) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `User is not active with identifier ${identifier}`,
            `Please finish your registration process`,
            apiResponse.apiConstants.FINISH_REGISTRATION
          );

          return res.status(200).json(response);
        }
        const token = generateAccessToken(userData.userId);
        const refreshToken = generateRefreshToken(userData.userId);

        const data = {
          contactDetails: {
            firstName: userData.contactDetails.firstName,
            lastName: userData.contactDetails.lastName,
            phoneNumber: userData.contactDetails.phoneNumber,
            email: userData.contactDetails.email,
          },
          address: {
            addressLine1: userData.address.addressLine1,
            zipCode: userData.address.zipCode,
          },
          _id: userData._id,
          userId: userData.userId,
          firebaseIds: userData.firebaseIds,
          status: userData.status,
          gender: userData.gender,
          userRole: userData.userRole,
          iAgreeToReceiveInformationAndUpdates:
            userData.iAgreeToReceiveInformationAndUpdates,
          iAcceptTermsAndPolicyOfConfidentiality:
            userData.iAcceptTermsAndPolicyOfConfidentiality,
          iAcceptTheCommitmentLetter: userData.iAcceptTheCommitmentLetter,
          is_suzali: userData.is_suzali,
          iAcceptToBeContactedForParticipatingInAStudy:
            userData.iAcceptToBeContactedForParticipatingInAStudy,
          createdAt: userData.createdAt,
          program: userData.program || null,
          department: userData.department || null,
          hasChangedPassword: userData.hasChangedPassword,
        };

        let response = apiResponse.response(
          true,
          `User is found with identifier ${identifier}`,
          { ...data, token }
        );
        res.cookie("refreshToken", refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
        });
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User is not found with identifier ${identifier}`,
          `Please provide a valid identifier`,
          apiResponse.apiConstants.USER_NOT_FOUND
        );
        return res.status(200).json(response);
      }

      //
    } catch (error) {
      console.log("Error while signing in user", error);
      let response = apiResponse.responseWithStatusCode(
        false,
        `Error while signing in user`,
        error,
        apiResponse.apiConstants.ERROR_WHILE_SIGNING_IN_USER
      );
      return res.status(500).json(response);
    }
  }
);

//reset password by email, check if email is registered or not and send reset password link to the email from Firebase
/**
 * @swagger
 * /api/v1/user/resetPassword:
 *   get:
 *     summary: Reset password by email
 *     description: Endpoint to reset the password by email. Checks if the email is registered, sends a reset password link to the email from Firebase.
 *     tags:
 *       - User
 *     parameters:
 *       - in: query
 *         name: email
 *         required: true
 *         description: Email address of the user.
 *         schema:
 *           type: string
 *           format: email
 *       - in: query
 *         name: redirectUrl
 *         required: true
 *         description: Redirect URL for the reset password link.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Reset password link sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Reset password link is <NAME_EMAIL>
 *                 resetLink:
 *                   type: string
 *                   example: https://example.com/resetpassword?token=abcdef123456
 *       401:
 *         description: Invalid email or redirectUrl
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Email is required, please provide a valid email
 *                 errorCode:
 *                   type: string
 *                   example: EMAIL_IS_INVALID
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while resetting password
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get("/user/resetPassword", async (req, res) => {
  try {
    const { email, redirectUrl } = req.query;

    const emailValidate = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;

    //check if email is present in the request
    if (!email.match(emailValidate)) {
      let response = apiResponse.responseWithStatusCode(
        false,
        `Email is invalid: ${email}`,
        `Email is required, please provide a valid email`,
        apiResponse.apiConstants.EMAIL_IS_INVALID
      );
      return res.status(401).json(response);
    } else if (!redirectUrl || typeof redirectUrl !== "string") {
      let response = apiResponse.responseWithStatusCode(
        false,
        `RedirectUrl is invalid: ${redirectUrl}`,
        `RedirectUrl is required, please provide a valid redirectUrl`,
        apiResponse.apiConstants.REDIRECT_URL_IS_INVALID
      );
      return res.status(401).json(response);
    }

    //Check if user is already present in the database
    let userData = await User.findByEmail(email);
    if (userData !== null) {
      //init firebase
      const firebaseHelper = require("../controllers/firebase/firebase.helper.js");

      //send reset password link to the email
      let restLink = await firebaseHelper.resetPassword(userData.userId, email);

      if (restLink) {
        let response = apiResponse.response(
          true,
          `Reset password link is sent to ${email}`,
          restLink
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.response(
          false,
          `Error while sending reset password link to ${email}`,
          `Please try again`
        );
        return res.status(200).json(response);
      }
    } else {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User is not found with email ${email}`,
        `Please provide a valid email`,
        apiResponse.apiConstants.USER_NOT_FOUND
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    console.log("Error while getting user by email", error);
    let response = apiResponse.response(
      false,
      `Error while resetting password`,
      error
    );
    return res.status(500).json(response);
  }
});

//email verification by email, check if email is registered or not and send email verification link to the email from Firebase
/**
 * @swagger
 * /api/v1/user/emailVerification:
 *   get:
 *     summary: Send email verification link
 *     description: Endpoint to send an email verification link. Checks if the email is registered, and sends an email verification link to the email from Firebase.
 *     tags:
 *       - User
 *     security:
 *       - apiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: email
 *         required: true
 *         description: Email address of the user.
 *         schema:
 *           type: string
 *           format: email
 *     responses:
 *       200:
 *         description: Email verification link sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Email verification link is <NAME_EMAIL>
 *                 verificationLink:
 *                   type: string
 *                   example: https://example.com/verifyemail?token=abcdef123456
 *       400:
 *         description: Invalid email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Email is required, please provide a valid email
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while email verification
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get("/user/emailVerification", async (req, res) => {
  try {
    const email = req.query.email;

    //check if email is present in the request
    if (!email) {
      let response = apiResponse.response(
        false,
        `Email is null ${email}`,
        `Email is required, please provide a valid email`
      );
      return res.status(400).json(response);
    }

    //Check if user is already present in the database
    let userData = await User.findByEmail(email);
    if (userData) {
      //init firebase
      const firebaseHelper = require("../controllers/firebase/firebase.helper.js");

      //send email verification link to the email
      let restLink = await firebaseHelper.emailVerification(
        userData.userId,
        email
      );

      if (restLink) {
        let response = apiResponse.response(
          true,
          `Email verification link is sent to ${email}`,
          restLink
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.response(
          false,
          `Error while sending email verification link to ${email}`,
          `Please try again`
        );
        return res.status(200).json(response);
      }
    } else {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User is not found with email ${email}`,
        `Please provide a valid email`,
        apiResponse.apiConstants.USER_NOT_FOUND
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    console.log("Error while getting user by email", error);
    let response = apiResponse.response(
      false,
      `Error while email verification`,
      error
    );
    return res.status(500).json(response);
  }
});

//save phone number in contact details of the user
/**
 * @swagger
 * /api/v1/user/savePhoneNumber:
 *   post:
 *     summary: Save phone number for a user
 *     description: Endpoint to save phone number for a user. Checks if the user exists and updates the phone number in the user's contact details.
 *     tags:
 *       - User
 *     security:
 *       - apiKeyAuth: []
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user.
 *               firebaseUserId:
 *                 type: string
 *                 description: ID of the user in Firebase.
 *               provider:
 *                 type: string
 *                 description: Provider of the authentication (e.g., Google, Facebook).
 *               firebaseIdentifier:
 *                 type: string
 *                 description: Identifier of the user in Firebase.
 *     responses:
 *       200:
 *         description: Phone number saved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: FirebaseIdentifier ********** is saved successfully
 *                 isFirebaseIdentifierExists:
 *                   type: boolean
 *                   example: false
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: UserId is null
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while saving phone number
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.post(
  "/user/savePhoneNumber",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const userId = fields.userId;
        const firebaseUserId = fields.firebaseUserId;
        const provider = fields.provider;
        const firebaseIdentifier = fields.firebaseIdentifier;

        //check if userId is present in the request
        if (!userId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `UserId is null`,
            `UserId is required, please provide a valid userId`,
            apiResponse.apiConstants.USER_ID_IS_NULL
          );
          return res.status(400).json(response);
        }

        //check if firebaseUserId is present in the request
        if (!firebaseUserId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `FirebaseUserId is null`,
            `FirebaseUserId is required, please provide a valid firebaseUserId`
          );
          return res.status(400).json(response);
        }

        //check if provider is present in the request
        if (!provider) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Provider is null`,
            `Provider is required, please provide a valid provider`
          );
          return res.status(400).json(response);
        }

        //check if firebaseIdentifier is present in the request
        if (!firebaseIdentifier) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `FirebaseIdentifier is null`,
            `FirebaseIdentifier is required, please provide a valid firebaseIdentifier`
          );
          return res.status(400).json(response);
        }

        //get user details by firebaseIdentifier
        let isUserExists = await userHelper.checkIfTheFirebaseIdentifierExist(
          firebaseIdentifier
        );

        if (isUserExists.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `This firebaseIdentifier ${firebaseIdentifier} is already registered for another user`,
            { isFirebaseIdentifierExists: isUserExists.status }
          );
          return res.status(200).json(response);
        }

        //get user details by userId
        let userData = await userHelper.getUserDetailsByUserId(userId);

        //check if user is present in the database
        if (!userData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User is not found with userId ${userId}`,
            `Please provide a valid userId`,
            apiResponse.apiConstants.USER_NOT_FOUND
          );
          return res.status(200).json(response);
        }

        //update current phone number in contact details or add new phone number in contact details
        let contactDetails = userData.contactDetails;

        //check if contactDetails is null or empty and if yes then create new
        if (contactDetails) {
          contactDetails.phoneNumber = firebaseIdentifier;
        } else {
          contactDetails = {
            phoneNumber: firebaseIdentifier,
          };

          userData.contactDetails = contactDetails;
        }

        //update firebaseIds, if firebaseId is already present then update it otherwise add new firebaseId
        let firebaseIds = userData.firebaseIds;

        //check if firebaseIds is null or empty and if yes then create new array
        if (firebaseIds) {
          let isFirebaseIdPresent = false;
          for (let i = 0; i < firebaseIds.length; i++) {
            if (firebaseIds[i].provider == provider) {
              firebaseIds[i].provider = provider;
              firebaseIds[i].identifier = firebaseIdentifier;
              firebaseIds[i].firebaseUserId = firebaseUserId;
              isFirebaseIdPresent = true;
              break;
            }
          }

          //if firebaseId is not present then add new firebaseId
          if (!isFirebaseIdPresent) {
            let firebaseId = {
              firebaseUserId: firebaseUserId,
              provider: provider,
              identifier: firebaseIdentifier,
            };
            firebaseIds.push(firebaseId);
          }

          userData.firebaseIds = firebaseIds;
        } else {
          firebaseIds = [];

          let firebaseId = {
            firebaseUserId: firebaseUserId,
            provider: provider,
            identifier: firebaseIdentifier,
          };
          firebaseIds.push(firebaseId);

          userData.firebaseIds = firebaseIds;
        }

        //save user data
        let savedUserData = await userData.save();
        //save phoneNumber to student preferences
        if (userData.userRole === userRolesConstants.ROLE_STUDENT) {
          await StudentPreferencesModel.findOneAndUpdate(
            {
              userId: userData.userId,
            },
            {
              $set: {
                "contactDetails.phoneNumber": firebaseIdentifier,
              },
            },
            {
              new: true,
            }
          );
        }
        //check if user data is saved or not
        if (savedUserData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `FirebaseIdentifier ${firebaseIdentifier} is saved successfully`,
            {
              isFirebaseIdentifierExists: isUserExists.status,
            }
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error while saving phone number`,
            `Please try again`
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log("Error while saving phone number", error);
        let response = apiResponse.responseWithStatusCode(
          false,
          `Error while saving phone number`,
          error,
          apiResponse.apiConstants.ERROR_WHILE_SAVING_PHONE_NUMBER
        );
        return res.status(500).json(response);
      }
    });
  }
);

//check if user exists or not by firebaseIdentifier
/**
 * @swagger
 * /api/v1/user/checkIfTheUserExistsInDB:
 *   get:
 *     summary: Check if user exists in the database
 *     description: Endpoint to check if a user exists in the database by their Firebase identifier.
 *     tags:
 *       - User
 *     security:
 *       - apiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: firebaseIdentifier
 *         required: true
 *         description: Firebase identifier of the user.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User existence checked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User is found with firebaseIdentifier **********
 *                 isUserExists:
 *                   type: boolean
 *                   example: true
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: There is no user found with firebaseIdentifier **********
 *                 isUserExists:
 *                   type: boolean
 *                   example: false
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while checking user by firebaseIdentifier
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/user/checkIfTheUserExistsInDB",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      let firebaseIdentifier = req.query.firebaseIdentifier;
      const userRole = req.query.userRole;
      firebaseIdentifier = encodeURIComponent(firebaseIdentifier);

      //replace %20 with + in firebaseIdentifier
      firebaseIdentifier = firebaseIdentifier.replace(/%20/g, "+");

      //replace %40 with @ in firebaseIdentifier
      firebaseIdentifier = firebaseIdentifier.replace(/%40/g, "@");

      //check if firebaseIdentifier is present in the request
      if (!firebaseIdentifier) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `FirebaseIdentifier is null`,
          `FirebaseIdentifier is required, please provide a valid firebaseIdentifier`
        );
        return res.status(200).json(response);
      }

      //get user details by firebaseIdentifier
      let userData = await userHelper.checkIfTheFirebaseIdentifierExist(
        firebaseIdentifier,
        userRole
      );

      const isUserExists = userData.status;
      if (isUserExists) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User is found with firebaseIdentifier ${firebaseIdentifier}`,
          {
            isUserExists: isUserExists,
          }
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `There is no user found with firebaseIdentifier ${firebaseIdentifier}`,
          {
            isUserExists: isUserExists,
          }
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log("Error while checking user by firebaseIdentifier", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while checking user by firebaseIdentifier`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//check if the email or firstName or lastName is already present in the database
/**
 * @swagger
 * /api/v1/user/checkIfTheEmailAndNameExists:
 *   get:
 *     summary: Check if email, firstName, or lastName already exist in the database
 *     description: Endpoint to check if the email, firstName, or lastName already exist in the database. Returns true if any of the provided parameters match existing user records.
 *     tags:
 *       - User
 *     security:
 *       - apiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: email
 *         required: false
 *         description: Email address of the user.
 *         schema:
 *           type: string
 *       - in: query
 *         name: firstName
 *         required: false
 *         description: First name of the user.
 *         schema:
 *           type: string
 *       - in: query
 *         name: lastName
 *         required: false
 *         description: Last name of the user.
 *         schema:
 *           type: string
 *       - in: query
 *         name: userRole
 *         required: true
 *         description: Role of the user.
 *         schema:
 *           type: string
 *       - in: query
 *         name: phoneNumber
 *         required: false
 *         description: Phone number of the user.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Email or name existence checked successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: This user with email:<EMAIL> or fullName:John Doe isExist:true
 *                 isUserExists:
 *                   type: boolean
 *                   example: true
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while checking email and name
 *                 error:
 *                   type: string
 *                   example: Error message details
 */

router.get(
  "/user/checkIfTheEmailAndNameExists",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      let email = req.query.email;
      const firstName = req.query.firstName;
      const lastName = req.query.lastName;
      const userRole = req.query.userRole;
      const phoneNumber = req.query.phoneNumber;

      //if all the fields are empty then return message
      // if (!email && !firstName && !lastName) {
      //      let response = apiResponse.responseWithStatusCode(
      //           apiResponse.apiConstants.API_REQUEST_SUCCESS,
      //           `Email and firstName and lastName are null`,
      //           `Please provide email or firstName & lastName`
      //      );
      //      return res.status(200).json(response);
      // }

      if (email) {
        console.log(email);
        email = email.replace(/ /g, "+");

        console.log(email);
      }

      if (!userRole) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `userRole is empty`,
          `Please provide userRole`
        );
        return res.status(200).json(response);
      }

      const isEmailOrFirstNameAndLastNameExist =
        await userHelper.checkIfTheEmailAndFirstAndLastNameExist(
          email,
          firstName,
          lastName,
          userRole,
          phoneNumber
        );

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `This user with email:${email} or fullName:${firstName} ${lastName} isExist:${isEmailOrFirstNameAndLastNameExist.data}`,
        { isUserExists: isEmailOrFirstNameAndLastNameExist.data }
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log("Error while checking email and name", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while checking email and name`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//generate email verification code
/**
 * @swagger
 * /api/v1/user/emailVerificationCode:
 *   get:
 *     summary: Generate email verification code
 *     description: Endpoint to generate an email verification code for a given email address.
 *     tags:
 *       - User
 *     security:
 *       - apiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: email
 *         required: true
 *         description: Email address for which the verification code needs to be generated.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Email verification code generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Email verification code is generated successfully
 *                 emailVerificationCode:
 *                   type: string
 *                   example: ABC123
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while generating email verification code
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/user/emailVerificationCode",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const email = req.query.email;

      //check if email is present in the request
      if (!email) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Email is null or empty`,
          `Email is required, please provide a valid email`
        );
        return res.status(200).json(response);
      }

      //generate email verification code
      const emailVerificationCode =
        await firebaseHelper.generateVerificationCode(email);

      //return response
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Email verification code is generated successfully`,
        {
          emailVerificationCode: emailVerificationCode,
        }
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log("Error while generating email verification code", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while generating email verification code`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
// a route for generating a new jwt accessToken
router.get("/generateAccessToken", async (req, res) => {
  const { userId } = req.query;
  const token = generateAccessToken(userId);
  return res.status(200).json({ token });
});
router.get("/user/checkEmailValidation", async (req, res) => {
  const { userId } = req.query;
  const user = await User.findOne({ userId });
  const isEmailVerified = await firebaseHelper.checkEmailVerification(
    user?.contactDetails?.email
  );
  if (isEmailVerified) {
    return res.status(200).json({ isEmailVerified });
  } else {
    return res.status(200).json({ isEmailVerified: false });
  }
});

/**
 * @swagger
 * /api/v1/user/password-status:
 *   put:
 *     summary: Update User Password Change Status
 *     description: Updates the password change status for a user.
 *     tags: [User]
 *     parameters:
 *       - in: body
 *         name: body
 *         description: The user data for updating the password status.
 *         required: true
 *         schema:
 *           type: object
 *           properties:
 *             userId:
 *               type: string
 *               description: The unique ID of the user.
 *             hasChangedPassword:
 *               type: boolean
 *               description: Whether the user has changed their password.
 *             userRole:
 *               type: string
 *               description: The role of the user (e.g., Parent, Student, Tutor, Admin).
 *     responses:
 *       200:
 *         description: Password status updated successfully.
 *       400:
 *         description: Bad request. Missing or invalid parameters.
 *       404:
 *         description: User not found.
 *       500:
 *         description: Internal server error.
 */
router.put("/user/password-status", [verifyApiKey], async (req, res) => {
  try {
    const { userId, hasChangedPassword, userRole, email } = req.body;

    // Validation
    if (hasChangedPassword === undefined || !userRole) {
      return res.status(400).json({
        status: false,
        message:
          "Missing required parameters: userId, hasChangedPassword, or userRole",
      });
    }

    // Call updatePasswordUserStatus
    const result = await userHelper.updatePasswordUserStatus(
      userId,
      hasChangedPassword,
      userRole,
      email
    );

    if (result.status) {
      res.status(200).json({
        status: true,
        message: result.message,
        data: result.data,
      });
    } else {
      res.status(404).json({
        status: false,
        message: "Failed to update password status. " + result.message,
      });
    }
  } catch (error) {
    console.error(error);
    res.status(500).json({
      status: false,
      message: "An error occurred while updating password status.",
      error: error.message,
    });
  }
});

//export router
module.exports = router;
