//import tutorPreferences
const tutorPreferences = require("../../models/Tutor.Preferences.Model.js");
const mongoose = require("mongoose");

const establishmentModel = require("../../models/Establishment.Model.js");

//import user model
const userModel = require("../../models/User.Models.js");
const sendGridHelper = require("../../middleware/mailServiceSendGrid/EmailHelper.js");

//import studentHelper
const studentHelper = require("../../controllers/students/student.helpers.js");
const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const userHelper = require("../user/User.Helper.js");

const matchingHelper = require("../matching/matching.helper.js")
const sessionModel = require("../../models/Sessions.Model");
const crscreportsModel = require("../../models/Cr.Report.Model.js");

const establishmentHelper = require("../establishments/establishments.helper.js");

const firebaseHelper = require("../firebase/firebase.helper.js");
const firebaseFileHelper = require("../firebase/FirebaseFile.Helper.js");
const firebaseConstants = require("../../utils/constants/FirebaseConstants.js");
const tutorJob = require("../../services/aaJobs/tutor.job.js");
const sendGridConstants = require("../../utils/constants/sendgrid.constants.js");

const tutorConstants = require("../../utils/constants/tutor.constants.js");
const StudentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const UserModels = require("../../models/User.Models.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const sessionConstants = require("../../utils/constants/sessions.constants.js");
const { v4: uuidv4 } = require("uuid");
const moment = require("moment");
const filterHelper = require("../../utils/filter.js");
const { getTutorsCountFromPreferences } = require("./tutor.pipelines.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const { ObjectId } = require("mongodb");
const appConstants = require("../../utils/constants/app.constants.js");
const userRolesTranslated = require("../../utils/constants/user.roles.translated.js");
const { checkAffectability } = require("../matching/matching.helper.js");
const migrationDate = moment("2024-09-13");

//get top 3 sessions for tutorId and date range is from now to 6 months
exports.getTop3Sessions = async (tutorId) => {
  try {
    const currentTimestamp = new Date().getTime();

    const listOfSessions = await sessionModel
      .find({
        tutors: {
          $elemMatch: {
            userId: tutorId,
            // , absence: false
          },
        },
        "dateAndTime.timeStamp": { $gt: currentTimestamp },
      })
      .sort({ "dateAndTime.timeStamp": 1 })
      .limit(3)
      .exec();

    if (listOfSessions.length === 0) {
      console.log("No sessions found");
      return { status: false, message: "No sessions found", data: null };
    }

    //get list of students for each session
    let listOfStudents = [];
    for (let i = 0; i < listOfSessions.length; i++) {
      const session = listOfSessions[i];
      const students = session.students;

      for (let j = 0; j < students.length; j++) {
        const student = students[j];
        if (student.absence) return;
        const studentFullName = student.fullName;
        const studentUserId = student.userId;
        const dateAndTime = session.dateAndTime;
        const studentData = { studentFullName, studentUserId, dateAndTime };
        listOfStudents.push(studentData);
      }
    }

    return { status: true, message: "Top 3 sessions", data: listOfStudents };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in getting top 3 sessions",
      data: null,
    };
  }
};

//Get tutor Data for my profile
exports.getTutorSpaceData = async (userId) => {
  try {
    //get data from user model
    const userDocument = await userModel.findOne({ userId: userId }).exec();
    if (!userDocument) {
      return { status: false, message: "User not found", data: null };
    }

    //get data from tutor preferences model
    const tutorPreferencesDocument = await tutorPreferences
      .findOne({ userId: userId })
      .exec();

    //combine data from user model and tutor preferences model
    const tutorData = {
      userId: userDocument.userId,
      firstName: userDocument.contactDetails.firstName,
      lastName: userDocument.contactDetails.lastName,
      email: userDocument.contactDetails.email,
      phoneNumber: userDocument.address.addressLine1,
      dataOfBirth: userDocument.dateOfBirth,
      gender: userDocument.gender,
      address: userDocument.address ? userDocument.address.addressLine1 : "",
      facilityName: tutorPreferencesDocument.facilityName,
      ufrCurriculum: tutorPreferencesDocument.activity.ufrCurriculum,
      fieldOfStudy: tutorPreferencesDocument.education[0]
        ? tutorPreferencesDocument.education[0].fieldOfStudy
        : "",
      howDidYouHearAboutUs: tutorPreferencesDocument.howDidYouHearAboutUs,
      isYourCommitmentValuedByYourInstitution:
        tutorPreferencesDocument.isYourCommitmentValuedByYourInstitution,

      numberOfStudentsToSupport: tutorPreferencesDocument.assignment
        .numberOfStudentsToSupport
        ? tutorPreferencesDocument.assignment.numberOfStudentsToSupport
        : 0,
      availability: tutorPreferencesDocument.availability
        ? tutorPreferencesDocument.availability
        : [],
      materials: tutorPreferencesDocument.materials
        ? tutorPreferencesDocument.materials
        : "",
      comments: tutorPreferencesDocument.activity.comments
        ? tutorPreferencesDocument.activity.comments
        : "",
      // suiviCoordo: tutorPreferencesDocument.activity.suiviCoordo
      //   ? tutorPreferencesDocument.activity.suiviCoordo
      //   : "",
    };

    return { status: true, message: "Tutor data", data: tutorData };
  } catch (error) {
    return {
      status: false,
      message: "Error in getting tutor data",
      data: null,
    };
  }
};

//import userHelper
//const userHelper = require("../user/user.helper.js");

// export function to get tutor preferences
exports.getTutorPreferences = async (userId) => {
  try {
    const tutorPreferencesDocuments = await tutorPreferences.findOne({
      userId: userId,
    });
    
    //get establishment name based on establishment id
    const establishmentInsideAssignment =
      tutorPreferencesDocuments?.assignment?.establishments;
    if (establishmentInsideAssignment) {
      for (let i = 0; i < establishmentInsideAssignment.length; i++) {
        const establishment = establishmentInsideAssignment[i];
        const establishmentId = establishment.establishmentId;
        const establishmentName =
          await establishmentHelper.getEstablishmentName(establishmentId);
        //update establishment name in the list
        establishmentInsideAssignment[i].establishmentName =
          establishmentName || establishment?.establishmentName;
      }
    }

    //update tutor preferences document with establishment name inside assignment
    if (tutorPreferencesDocuments && tutorPreferencesDocuments.assignment) {
      tutorPreferencesDocuments.assignment.establishments =
        establishmentInsideAssignment;
    }

    return tutorPreferencesDocuments;
  } catch (error) {
    console.log(error);
    return null;
  }
};

//#endregion ADMINISTRATOR TUTOR

//create new tutor
exports.createNewTutorIS = async (tutor) => {
  try {
    const userId = userHelper.generateNewUserId();

    //get tutor preferences
    const tutorPreferencesData = {
      ...tutor.tutorPreferences,
      gender: tutor.gender,
      program: studentHelper.addPriorityToProgram(
        tutor?.tutorPreferences?.program?.programId
      ),
    };

    //delete tutor.tutorPreferences from tutor object
    delete tutor.tutorPreferences;

    //create new tutor in User collection
    const newTutorDocument = new userModel({
      userId: userId,
      userRole: userRoleConstants.ROLE_TUTOR,
      hasChangedPassword: true,
      ...tutor,
    });
    const newTutorDocumentSaved = await newTutorDocument.save();
    if (!newTutorDocumentSaved) {
      return {
        status: false,
        message: "Error in creating new tutor",
        data: null,
      };
    }

    //create tutor preferences
    const newTutorPreferencesDocument = new tutorPreferences({
      userId: userId,
      status: newTutorDocumentSaved.status,
      contactDetails: newTutorDocumentSaved.contactDetails,
      ...tutorPreferencesData,
    });
    const newTutorPreferencesDocumentSaved =
      await newTutorPreferencesDocument.save();

    if (!newTutorPreferencesDocumentSaved) {
      let combineData = {
        ...newTutorDocumentSaved._doc,
        tutorPreferences: null,
      };
      return {
        status: true,
        message: "Tutor created successfully",
        data: combineData,
      };
    } else {
      let combineData = {
        ...newTutorDocumentSaved._doc,
        tutorPreferences: newTutorPreferencesDocumentSaved._doc,
      };
      return {
        status: true,
        message: "Tutor created successfully",
        data: combineData,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in creating new tutor",
      data: null,
    };
  }
};

//update tutor
exports.updateTutorIS = async (tutor, connectedUser) => {
  try {
    const tutorPreferencesData = {
      ...tutor.tutorPreferences,
      program: studentHelper.addPriorityToProgram(
        tutor?.tutorPreferences?.program?.programId
      ),
    };
    //delete tutor.tutorPreferences from tutor object
    delete tutor.tutorPreferences;
    const tutorData = tutor;

    const user = await userModel.findOne({ userId: tutor.userId }).exec();
    const userEmail = user.contactDetails.email;

    //update tutor in User in firebase
    const responseFirebase = await firebaseHelper.resetEmail(
      userEmail,
      tutorData.contactDetails.email
    );

    if (!responseFirebase) {
      return {
        status: false,
        message: "Email tutor not found in firebase",
        data: null,
      };
    }

    //update tutor in User collection
    const tutorDocument = await userModel
      .findOneAndUpdate(
        { userId: tutor.userId },
        {
          $set: {
            ...tutorData,
            "firebaseIds.0.identifier": tutorData.contactDetails.email,
          },
        },
        { new: true }
      )
      .exec();

    if (!tutorDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }

    const tutorPreferencesToUpdate = await tutorPreferences.findOne({
      userId: tutor.userId,
    });
    if ((tutorPreferencesToUpdate.situation !== tutorPreferencesData.situation) ||
    (tutorPreferencesToUpdate.status !== tutorPreferencesData.status)
  ) {
      tutorPreferencesData.situationLastUpdate = new Date();
      tutorPreferencesData.situationLastUpdateBy = connectedUser;
      if (
        tutorPreferencesData.situation === tutorConstants.situationOfTutor.STOP ||
        tutorPreferencesData.situation === tutorConstants.situationOfTutor.PAUSE ||
        tutorPreferencesData.situation === tutorConstants.situationOfTutor.FOR_RENEWAL ||
        tutorPreferencesData.status == tutorConstants.statusOfTutors.TUTOR_DEACTIVATED 
      ) {
       const dateNow = dateTimeHelper.getCurrentDateInParisZone();
               const sessionToCancel = await sessionModel.find({
                 "tutors.userId": tutorPreferencesToUpdate.userId,
                 "sessionDate.startDate": { $gte: dateNow },
               });
               // Extraire les sessionIds
               const sessionIds = sessionToCancel.map((session) => session.sessionId);
               const studentsIds = [...new Set(sessionToCancel.flatMap((session) => session.students.map(student => student.userId)))];
               const updateSessionsPromise = sessionModel.updateMany(
                 { sessionId: { $in: sessionIds } },
                 { status: sessionConstants.sessionsStatus.CANCELED }
               );
               // delete matchedStudents
                 const unmatchStudentPromise = StudentPreferencesModel.updateMany(
                  { userId: { $in: studentsIds }, "matching.sessionId": { $in: sessionIds } },
                  {
                  $set: {
                    "matching.$[elem].status":
                    sessionConstants.sessionsStatus.CANCELED, // Mise à jour du statut de chaque session correspondante
                    matchedTutors: [], // Réinitialisation de matchedTutors
                  },
                  },
                  {
                  arrayFilters: [
                    // Filtres pour cibler uniquement les éléments du tableau matching
                    { "elem.sessionId": { $in: sessionIds } },
                  ],
                  new: true,
                  }
                );
        
                const unmatchTutorPromise = TutorPreferencesModel.updateMany(
                  { userId :  tutorPreferencesToUpdate.userId , "matching.sessionId": { $in: sessionIds } }, // Critère pour trouver les sessions à mettre à jour
                  {
                  $set: {
                    "matching.$[elem].status":
                    sessionConstants.sessionsStatus.CANCELED,
                    matchedStudents: [], // Suppression des étudiants correspondant
                  }, // Mise à jour du statut
                  },
                  {
                  arrayFilters: [
                    // Filtres pour cibler uniquement les éléments du tableau matching
                    { "elem.sessionId": { $in: sessionIds } },
                  ],
                  new: true,
                  }
                );
        
                await Promise.all([
                  updateSessionsPromise,
                  unmatchStudentPromise,
                  unmatchTutorPromise,
                ]);
      }
    }
    //update tutor preferences
    tutorPreferencesData.contactDetails = tutorDocument.contactDetails;
    tutorPreferencesData.iAcceptToBeContactedForParticipatingInAStudy =
      tutor.iAcceptToBeContactedForParticipatingInAStudy;
    tutorPreferencesData.gender = tutorDocument.gender;
    tutorPreferencesData.status = tutorDocument.status;
    if (tutorDocument.status === tutorConstants.statusOfTutors.TUTOR_ACTIVE) {
      tutorPreferencesData.onBoardingStatus =
        tutorConstants.onBoardingStatus.ON_BOARDING_COMPLETED;
    } else {
      tutorPreferencesData.onBoardingStatus =
        tutorConstants.onBoardingStatus.ON_BOARDING_IN_REVIEW;
    }
    const tutorPreferencesDocument = await tutorPreferences
      .findOneAndUpdate({ userId: tutor.userId }, tutorPreferencesData, {
        new: true,
      })
      .exec();
    if (!tutorPreferencesDocument) {
      return { status: false, message: "Error in updating tutor", data: null };
    }

    if (
      tutorPreferencesToUpdate.situation !== tutorPreferencesData.situation ||
      tutorPreferencesToUpdate.status !== tutorPreferencesData.status
    ) {
      if (
        tutorPreferencesData.situation ===
        tutorConstants.situationOfTutor.COMPLETE
      ) {
        await tutorJob.sendNotificationToTutor({
          email: tutorDocument.contactDetails.email,
          data: {
            firstName: tutorDocument.contactDetails.firstName,
          },
          templateId:
            sendGridConstants.EmailTemplates
              .TEMPLATE_TUTOR_EMAIL_COMPLETE_STATUT,
        });
      }
      if (
        tutorPreferencesData.situation ===
        tutorConstants.situationOfTutor.AWAITING_STUDENT_1
      ) {
        await tutorJob.sendNotificationToTutor({
          email: tutorDocument.contactDetails.email,
          data: {
            firstName: tutorDocument.contactDetails.firstName,
          },
          templateId:
            sendGridConstants.EmailTemplates
              .TEMPLATE_TUTOR_EMAIL_AWAITING_STUDENT_1_STATUT,
        });
      }
    }

    const combineData = {
      ...tutorDocument._doc,
      tutorPreferences: tutorPreferencesDocument._doc,
    };
    return {
      status: true,
      message: "Tutor updated successfully",
      data: combineData,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in updating tutor", data: null };
  }
};

//delete tutor
exports.deleteTutorIS = async (userId) => {
  try {
    console.log("delete tutor");
    const userDocument = await userModel.findOne({ userId: userId }).exec();
    if (!userDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    const firebaseIds = userDocument.firebaseIds;

    await userDocument.delete();

    //delete tutor preferences
    const tutorPreferencesDocument = await tutorPreferences
      .findOne({ userId: userId })
      .exec();
    if (tutorPreferencesDocument) {
      await tutorPreferencesDocument.delete();
    }

    //check if userDocument and tutorPreferencesDocument are deleted
    if (userDocument && tutorPreferencesDocument) {
      //delete firebaseIds
      if (firebaseIds) {
        for (let i = 0; i < firebaseIds.length; i++) {
          const firebaseId = firebaseIds[i];
          await firebaseHelper.deleteUserFromAuth(firebaseId.identifier);
        }
      }
      return {
        status: true,
        message: "Tutor deleted successfully",
        data: null,
      };
    } else {
      return { status: false, message: "Error in deleting tutor", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in deleting tutor", data: null };
  }
};

//Get All Tutors base on tutorType, and return establishmentName from establishment  collection
exports.getIsTutorsDashboard = async (payload) => {
  try {
    let { filter, sortBy, page, pageSize, userId, userRole, strictProgramFilter } = payload;
    pageSize = buildPageSize(pageSize);
    page = buildPage(page);
    let filterData = buildFilterForPreferences(filter);
    if (!filterData.status) {
      return { status: false, message: filterData.message };
    }
    const { tutor, preference } = filterData.data;

    // Check if the user is an admin and his role in [coordinator or vsc]
    if (
      [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(userRole)
    ) {
      // get the admin preferences model of the user
      const userAdminDetails = await UserAdministrationModel.findOne({
        userId,
      });

      // get the program preferences of the user
      const pref = userAdminDetails?.administrationPreferences;

      // check if the user has program preferences
      if (pref && pref.program?.length ) {
        const samePrograms = [
          HOME_CLASSES.programId,
          ZUPDEFOOT.programId,
          CLASSSES_HOME.programId,
        ];
        if(!preference["program.programId"]) {
          if (samePrograms.includes(pref.program[0].programId) ) {
            //console.log("userAdminDetails===>", here);
  
            preference["program.programId"] = {
              $in: samePrograms,
            };
          } else {
            preference["program.programId"] = {
              $in: pref.program.map((prog) => prog.programId),
            };
          }
        }
        // add the program preferences to the filter to match the student preferences with the programId got from admin preferences model

        // get the useradmin program from the program preferences
        const program = pref.program[0];

        // check if the program is (DEVOIRS_FAITS or ZUPDEFOOT) Or Home Classe
        if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
          //Tutorat solidaire or ZupdeFoot userAdmin

          // Step 1
          // get the establishmentIds from the admin preferences model
          const establishmentIds = pref.establishment.map(
            (est) => est.establishmentId
          );
          // Step 2
          //  check if the user has establishmentIds
          if (!establishmentIds?.length) {
            // in case of coordinator/vsc has no establishmentIds in his admin preferences model
            preference["_id"] = null;
          } else {
            // Step 3
            // in case of coordinator/vsc has multiple establishmentIds in his admin preferences model
            // add establishmentIds to the filter to match the student preferences with the programId && establishementIds got from admin preferences model
            // Example : this id "651d9d1cf5f4cd498806d217" is an example of establishmentId that has tutors in Home Class program
            // Example Explanation : For Test (You should have a HC userAdmin coord/vsc that assigned to this establishment:"651d9d1cf5f4cd498806d217")
            preference["assignment.establishments.establishmentId"] = {
              $in: establishmentIds,
            };
          }
        }
      }
    }

    let sortByObject = buildSortBy(sortBy);
    const baseQuery = {
      ...preference,
      ...tutor,
      createdAt: { $exists: true },
    };
    const listOfTutors = await TutorPreferencesModel.find(baseQuery)
      .sort(sortByObject)
      .skip((page - 1) * pageSize)
      .limit(pageSize)
      .exec();
    const totalNumberOfTutors = await TutorPreferencesModel.countDocuments(
      baseQuery
    );
    let listOfTutorsWithPreferences = [];
    for (let i = 0; i < listOfTutors.length; i++) {
      const tutor = listOfTutors[i];
      const userId = tutor.userId;
      // console.log("userId", userId.toString());
      // _id = mongoose.Types.ObjectId(userId.toString());
      // console.log("_iddd", _id);
      const userModel = await UserModels.findOne({ userId: userId });
      let establishmentNames = [];
      const listOfEstablishments = tutor.assignment.establishments;
      for (let j = 0; j < listOfEstablishments.length; j++) {
        const establishment = listOfEstablishments[j];
        const establishmentId = establishment?.establishmentId;
        if (!establishmentId) {
          continue;
        }
        const establishmentName =
          await establishmentHelper.getEstablishmentName(
            establishment.establishmentId
          );
        establishmentNames.push(establishmentName);
      }

      //convert to string  establishmentName with , separator
      establishmentNames = establishmentNames.join(", ");
      //return firstName, lastName, typeOfTutor, status, establishmentName
      const isBeforeMigrationDate = moment(tutor.createdAt).isBefore(
        migrationDate
      );
      const folderStatus =
        tutor?.status === "active" &&
        tutor?.availability?.length > 0 &&
        tutor?.assignment?.level?.length > 0 &&
        tutor?.assignment?.numberOfStudentsToSupport > 0 &&
        tutor?.subjectToTeach &&
        tutor?.gender &&
        (tutor?.contactDetails?.phoneNumber || tutor?.contactDetails?.email) &&
        (isBeforeMigrationDate
          ? true
          : tutor?.documents?.find(
              (doc) => doc.documentType === "criminal_record"
            )?.documentType === "criminal_record");
            const sessionPassesNbre = await sessionModel.countDocuments({
              status: "session-0-to-be-scheduled",
              "tutors.userId": tutor.userId,
              "sessionDate.startDate": { $lte: new Date() },
              parentSessionId : {$ne : null}
            });
            const nbrCr = await crscreportsModel.countDocuments({
              status: { $ne: "to-be-entered" },
              "tutors.userId": tutor.userId,
            });
            const aggregationResult = await crscreportsModel.aggregate([
              {
                $match: {
                  status: { $ne: "to-be-entered" },
                  "tutors.userId": tutor.userId
                }
              },
              {
                $group: {
                  _id: null,
                  sumGrade: { $sum: "$grade" },
                  count: { $sum: 1 }
                }
              }
            ]);
            const SumGradeOptimized = aggregationResult.length > 0 ? aggregationResult[0].sumGrade : 0;
            // console.log("repereeeeee", SumGradeOptimized, nbrCr)
            let tutorWithPreferences = {
        userId: userId,
        firstName: tutor?.contactDetails?.firstName,
        lastName: tutor?.contactDetails?.lastName,
        email: tutor?.contactDetails?.email,
        zipCode: userModel?.address.zipCode,
        typeOfTutor: tutor.typeOfTutor ? tutor.typeOfTutor : "",
        status: tutor.status,
        situation: tutor.situation,
        program: tutor.program ? tutor.program : "",
        establishmentName: establishmentNames,
        department: tutor.assignment.department.departmentName
          ? extractCityName(tutor.assignment.department.departmentName) +
            ` (${tutor.assignment.department.departmentId})`
          : "",
        createdAt: tutor.createdAt,
        iAcceptToBeContactedForParticipatingInAStudy:
          tutor.iAcceptToBeContactedForParticipatingInAStudy,
        folderStatus: folderStatus,
        onBoardingStep: tutor.onBoardingStep || "1",
        homeDepartment: tutor.homeDepartment?.departmentName
          ? extractCityName(tutor.homeDepartment.departmentName) +
            ` (${tutor.homeDepartment.departmentId})`
          : "",
        activity: tutor.activity,
        gender: tutor?.gender || "",
        subjectToTeach: tutor?.subjectToTeach || "",
        matchedStudents: tutor.matchedStudents || [],
        numberOfStudentsToSupport: tutor.assignment?.numberOfStudentsToSupport || 0,
         sessionNbre : sessionPassesNbre || 0,
         nbreCr : nbrCr || 0,
        pourcentageCr : (nbrCr/sessionPassesNbre) *100 || 0,
        note :  nbrCr > 0 ? Math.floor(SumGradeOptimized / nbrCr) : 0,
      };
      listOfTutorsWithPreferences.push(tutorWithPreferences);
    }

    return {
      status: true,
      message: "Tutors",
      data: listOfTutorsWithPreferences,
      totalNumberOfTutors: totalNumberOfTutors,
      page: page,
      pageSize: pageSize,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: "Error in getting tutors", data: null };
  }
};
function extractCityName(input) {
  // Utilise une expression régulière pour extraire le nom de la ville, en éliminant les codes éventuels
  const match = input.match(/^(.+?)(?:\s*\(\d{2}\))?$/);

  if (match) {
    // Extrait et retourne le nom de la ville sans les codes
    return match[1];
  }

  // Retourne l'entrée d'origine si aucune correspondance n'est trouvée
  return input;
}

function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null) {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null) {
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }

  return page;
}

//in this function I need to check if the filter is json or not and If one of the keys is firstName or lastName or email
function checkIfFilterContainsFirstNameLastNameOrEmail(filter) {
  try {
    let isContain = false;
    for (const [key, value] of Object.entries(filter)) {
      console.log(`Filter data:  ${key}: ${value}`);
      if (
        key == "contactDetails.firstName" ||
        key == "contactDetails.lastName" ||
        key == "contactDetails.email"
      ) {
        isContain = true;
        break;
      }
    }
    console.log(
      "Filter is contain firstName or lastName or email: ",
      isContain
    );
    return isContain;
  } catch (error) {
    return false;
  }
}
// Helper function to create regular expression object for a given key and value
function createRegExpObject(key, value) {
  return {
    [`contactDetails.${key}`]: { $regex: value, $options: "i" },
  };
}
function buildFilterForPreferences(filter) {
  let parsedFilter = {};
  //check if the filter is json or not
  if (filter) {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      parsedFilter = JSON.parse(filter);
    }
  }
  //filter with value string RegExp
  let tutorFilter = {};
  let preferencesFilter = {};
  for (const [key, value] of Object.entries(parsedFilter)) {
    // Gestion de la recherche globale (nom, prénom, email)
    if (key === "globalSearch" && value) {
      tutorFilter = {
        ...tutorFilter,
        $or: [
          { "contactDetails.firstName": { $regex: value, $options: "i" } },
          { "contactDetails.lastName": { $regex: value, $options: "i" } },
          { "contactDetails.email": { $regex: value, $options: "i" } },
          { "userId": { $regex: value, $options: "i" } }
        ]
      };
      continue; // Passer à l'itération suivante
    }

    tutorFilter =
      key === "lastName" || key === "firstName"
        ? { ...tutorFilter, ...createRegExpObject(key, value) }
        : key === "email"
        ? {
            ...tutorFilter,
            "contactDetails.email": { $regex: value, $options: "i" },
          }
        : key === "status"
        ? { ...tutorFilter, status: { $regex: value, $options: "i" } }
        : { ...tutorFilter };

    preferencesFilter =
      key === "program"
        ? {
            ...preferencesFilter,
            "program.programId": { $regex: value, $options: "i" },
          }
        : key === "department"
        ? {
            ...preferencesFilter,
            "assignment.department.departmentId": {
              $regex: value,
              $options: "i",
            },
          }
        : key === "onBoardingStep"
        ? {
            ...preferencesFilter,
            onBoardingStep: {
              $regex: value,
              $options: "i",
            },
          }
        : key === "establishment"
        ? {
            ...preferencesFilter,
            "assignment.establishments": {
              $elemMatch: {
                establishmentId: value,
              },
            },
          }
        : key === "partner"
        ? {
            ...preferencesFilter,
            partner: { $regex: value, $options: "i" },
          }
        : key === "situation"
        ? Array.isArray(value)
          ? {
              ...preferencesFilter,
              situation: { $in: value },
            }
          : {
              ...preferencesFilter,
              situation: value,
            }
        : key === "activeCommitment" && value === true
        ? {
            ...preferencesFilter,
            "commitment.endDate": { $lte: new Date().setDate(new Date().getDate() + 30) }
          }
          : key === "thirtyDaysAgo" && value === true
        ? {
            ...preferencesFilter,
            "createdAt": { $gte: new Date().setDate(new Date().getDate() - 30) }
          }
        : key === "study"
        ? {
            ...preferencesFilter,
            iAcceptToBeContactedForParticipatingInAStudy: value,
          }
        : key === "folderStatus" && value === "complete"
        ? {
            ...preferencesFilter,
            status: "active",
            availability: { $exists: true, $ne: [] },

            "assignment.level": { $exists: true, $ne: [] },
            "assignment.numberOfStudentsToSupport": { $exists: true, $gt: 0 },
            subjectToTeach: { $exists: true, $nin: ["", null] },
            gender: { $exists: true, $nin: ["", null] },
            $or: [
              {
                createdAt: { $gt: migrationDate }, // createdAt >= migrationDate
                $or: [
                  { documents: { $ne: [] } }, // Vérifie si documents n'est pas un tableau vide
                  { "documents.documentType": { $eq: "criminal_record" } }, // Vérifie si documentType est égal à "criminal_record"
                ],
              },
              // Cas 2 : Si createdAt est inférieur ou égal à migrationDate
              {
                createdAt: { $lte: migrationDate }, // createdAt <= migrationDate
              },
              // Vérification des numéros de téléphone et des emails

              {
                "contactDetails.phoneNumber": { $exists: true, $eq: "" },
                $or: [
                  {
                    "contactDetails.email": { $exists: true, $eq: "" },
                  },
                ],
              },
            ],
          }
        : key === "folderStatus" && value === "incomplete"
        ? {
            ...preferencesFilter,
            $or: [
              {
                status: { $ne: "active" },
              },
              { availability: { $exists: true, $eq: [] } },
              { "assignment.level": { $exists: true, $eq: [] } },
              { "assignment.numberOfStudentsToSupport": { $exists: false } },
              { "assignment.numberOfStudentsToSupport": { $eq: 0 } },
              {
                $or: [
                  { subjectToTeach: { $exists: false } },
                  { subjectToTeach: { $exists: true, $eq: "" } },
                ],
              },
              {
                $or: [
                  { gender: { $exists: true, $eq: "" } },
                  { gender: { $exists: false } },
                ],
              },
              { "contactDetails.phoneNumber": { $exists: true, $eq: "" } },
              { "contactDetails.email": { $exists: true, $eq: "" } },
              {
                $nor: [
                  {
                    createdAt: { $gt: migrationDate }, // createdAt >= migrationDate
                    $or: [
                      { documents: { $ne: [] } }, // Vérifie si documents n'est pas un tableau vide
                      { "documents.documentType": { $eq: "criminal_record" } }, // Vérifie si documentType est égal à "criminal_record"
                    ],
                  },
                  {
                    createdAt: { $lte: migrationDate }, // createdAt <= migrationDate
                  },
                ],
              },
            ],
          }
        : key === "affectable" 
        ? {
          ...preferencesFilter,
          matchedStudents :  { $exists: true, $eq: [] },
        }
        : key === "hasCapacity" 
        ? {
          ...preferencesFilter,
          "matchedStudents": { $exists: true, $not: { $size: 0 } },
  
  $expr: {
    $lt: [
      { $size: "$matchedStudents" },
      "$assignment.numberOfStudentsToSupport"
    ]
  }
}
        : { ...preferencesFilter };
  }

  const finalfilter = {
    tutor: tutorFilter,
    preference: preferencesFilter,
    userRole: userRoleConstants.ROLE_TUTOR,
  };

  return { status: true, data: finalfilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      } else if (key == "firstName") {
        key = "contactDetails.firstName";
      } else if (key == "email") {
        key = "contactDetails.email";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["createdAt", -1]];
  }
  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

//update userStatus base on userId
exports.updateTutorPreferencesStatus = async (userId, status) => {
  try {
    const userDocument = await tutorPreferences
      .findOneAndUpdate({ userId: userId }, { status: status }, { new: true })
      .exec();
    return {
      status: true,
      message: `User status updated successfully`,
      data: userDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//update user Admin with invitationCode
exports.updateUserAdminWithInvitationCode = async (
  userId,
  invitationCode,
  invitationAccepted
) => {
  try {
    let userDocument = await tutorPreferences
      .findOne({ userId: userId })
      .exec();

    if (!userDocument) {
      console.log("User not found");
      return { status: false, message: "User not found", data: null };
    }

    if (invitationAccepted) {
      userDocument.invitation.invitationAccepted = true;
      userDocument.invitation.invitationAcceptedDate = new Date();
    } else {
      //setup invitation object
      let invitationObject = {
        invitationCode: invitationCode,
        invitationAccepted: false,
        invitationDate: new Date(),
        invitationAcceptedDate: null,
      };

      //add invitation object to userDocument
      userDocument.invitation = invitationObject;
    }

    //save userDocument
    let updatedUserDocument = await userDocument.save();

    return {
      status: true,
      message: "Invitation code updated successfully",
      data: updatedUserDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//get tutorProfile by userId
exports.getTutorProfile = async (userId) => {
  try {
    const tutorProfileDocument = await userModel
      .findOne({ userId: userId })
      .exec();
    if (!tutorProfileDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    const phoneNumber = tutorProfileDocument.contactDetails.phoneNumber
      ? tutorProfileDocument.contactDetails.phoneNumber
      : "";
    const tutorProfile = {
      userId: tutorProfileDocument.userId,
      profilePic: tutorProfileDocument.profilePic,
      fullName:
        tutorProfileDocument.contactDetails.firstName +
        " " +
        tutorProfileDocument.contactDetails.lastName,
      email: tutorProfileDocument.contactDetails.email
        ? tutorProfileDocument.contactDetails.email
        : "",
      phoneNumber: phoneNumber,
    };

    return { status: true, message: `Tutor profile found`, data: tutorProfile };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

// get tutorFullName by userId
exports.tutorFullName = async (userId) => {
  try {
    const tutorProfileDocument = await userModel
      .findOne({ userId: userId })
      .exec();
    if (!tutorProfileDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    const tutorFullName =
      tutorProfileDocument.contactDetails.firstName +
      " " +
      tutorProfileDocument.contactDetails.lastName;

    return {
      status: true,
      message: `Tutor profile found`,
      data: tutorFullName,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//get tutorProfile by userId
exports.getTutorProfiles = async (tutorIds) => {
  try {
    const tutorProfileDocument = await userModel
      .find({ userId: { $in: tutorIds } })
      .exec();

    if (!tutorProfileDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }

    let tutorProfiles = [];
    tutorProfileDocument.forEach((tutorProfileItem) => {
      const tutorProfile = {
        userId: tutorProfileItem.userId,
        profilePic: tutorProfileItem.profilePic,
        fullName:
          tutorProfileItem.contactDetails.firstName +
          " " +
          tutorProfileItem.contactDetails.lastName,
        email: tutorProfileItem.contactDetails.email,
        phoneNumber: tutorProfileItem.contactDetails.phoneNumber,
      };
      tutorProfiles.push(tutorProfile);
    });

    return {
      status: true,
      message: `Tutor profile found`,
      data: tutorProfiles,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//get sector of Tutor by userId
exports.getSectorsOfTutor = async (userId) => {
  try {
    const tutorPreferencesDocument = await tutorPreferences
      .findOne({ userId: userId })
      .exec();

    if (!tutorPreferencesDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }

    const listOfSectorIds = tutorPreferencesDocument.assignment.sectors.map(
      (sector) => sector.sectorId
    );

    return {
      status: true,
      message: `Tutor sectors found`,
      data: listOfSectorIds,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//#endregion ADMINISTRATOR TUTOR

//number of tutors per sector
exports.getNumberOfTutorsPerSector = async (sectorId) => {
  try {
    //convert sectorId from ObjectId to string
    sectorId = sectorId.toString();
    //count total number of students  per sector
    const numberOfTutors = await tutorPreferences
      .countDocuments({ "assignment.sectors.sectorId": sectorId })
      .exec();
    return {
      status: true,
      message: "Number of students per sector",
      data: numberOfTutors.toString(),
    };
  } catch (error) {
    console.log("studentHelper.getNumberOfStudentsPerSector", error);
    return {
      status: false,
      message: `Error in getting number of students per sector ${error.message}`,
      data: 0,
    };
  }
};
exports.getNumberOfTutorsPerDepartment = async (department) => {
  try {
    //convert departmentId from ObjectId to string
    department = department.toString();
    //count total number of students  per department
    const numberOfTutors = await tutorPreferences
      .countDocuments({ "assignment.department.departmentId": department })
      .exec();
    return {
      status: true,
      message: "Number of students per department",
      data: numberOfTutors.toString(),
    };
  } catch (error) {
    console.log("studentHelper.getNumberOfStudentsPerDepartment", error);
    return {
      status: false,
      message: `Error in getting number of students per department ${error.message}`,
      data: 0,
    };
  }
};

//get Tutor commitment start date  and end date by tutorId
exports.getTutorCommitmentStartDateAndEndDate = async (tutorId) => {
  try {
    const tutorDocument = await tutorPreferences
      .findOne({ userId: tutorId })
      .exec();
    if (!tutorDocument) {
      return { status: false, message: "Tutor not found", data: null };
    }
    const commitmentStartDate = tutorDocument.commitment.startDate;
    const commitmentEndDate = tutorDocument.commitment.endDate;
    return {
      status: true,
      message: `Tutor commitment start date and end date found`,
      data: {
        commitmentStartDate: commitmentStartDate,
        commitmentEndDate: commitmentEndDate,
      },
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//get list of userIds baaed on sectorId
exports.getListOfTutorIdsBySectorId = async (sectorId) => {
  try {
    const listOfTutorIds = await tutorPreferences
      .find({
        "assignment.sectors.sectorId": sectorId,
        status: tutorConstants.statusOfTutors.TUTOR_ACTIVE,
      })
      .distinct("userId")
      .exec();
    return { status: true, message: "List of tutorIds", data: listOfTutorIds };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

exports.getTutorForSituationReminderIncomplet1 = async () => {
  try {
    const currentDate = dateTimeHelper.getCurrentDateTimeInParisZone();
    const dateSubstracted = dateTimeHelper.subtractDaysFromDate(currentDate, 5);

    const listOfTutors = await tutorPreferences
      .find({
        situation: tutorConstants.situationOfTutor.NEW,
        status: tutorConstants.statusOfTutors.TUTOR_ACTIVE,
        situationLastUpdate: {
          $lte: dateSubstracted,
        },
      })
      .exec();
    return { status: true, message: "List of tutors", data: listOfTutors };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

exports.getTutorForSituationReminderIncomplet2 = async () => {
  try {
    const currentDate = dateTimeHelper.getCurrentDateTimeInParisZone();
    const dateSubstracted = dateTimeHelper.subtractDaysFromDate(
      currentDate,
      12
    );

    const listOfTutors = await tutorPreferences
      .find({
        situation: tutorConstants.situationOfTutor.INCOMPLETE_RELANCE_1,
        status: tutorConstants.statusOfTutors.TUTOR_ACTIVE,
        situationLastUpdate: {
          $lte: dateSubstracted,
        },
      })
      .exec();
    return { status: true, message: "List of tutors", data: listOfTutors };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

exports.updateTutorSituation = async (tutorId, situation) => {
  await tutorPreferences
    .findOneAndUpdate(
      { userId: tutorId },
      { situation: situation, situationLastUpdate: new Date() },
      { new: true }
    )
    .exec();
};

exports.updateTutorAvailability = async ({
  tutorId,
  availability_id,
  taken,
}) => {
  try {
    const updatedDocument = await tutorPreferences
      .findOneAndUpdate(
        { userId: tutorId, "availability._id": ObjectId(availability_id) },
        { $set: { "availability.$.taken": taken } },
        { new: true } // Return the modified document, but only the matched availability
      )
      .exec();
    if (!updatedDocument) {
      return {
        status: false,
        message: "Tutor or Availability not found",
        data: null,
      };
    }
    await this.updateTutorOnBoardingStatus(tutorId);
    return {
      status: true,
      message: "Availability updated successfully",
      data: updatedDocument.availability[0],
    };
  } catch (error) {
    console.error(error);
    return { status: false, message: error.message, data: error.message };
  }
};
exports.updateTutorOnBoardingStatus = async (tutorId) => {
  try {
    const tutorDocument = await UserModels.findOne({
      userId: tutorId,
    }).exec();

    if (!tutorDocument) {
      return {
        status: false,
        message: "Tutor not found",
        data: null,
      };
    }
    if (tutorDocument.status === tutorConstants.statusOfTutors.TUTOR_ON_HOLD) {
      tutorDocument.status = tutorConstants.statusOfTutors.TUTOR_ACTIVE;
      await tutorDocument.save();
    }
    return {
      status: true,
      message: "Availability updated successfully",
      data: tutorDocument,
    };
  } catch (error) {
    console.error(error);
    return { status: false, message: error.message, data: error.message };
  }
};
exports.sendInvitationEmailToTutor = async (userDetails, sendEmailOnCreate) => {
  try {
    const { email } = userDetails.contactDetails;
    const { userId } = userDetails;
    const { label: role } = userRolesTranslated.find(
      (role) => role.id === userDetails.userRole
    );
    const fullName = `${userDetails.contactDetails.firstName} ${userDetails.contactDetails.lastName}`;

    // Generate invitation code
    const invitationCode = email + "=" + uuidv4();

    const invitationLink = `${appConstants.APP_BASE_URL}/tutor/register/?accept=${invitationCode}&firebaseIdentifier=${email}&userId=${userId}`;
    const emailSubject = `Invited to join zupdeco.org`;
    const setupInvitation = sendGridHelper.invitationToAdmin(
      email,
      emailSubject,
      invitationLink,
      fullName,
      role
    );

    const invitationData = {
      setupInvitation,
      invitationCode,
      email,
    };

    if (sendEmailOnCreate) {
      try {
        await sendGrid.send(invitationData.setupInvitation);
        return apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation sent successfully`,
          `Invitation sent successfully to ${invitationData.email}, please notify user-admin to check email. Invitation code: ${invitationData.invitationCode}`,
          apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESS
        );
      } catch (error) {
        console.log("ERROR =>", error);
        if (error.response) {
          console.error(error.response.body);
          console.log("ERROR BODY =>", error.response.body);
        }
        console.error("Error while sending invitation email", error);
        return apiResponse.responseWithStatusCode(
          false,
          `Error while sending invitation email`,
          error,
          apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
        );
      }
    } else {
      return invitationData;
    }
  } catch (error) {
    return { status: false, data: error };
  }
};
exports.encodeBase64 = (data) => {
  const jsonData = JSON.stringify(data); // On transforme les données en JSON string
  return Buffer.from(jsonData).toString("base64"); // On encode le JSON en base64
};
/**
 * Fonction pour obtenir les statistiques des tuteurs
 * Retourne le nombre total de tuteurs, complets, non complets, par étape d'inscription et par statut
 */
exports.getTutorStatistics = async (payload) => {
  try {
    let { filter, userId, userRole } = payload;
    let filterData = buildFilterForPreferences(filter);
    if (!filterData.status) {
      return { status: false, message: filterData.message };
    }
    const { tutor, preference } = filterData.data;

    // Vérification pour les administrateurs (coordinator ou vsc)
    if (
      [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(userRole)
    ) {
      const userAdminDetails = await UserAdministrationModel.findOne({
        userId,
      });

      const pref = userAdminDetails?.administrationPreferences;

      if (pref && pref.program?.length) {
        const samePrograms = [
          HOME_CLASSES.programId,
          ZUPDEFOOT.programId,
          CLASSSES_HOME.programId,
        ];
        if (samePrograms.includes(pref.program[0].programId)) {
          preference["program.programId"] = {
            $in: samePrograms,
          };
        } else {
          preference["program.programId"] = {
            $in: pref.program.map((prog) => prog.programId),
          };
        }

        const program = pref.program[0];
        if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
          const establishmentIds = pref.establishment.map(
            (est) => est.establishmentId
          );
          if (!establishmentIds?.length) {
            preference["_id"] = null;
          } else {
            preference["assignment.establishments.establishmentId"] = {
              $in: establishmentIds,
            };
          }
        }
      }
    }

    const baseQuery = {
      ...preference,
      ...tutor,
      createdAt: { $exists: true },
     
    };

    // Obtenir le nombre total de tuteurs
    const totalNumberOfTutors = await TutorPreferencesModel.countDocuments(
      baseQuery
    );
    // const datStat = await matchingHelper.getMatchingStats()
    const numberOfUnmatchedTutors = await TutorPreferencesModel.countDocuments(
      { ...baseQuery,matchedStudents: { '$exists': true, '$eq': [] }}
    );
    const numberOfPartialUnmatedTutors = await TutorPreferencesModel.countDocuments(
      { ...baseQuery, 
        matchedStudents: { $exists: true, $not: { $size: 0 } },
        $expr: {
          $and: [
            { $gt: [{ $size: "$matchedStudents" }, 0] },
            {
              $lt: [
                { $size: "$matchedStudents" },
                "$assignment.numberOfStudentsToSupport",
              ],
            },
          ],
        }}
    );
    // Compter les tuteurs avec dossier complet
    const completeFolderQuery = {
      ...baseQuery,
      status: "active",
      availability: { $exists: true, $ne: [] },
      "assignment.level": { $exists: true, $ne: [] },
      "assignment.numberOfStudentsToSupport": { $exists: true, $gt: 0 },
      subjectToTeach: { $exists: true, $nin: ["", null] },
      gender: { $exists: true, $nin: ["", null] },
      $or: [
        {
          createdAt: { $gt: migrationDate },
          $or: [
            { documents: { $ne: [] } },
            { "documents.documentType": { $eq: "criminal_record" } },
          ],
        },
        { createdAt: { $lte: migrationDate } },
        {
          "contactDetails.phoneNumber": { $exists: true, $eq: "" },
          $or: [{ "contactDetails.email": { $exists: true, $eq: "" } }],
        },
      ],
    };
    const completeFolderCount = await TutorPreferencesModel.countDocuments(
      completeFolderQuery
    );

    // Compter les tuteurs avec dossier incomplet
    const incompleteFolderQuery = {
      ...baseQuery,
      $or: [
        { status: { $ne: "active" } },
        { availability: { $exists: true, $eq: [] } },
        { "assignment.level": { $exists: true, $eq: [] } },
        { "assignment.numberOfStudentsToSupport": { $exists: false } },
        { "assignment.numberOfStudentsToSupport": { $eq: 0 } },
        {
          $or: [
            { subjectToTeach: { $exists: false } },
            { subjectToTeach: { $exists: true, $eq: "" } },
          ],
        },
        {
          $or: [
            { gender: { $exists: true, $eq: "" } },
            { gender: { $exists: false } },
          ],
        },
        { "contactDetails.phoneNumber": { $exists: true, $eq: "" } },
        { "contactDetails.email": { $exists: true, $eq: "" } },
        {
          $nor: [
            {
              createdAt: { $gt: migrationDate },
              $or: [
                { documents: { $ne: [] } },
                { "documents.documentType": { $eq: "criminal_record" } },
              ],
            },
            { createdAt: { $lte: migrationDate } },
          ],
        },
      ],
    };
    const incompleteFolderCount = await TutorPreferencesModel.countDocuments(
      incompleteFolderQuery
    );

    // Compter les tuteurs avec situation "incomplete-relance-1" ou "incomplete-relance-2"
    const incompleteRelanceQuery = {
      ...baseQuery,
      situation: { $in: ["incomplete-relance-1", "incomplete-relance-2"] }
    };
    const incompleteRelanceCount = await TutorPreferencesModel.countDocuments(
      incompleteRelanceQuery
    );

    // Compter les tuteurs avec engagement actif (date de fin >= aujourd'hui)
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(new Date().getDate() + 30);
    const activeCommitmentQuery = {
      ...baseQuery,
      "commitment.endDate": { $lte: thirtyDaysFromNow }
    };
    const activeCommitmentCount = await TutorPreferencesModel.countDocuments(
      activeCommitmentQuery
    );
    const thirtyDaysAgo = new Date();
   thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const newTutorCount = await TutorPreferencesModel.countDocuments(
      {createdAt: { $gte: thirtyDaysAgo }});
    // Obtenir les statistiques par étape d'inscription
    const onBoardingStepPipeline = [
      
      { $match: baseQuery },
      {
        $group: {
          _id: { $ifNull: ["$onBoardingStep", "1"] },
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ];

    const onBoardingStepStats = await TutorPreferencesModel.aggregate(
      onBoardingStepPipeline
    );

    // Obtenir les statistiques par statut
    const statusPipeline = [
      { $match: baseQuery },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ];

    const statusStats = await TutorPreferencesModel.aggregate(statusPipeline);

    // Obtenir les statistiques par situation
    const situationPipeline = [
      { $match: baseQuery },
      {
        $group: {
          _id: "$situation",
          count: { $sum: 1 },
        },
      },
      { $sort: { _id: 1 } },
    ];

    const situationStats = await TutorPreferencesModel.aggregate(situationPipeline);

    // Formater les résultats
    const onBoardingStepData = {
      "1": 0,
      "2": 0,
      "3": 0,
      "5": 0,
      "6": 0,
      "7": 0,
    };
    onBoardingStepStats.forEach((item) => {
      onBoardingStepData[item._id] = item.count;
    });

    const statusData = {
      "active": 0,
      "inactive": 0,
      "pending": 0,
    };
    statusStats.forEach((item) => {
      statusData[item._id || "undefined"] = item.count;
    });

    const situationData = {
      "incomplete": 0,
      "complete": 0,
      "stop": 0,
      "pause": 0,
      "for_renewal": 0,
      "incomplete_relance_1": 0,
      "incomplete_relance_2": 0,
    };
    situationStats.forEach((item) => {
      situationData[item._id || "undefined"] = item.count;
    });

    return {
      status: true,
      message: "Statistiques des tuteurs",
      data: {
        totalNumberOfTutors,
        numberOfUnmatchedTutors,
        numberOfPartialUnmatedTutors,
        newTutorCount,
        completeFolderCount,
        incompleteFolderCount,
        incompleteRelanceCount,
        activeCommitmentCount,
        onBoardingStepStats: onBoardingStepData,
        statusStats: statusData,
        situationStats: situationData,
      },
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de l'obtention des statistiques des tuteurs",
      data: null,
    };
  }
};
