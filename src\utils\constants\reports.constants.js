//To be Entered by default
//Entered
//Confirmed

exports.crScReportsStatus = {
     toBeEntered: {
          name: "To be Entered",
          crScReportsStatusId: "to-be-entered",
     },
     entered: {
          name: "Entered",
          crScReportsStatusId: "entered",
     },
     confirmed: {
          name: "Confirmed",
          crScReportsStatusId: "confirmed",
     },
};

exports.crScReportsTypes = {
     crReport: {
          name: "CR",
          crScReportsTypeId: "cr-report-az-1997",
     },
     scReport: {
          name: "SC Report",
          scScReportsTypeId: "sc-report-az-1998",
     },
};
