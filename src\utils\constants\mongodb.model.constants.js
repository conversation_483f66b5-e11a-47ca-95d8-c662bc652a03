//module.exports array of strings with the names of the collections in the database
const modelName = {
     ESTABLISHMENTS: "establishments",
     EDUCATION_ANNUAIRE: "education-annuaire",
     USER_ROLES: "userroles",
     TUTOR_PREFERENCES: "tutorpreferences",
     PARENT_PREFERENCES: "parentpreferences",
     STUDENT_PREFERENCES: "studentpreferences",
     SCHOLAR_YEARS: "scholaryears",
     SCHOOL_ZONES: "schoolzones",
     SECTORS: "sectors",
     PUBLIC_HOLIDAYS: "publicholidays",
     AVAILABILITY: "availability",
     SESSIONS: "sessions",
     USER_ADMINISTRATION: "useradministration",
     BIGBLUEBUTTON_WEBHOOKS: "bigbluewebhooks",
};
function getModelNameAsObject() {
     //modelName covert to array of strings
     const modelNameArray = Object.keys(modelName);

     //return array of strings with the values of the modelName array
     return modelNameArray.map((key) => modelName[key]);
}

module.exports = { modelName, getModelNameAsObject };
