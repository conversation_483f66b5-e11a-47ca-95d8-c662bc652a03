const userAdministrationModel = require("../../models/User.Administration.Model.js");

const userModel = require("../../models/User.Models.js");

const userHelper = require("./User.Helper.js");

//import Establishment.Model.js
const establishmentModel = require("../../models/Establishment.Model.js");

//import Sector.Model.js
const sectorModel = require("../../models/Sectors.Model.js");

const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const userRoleStatusConstants = require("../../utils/constants/user.role.status.constants.js");

const appConstants = require("../../utils/constants/app.constants.js");

const tutorHelper = require("../tutor/tutor.helper.js");

const parentHelper = require("../parent/parent.helper.js");
const studentHelper = require("../students/student.helpers.js");
const sessionHelper = require("../sessions/sessions.helper.js");

const emailHelper = require("../../middleware/mailServiceSendGrid/EmailHelper.js");

const { v4: uuidv4 } = require("uuid");

const sendGrid = require("@sendgrid/mail");

require("dotenv").config();

const establishmentHelper = require("../establishments/establishments.helper.js");

const firebaseHelper = require("../firebase/firebase.helper.js");
const userRoleTranslated = require("../../utils/constants/user.roles.translated");
const GovernmentSectorsModel = require("../../models/GovernmentSectors.Model.js");
const SessionsModel = require("../../models/Sessions.Model.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const {
  getTutorsCountFromPreferences,
} = require("../tutor/tutor.pipelines.js");
const {
  getStudentsCountFromPreferences,
} = require("../students/student.pipelines.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const programConstants = require("../../utils/constants/program.constants.js");
const SectorsModel = require("../../models/Sectors.Model.js");
const onligneProgram = [
  HOME_CLASSES.programId,
  CLASSSES_HOME.programId,
  ZUPDEFOOT.programId,
];
const StudentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const crSessionsModel = require("../../models/Cr.Report.Model.js");
const crReportsModel = require("../../models/Cr.Report.Model.js");

// Importer les utilitaires de statistiques
const statsUtils = require("./stats.utils.js");

/**
 * Calcule les statistiques de séances pour un coordinateur selon différentes périodicités
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} payload.period - La périodicité ('perWeek', 'perMonth', 'perTrimester', 'perYear')
 * @param {string} [payload.timeslot] - La période spécifique au format approprié pour chaque périodicité
 * @param {string} [payload.program] - L'ID du programme pour filtrer les statistiques
 * @returns {Object} - Les statistiques calculées
 */
exports.getCoordinatorSessionsStats = async (payload) => {
  try {
    // Vérifier si l'utilisateur est un coordinateur
    const validationError = statsUtils.validateCoordinatorAccess(payload);
    if (validationError) return validationError;
    
    const { userId, period, timeslot, program } = payload;

    // Vérifier si la périodicité est valide
    if (!period) {
      return {
        status: false,
        message: "La périodicité doit être spécifiée.",
        data: null,
      };
    }

    // Récupérer les tuteurs sous la responsabilité du coordinateur
    const tutorIds = await statsUtils.getTutorIdsWithFilter(program, userId);

    if (!tutorIds.length) {
      return {
        status: true,
        message: "Statistiques de séances récupérées avec succès",
        data: [], // Retourner un tableau vide au lieu d'une erreur
      };
    }

    // Calculer les périodes de dates
    const periods = statsUtils.getPeriodDateRanges(period, timeslot);
    
    if (!periods.isValid) {
      return periods.error;
    }
    
    // Définir les périodes en fonction du paramètre
    if (period === "perWeek") {
      // Pipeline d'agrégation pour les stats hebdomadaires
      const pipeline = [
        // Étape 1: Filtrer les sessions pour les tuteurs concernés et la période spécifiée
        {
          $match: {
            "students.userId": { $in: tutorIds },
            status: { $ne: "session-0-abandoned" },
            "sessionDate.startDate": {
              $gte: periods.currentPeriod.start,
              $lte: periods.currentPeriod.end,
            },
          },
        },
        // Étape 2: Calculer le jour de la semaine et formater la date
        {
          $addFields: {
            dayOfWeek: { $dayOfWeek: "$sessionDate.startDate" }, // 1 = dimanche, 2 = lundi, etc.
            dayDate: {
              $dateToString: {
                format: "%d/%m/%Y",
                date: "$sessionDate.startDate",
              },
            },
          },
        },
        // Étape 3: Regrouper par jour
        {
          $group: {
            _id: {
              dayOfWeek: "$dayOfWeek",
              dayDate: "$dayDate",
            },
            seancesPlanifiees: { $sum: 1 },
          },
        },
        // Étape 4: Formater le résultat final
        {
          $project: {
            _id: 0,
            jour: {
              $concat: [
                {
                  $switch: {
                    branches: [
                      {
                        case: { $eq: ["$_id.dayOfWeek", 1] },
                        then: "Dimanche",
                      },
                      { case: { $eq: ["$_id.dayOfWeek", 2] }, then: "Lundi" },
                      { case: { $eq: ["$_id.dayOfWeek", 3] }, then: "Mardi" },
                      {
                        case: { $eq: ["$_id.dayOfWeek", 4] },
                        then: "Mercredi",
                      },
                      { case: { $eq: ["$_id.dayOfWeek", 5] }, then: "Jeudi" },
                      {
                        case: { $eq: ["$_id.dayOfWeek", 6] },
                        then: "Vendredi",
                      },
                      { case: { $eq: ["$_id.dayOfWeek", 7] }, then: "Samedi" },
                    ],
                    default: "Inconnu",
                  },
                },
                " ",
                "$_id.dayDate",
              ],
            },
            seancesPlanifiees: 1,
            seancesRealisees: { $literal: "-" },
            sortDate: { $dateFromString: { dateString: "$_id.dayDate", format: "%d/%m/%Y" } }
          },
        },
        // Étape 5: Trier par date
        {
          $sort: { sortDate: 1 },
        },
        // Étape 6: Supprimer le champ de tri temporaire
        {
          $project: {
            sortDate: 0
          }
        }
      ];

      const results = await SessionsModel.aggregate(pipeline);

      return {
        status: true,
        message: "Statistiques de séances récupérées avec succès",
        data: results,
      };
    } 
    else if (period === "perMonth") {
      // Pipeline d'agrégation pour les stats mensuelles par semaine
      const pipeline = [
        // Filtrer les sessions pour les tuteurs et la période
        {
          $match: {
            "students.userId": { $in: tutorIds },
            status: { $ne: "session-0-abandoned" },
            "sessionDate.startDate": {
              $gte: periods.currentPeriod.start,
              $lte: periods.currentPeriod.end,
            },
          },
        },
        // Calculer la semaine du mois
        {
          $addFields: {
            dayOfMonth: { $dayOfMonth: "$sessionDate.startDate" },
            weekOfMonth: {
              $switch: {
                branches: [
                  {
                    case: {
                      $lte: [{ $dayOfMonth: "$sessionDate.startDate" }, 7],
                    },
                    then: "S1",
                  },
                  {
                    case: {
                      $lte: [{ $dayOfMonth: "$sessionDate.startDate" }, 14],
                    },
                    then: "S2",
                  },
                  {
                    case: {
                      $lte: [{ $dayOfMonth: "$sessionDate.startDate" }, 21],
                    },
                    then: "S3",
                  },
                  {
                    case: {
                      $lte: [{ $dayOfMonth: "$sessionDate.startDate" }, 31],
                    },
                    then: "S4",
                  },
                ],
                default: "Inconnu",
              },
            },
          },
        },
        // Regrouper par semaine
        {
          $group: {
            _id: "$weekOfMonth",
            seancesPlanifiees: { $sum: 1 },
          },
        },
        // Formater le résultat
        {
          $project: {
            _id: 0,
            semaine: "$_id",
            seancesPlanifiees: 1,
            seancesRealisees: { $literal: "-" },
          },
        },
        // Trier par semaine
        {
          $sort: {
            semaine: 1,
          },
        },
      ];

      const results = await SessionsModel.aggregate(pipeline);

      return {
        status: true,
        message: "Statistiques de séances récupérées avec succès",
        data: results,
      };
    } 
    else if (period === "perYear") {
      // Pipeline d'agrégation pour les stats annuelles par mois
      const pipeline = [
        // Filtrer les sessions pour les tuteurs et l'année scolaire
        {
          $match: {
            "students.userId": { $in: tutorIds },
            status: { $ne: "session-0-abandoned" },
            "sessionDate.startDate": {
              $gte: periods.currentPeriod.start,
              $lte: periods.currentPeriod.end,
            },
          },
        },
        // Extraire le mois et l'année
        {
          $addFields: {
            month: { $month: "$sessionDate.startDate" },
            year: { $year: "$sessionDate.startDate" },
          },
        },
        // Regrouper par mois
        {
          $group: {
            _id: {
              month: "$month",
              year: "$year",
            },
            seancesPlanifiees: { $sum: 1 },
          },
        },
        // Formater le résultat
        {
          $project: {
            _id: 0,
            mois: {
              $switch: {
                branches: [
                  { case: { $eq: ["$_id.month", 1] }, then: "Janvier" },
                  { case: { $eq: ["$_id.month", 2] }, then: "Février" },
                  { case: { $eq: ["$_id.month", 3] }, then: "Mars" },
                  { case: { $eq: ["$_id.month", 4] }, then: "Avril" },
                  { case: { $eq: ["$_id.month", 5] }, then: "Mai" },
                  { case: { $eq: ["$_id.month", 6] }, then: "Juin" },
                  { case: { $eq: ["$_id.month", 7] }, then: "Juillet" },
                  { case: { $eq: ["$_id.month", 8] }, then: "Août" },
                  { case: { $eq: ["$_id.month", 9] }, then: "Septembre" },
                  { case: { $eq: ["$_id.month", 10] }, then: "Octobre" },
                  { case: { $eq: ["$_id.month", 11] }, then: "Novembre" },
                  { case: { $eq: ["$_id.month", 12] }, then: "Décembre" },
                ],
                default: "Inconnu",
              },
            },
            seancesPlanifiees: 1,
            seancesRealisees: { $literal: "-" },
            year: "$_id.year",
            monthNum: "$_id.month", // Pour le tri
          },
        },
        // Trier par ordre chronologique (année scolaire)
        {
          $addFields: {
            sortOrder: {
              $cond: {
                if: {
                  $and: [
                    { $gte: ["$monthNum", 9] }, // Septembre à décembre
                    { $eq: ["$year", periods.currentPeriod.start.getFullYear()] },
                  ],
                },
                then: "$monthNum",
                else: { $add: ["$monthNum", 12] }, // Janvier à août de l'année suivante
              },
            },
          },
        },
        {
          $sort: {
            sortOrder: 1,
          },
        },
        // Supprimer les champs temporaires
        {
          $project: {
            monthNum: 0,
            sortOrder: 0,
            year: 0,
          },
        },
      ];

      const results = await SessionsModel.aggregate(pipeline);

      return {
        status: true,
        message: "Statistiques de séances récupérées avec succès",
        data: results,
      };
    } 
    else if (period === "perTrimester") {
      return {
        status: false,
        message: "La période 'perTrimester' n'est pas encore implémentée.",
        data: null,
      };
    }

    return {
      status: false,
      message: "Période non reconnue.",
      data: null,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de la récupération des statistiques de séances",
      data: error,
    };
  }
};

/**
 * Calcule les statistiques de présence et d'assiduité pour un coordinateur
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} payload.period - La période (perWeek, perMonth, perTrimester)
 * @param {string} [payload.timeslot] - La période spécifique au format approprié pour chaque périodicité
 * @param {string} [payload.program] - L'ID du programme pour filtrer les statistiques
 * @returns {Object} - Les statistiques calculées
 */
exports.getCoordinatorAttendanceStats = async (payload) => {
  try {
    // Vérifier si l'utilisateur est un coordinateur
    const validationError = statsUtils.validateCoordinatorAccess(payload);
    if (validationError) return validationError;
    
    const { userId, period, program, timeslot } = payload;

    // Récupérer les IDs des tuteurs filtrés par programme
    const tutorIds = await statsUtils.getTutorIdsWithFilter(program, userId);
    
    if (!tutorIds.length) {
      // Calculer les périodes de dates pour avoir des valeurs cohérentes dans la réponse
      const periods = statsUtils.getPeriodDateRanges(period, timeslot);
      
      if (!periods.isValid) {
        return periods.error;
      }
      
      // Retourner un objet avec des statistiques à zéro
      return {
        status: true,
        message: "Statistiques de présence récupérées avec succès",
        data: {
          period: periods.label,
          currentPeriod: {
            start: periods.currentPeriod.start,
            end: periods.currentPeriod.end,
          },
          previousPeriod: {
            start: periods.previousPeriod.start,
            end: periods.previousPeriod.end,
          },
          plannedSessions: {
            count: 0,
            evolution: 0,
          },
          completionRate: {
            rate: "-",
            evolution: "-",
            details: {
              totalPastSessions: 0,
              completedPlannedSessions: 0,
            },
          },
          absenteeismRate: {
            rate: "-",
            evolution: "-",
          },
          canceledSessions: {
            count: 0,
            evolution: 0,
          },
        },
      };
    }

    // Calculer les périodes de dates
    const periods = statsUtils.getPeriodDateRanges(period, timeslot);
    
    if (!periods.isValid) {
      return periods.error;
    }

    // Exécuter plusieurs opérations en parallèle pour optimiser le temps de réponse
    const [
      // Période courante - statistiques
      currentStats,

      // Période précédente - statistiques
      previousStats,
    ] = await Promise.all([
      // 1. Période courante
      statsUtils.getSessionsStatistics(
        tutorIds, 
        periods.currentPeriod.start, 
        periods.currentPeriod.end
      ),

      // 2. Période précédente
      statsUtils.getSessionsStatistics(
        tutorIds, 
        periods.previousPeriod.start, 
        periods.previousPeriod.end
      )
    ]);

    // Extraire les résultats avec valeurs par défaut si non trouvées
    // Période courante
    const currentPlannedSessions = currentStats.plannedSessions[0]?.count || 0;
    const currentCanceledSessions = currentStats.canceledSessions[0]?.count || 0;
    const totalCurrentPastSessions = currentStats.pastSessions[0]?.count || 0;
    const completedCurrentPlannedSessions = currentStats.completedPastSessions[0]?.count || 0;
    const currentSessionIds = currentStats.sessionIds.map(
      (session) => session.sessionId
    );

    // Période précédente
    const previousPlannedSessions = previousStats.plannedSessions[0]?.count || 0;
    const previousCanceledSessions = previousStats.canceledSessions[0]?.count || 0;
    const totalPreviousPastSessions = previousStats.pastSessions[0]?.count || 0;
    const completedPreviousPlannedSessions = previousStats.completedPastSessions[0]?.count || 0;
    const previousSessionIds = previousStats.sessionIds.map(
      (session) => session.sessionId
    );

    // Récupérer les données d'absentéisme en une seule requête agrégée
    let currentAbsenteeismSessions = 0;
    let previousAbsenteeismSessions = 0;

    if (currentSessionIds.length > 0 || previousSessionIds.length > 0) {
      const absenteeismStats = await crSessionsModel.aggregate([
        {
          $match: {
            $or: [
              {
                sessionId: { $in: currentSessionIds },
                "students.0.absence": true,
              },
              {
                sessionId: { $in: previousSessionIds },
                "students.0.absence": true,
              },
            ],
          },
        },
        {
          $project: {
            sessionId: 1,
            "students.absence": 1,
            _id: 0,
          },
        },
        {
          $facet: {
            current: [
              { $match: { sessionId: { $in: currentSessionIds } } },
              { $count: "count" },
            ],
            previous: [
              { $match: { sessionId: { $in: previousSessionIds } } },
              { $count: "count" },
            ],
          },
        },
      ]);

      currentAbsenteeismSessions = absenteeismStats[0].current[0]?.count || 0;
      previousAbsenteeismSessions = absenteeismStats[0].previous[0]?.count || 0;
    }
    
    // Pour l'instant, ces valeurs sont désactivées (remplacées par "-")
    const currentCompletionRate = "-";
    const completionRateEvolution = "-";
    const currentAbsenteeismRate = "-";
    const absenteeismRateEvolution = "-";

    // Calculer l'évolution des séances planifiées
    const plannedSessionsEvolution = statsUtils.calculateEvolutionPercentage(
      currentPlannedSessions,
      previousPlannedSessions
    );

    // Calculer l'évolution des séances annulées
    const canceledSessionsEvolution = statsUtils.calculateEvolutionPercentage(
      currentCanceledSessions,
      previousCanceledSessions
    );

    // Construire l'objet de résultat
    const result = {
      period: periods.label,
      currentPeriod: {
        start: periods.currentPeriod.start,
        end: periods.currentPeriod.end,
      },
      previousPeriod: {
        start: periods.previousPeriod.start,
        end: periods.previousPeriod.end,
      },
      plannedSessions: {
        count: currentPlannedSessions,
        evolution: plannedSessionsEvolution,
      },
      completionRate: {
        rate: currentCompletionRate,
        evolution: completionRateEvolution,
        details: {
          totalPastSessions: totalCurrentPastSessions,
          completedPlannedSessions: completedCurrentPlannedSessions,
        },
      },
      absenteeismRate: {
        rate: currentAbsenteeismRate,
        evolution: absenteeismRateEvolution,
      },
      canceledSessions: {
        count: currentCanceledSessions,
        evolution: canceledSessionsEvolution,
      },
    };

    return {
      status: true,
      message: "Statistiques de présence récupérées avec succès",
      data: result,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message:
        "Erreur lors de la récupération des statistiques de présence et d'assiduité",
      data: error,
    };
  }
};

/**
 * Calcule les statistiques de performance pour un coordinateur
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} payload.period - La période ('perWeek', 'perMonth', 'perTrimester', 'perYear')
 * @param {string} [payload.timeslot] - La période spécifique au format approprié pour chaque périodicité
 * @param {string} [payload.program] - L'ID du programme pour filtrer les statistiques
 * @returns {Object} - Les statistiques calculées
 */
exports.getCoordinatorPerformanceStats = async (payload) => {
  try {
    // Vérifier si l'utilisateur est un coordinateur
    const validationError = statsUtils.validateCoordinatorAccess(payload);
    if (validationError) return validationError;
    
    const { userId, period, program, timeslot } = payload;

    // Récupérer les périodes
    const periods = statsUtils.getPeriodDateRanges(period, timeslot);
    if (!periods.isValid) {
      return periods.error;
    }

    // Récupérer les détails du coordinateur et ses départements
    const userAdminDetails = await UserAdministrationModel.findOne({ userId }).lean();
    if (!userAdminDetails || !userAdminDetails.administrationPreferences) {
      return {
        status: false,
        message: "Détails administrateur non trouvés ou incomplets",
        data: null,
      };
    }

    const pref = userAdminDetails.administrationPreferences;

    // Vérifier si le coordinateur a des préférences de programme
    if (!pref.program || pref.program.length === 0) {
      // Retourner des statistiques à zéro au lieu d'une erreur
      return {
        status: true,
        message: "Statistiques de performance récupérées avec succès",
        data: {
          period: periods.label,
          currentPeriod: {
            start: periods.currentPeriod.start,
            end: periods.currentPeriod.end,
          },
          previousPeriod: {
            start: periods.previousPeriod.start,
            end: periods.previousPeriod.end,
          },
          satisfactionRate: {
            rate: 0,
            evolution: 0,
            total: 0,
          },
          reportsCompletion: {
            rate: 0,
            evolution: 0,
            completedReports: 0,
            totalSessions: 0,
          },
        },
      };
    }

    // Récupérer les départements du coordinateur
    const departmentIds =
      pref.department && pref.department.length > 0
        ? pref.department.map((dep) => dep.departmentId)
        : [];

    if (departmentIds.length === 0) {
      // Retourner des statistiques à zéro au lieu d'une erreur
      return {
        status: true,
        message: "Statistiques de performance récupérées avec succès",
        data: {
          period: periods.label,
          currentPeriod: {
            start: periods.currentPeriod.start,
            end: periods.currentPeriod.end,
          },
          previousPeriod: {
            start: periods.previousPeriod.start,
            end: periods.previousPeriod.end,
          },
          satisfactionRate: {
            rate: 0,
            evolution: 0,
            total: 0,
          },
          reportsCompletion: {
            rate: 0,
            evolution: 0,
            completedReports: 0,
            totalSessions: 0,
          },
        },
      };
    }

    // Filtre pour les étudiants du coordinateur
    const studentFilter = { 
      "program.programId": { $in: pref.program.map(p => p.programId) },
      "assignment.department.departmentId": { $in: departmentIds }
    };

    // Récupérer les IDs des étudiants filtrés
    const studentIds = await StudentPreferencesModel.find(studentFilter)
      .select("userId")
      .lean()
      .then(students => students.map(student => student.userId));

    if (!studentIds.length) {
      // Retourner des statistiques à zéro au lieu d'une erreur
      return {
        status: true,
        message: "Statistiques de performance récupérées avec succès",
        data: {
          period: periods.label,
          currentPeriod: {
            start: periods.currentPeriod.start,
            end: periods.currentPeriod.end,
          },
          previousPeriod: {
            start: periods.previousPeriod.start,
            end: periods.previousPeriod.end,
          },
          satisfactionRate: {
            rate: 0,
            evolution: 0,
            total: 0,
          },
          reportsCompletion: {
            rate: 0,
            evolution: 0,
            completedReports: 0,
            totalSessions: 0,
          },
        },
      };
    }

    // Récupérer les IDs de sessions pour chaque période
    const [currentSessionIds, previousSessionIds] = await Promise.all([
      statsUtils.getSessionsIdsForPeriod(
        studentIds, 
        periods.currentPeriod.start, 
        periods.currentPeriod.end
      ),
      statsUtils.getSessionsIdsForPeriod(
        studentIds, 
        periods.previousPeriod.start, 
        periods.previousPeriod.end
      )
    ]);

    // Compter les sessions pour chaque période
    const totalCurrentSessions = currentSessionIds.length;
    const totalPreviousSessions = previousSessionIds.length;

    // Si aucune session n'a été trouvée, retourner des statistiques vides
    if (totalCurrentSessions === 0 && totalPreviousSessions === 0) {
      return {
        status: true,
        message: "Aucune session trouvée pour cette période",
        data: {
          period: periods.label,
          currentPeriod: {
            start: periods.currentPeriod.start,
            end: periods.currentPeriod.end,
          },
          previousPeriod: {
            start: periods.previousPeriod.start,
            end: periods.previousPeriod.end,
          },
          satisfactionRate: {
            rate: 0,
            evolution: 0,
            total: 0,
          },
          reportsCompletion: {
            rate: 0,
            evolution: 0,
            completedReports: 0,
            totalSessions: 0,
          },
        },
      };
    }

    // Traiter les rapports pour les deux périodes
    const [currentReportsStats, previousReportsStats] = await Promise.all([
      statsUtils.processReportsInBatches(currentSessionIds, ["confirmed", "entered"]),
      statsUtils.processReportsInBatches(previousSessionIds, ["confirmed", "entered"])
    ]);

    // Extraire les résultats avec des valeurs par défaut
    const completedCurrentReports = currentReportsStats.completedCount || 0;
    const currentGradesTotal = currentReportsStats.gradeTotal || 0;
    const currentReportsWithGradesCount = currentReportsStats.gradeCount || 0;

    const completedPreviousReports = previousReportsStats.completedCount || 0;
    const previousGradesTotal = previousReportsStats.gradeTotal || 0;
    const previousReportsWithGradesCount = previousReportsStats.gradeCount || 0;

    // Calculer les taux et évolutions
    // 1. Taux de satisfaction
    let currentSatisfactionRate = 0;
    if (currentReportsWithGradesCount > 0) {
      currentSatisfactionRate = parseFloat(
        (
          (currentGradesTotal / (currentReportsWithGradesCount * 5)) *
          100
        ).toFixed(1)
      );
    }

    let previousSatisfactionRate = 0;
    if (previousReportsWithGradesCount > 0) {
      previousSatisfactionRate = parseFloat(
        (
          (previousGradesTotal / (previousReportsWithGradesCount * 5)) *
          100
        ).toFixed(1)
      );
    }

    // Évolution du taux de satisfaction
    let satisfactionEvolution = 0;
    if (previousSatisfactionRate > 0) {
      satisfactionEvolution = parseFloat(
        (
          ((currentSatisfactionRate - previousSatisfactionRate) /
            previousSatisfactionRate) *
          100
        ).toFixed(1)
      );
      satisfactionEvolution =
        satisfactionEvolution < 0 ? 0 : satisfactionEvolution;
    } else if (currentSatisfactionRate > 0) {
      satisfactionEvolution = 100;
    }

    // 2. Taux de complétion des rapports
    let currentReportsCompletion = 0;
    if (totalCurrentSessions > 0) {
      currentReportsCompletion = Math.round(
        (completedCurrentReports / totalCurrentSessions) * 100
      );
    }

    let previousReportsCompletion = 0;
    if (totalPreviousSessions > 0) {
      previousReportsCompletion = Math.round(
        (completedPreviousReports / totalPreviousSessions) * 100
      );
    }

    // Évolution du taux de complétion
    const objectivesEvolution = statsUtils.calculateEvolutionPercentage(
      currentReportsCompletion,
      previousReportsCompletion
    );

    // Construire l'objet de résultat optimisé
    const result = {
      period: periods.label,
      currentPeriod: {
        start: periods.currentPeriod.start,
        end: periods.currentPeriod.end,
      },
      previousPeriod: {
        start: periods.previousPeriod.start,
        end: periods.previousPeriod.end,
      },
      satisfactionRate: {
        rate: currentSatisfactionRate,
        evolution: satisfactionEvolution,
        total: currentReportsWithGradesCount,
      },
      reportsCompletion: {
        rate: currentReportsCompletion,
        evolution: objectivesEvolution,
        completedReports: completedCurrentReports,
        totalSessions: totalCurrentSessions,
      },
    };

    return {
      status: true,
      message: "Statistiques de performance récupérées avec succès",
      data: result,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de la récupération des statistiques de performance",
      data: error,
    };
  }
};

/**
 * Calcule l'évolution temporelle des statistiques sur les 6 derniers mois pour un coordinateur
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} [payload.program] - L'ID du programme pour filtrer les statistiques
 * @param {string} [payload.period] - Type de périodicité ('perWeek', 'perMonth', 'perTrimester', 'perYear')
 * @param {string} [payload.timeslot] - Période spécifique au format approprié pour chaque périodicité
 * @returns {Object} - Les statistiques calculées
 */
exports.getCoordinatorTimeEvolutionStats = async (payload) => {
  try {    
    // Vérifier si l'utilisateur est un coordinateur
    const validationError = statsUtils.validateCoordinatorAccess(payload);
    if (validationError) return validationError;
    
    const { userId, program } = payload;
    
    // Récupérer le timeslot quelle que soit la casse
    const timeslot = payload.timeslot;

    // Récupérer les détails du coordinateur et ses départements
    const userAdminDetails = await UserAdministrationModel.findOne({ userId }).lean();
    if (!userAdminDetails || !userAdminDetails.administrationPreferences) {
      return {
        status: false,
        message: "Détails administrateur non trouvés ou incomplets",
        data: null,
      };
    }

    const pref = userAdminDetails.administrationPreferences;

    // Vérifier si le coordinateur a des préférences de programme
    if (!pref.program || pref.program.length === 0) {
      return {
        status: false,
        message: "Aucun programme assigné au coordinateur",
        data: null,
      };
    }

    // Récupérer les départements du coordinateur
    const departmentIds =
      pref.department && pref.department.length > 0
        ? pref.department.map((dep) => dep.departmentId)
        : [];

    if (departmentIds.length === 0) {
      return {
        status: false,
        message: "Aucun département assigné au coordinateur",
        data: null,
      };
    }

    // Définir les plages de dates pour l'évolution temporelle
    const today = new Date();
    let startDate, endDate;
    let customToday = new Date(today);
    let customMonthsCount = 6; // Nombre de mois à analyser par défaut

    if (timeslot) {
      // Format attendu: "MM/YYYY,MM/YYYY" pour une plage personnalisée
      const parts = timeslot.split(",");
      
      if (parts.length !== 2) {
        return {
          status: false,
          message:
            "Format de période invalide. Le format attendu est 'MM/YYYY,MM/YYYY' (ex. '10/2024,03/2025').",
          data: null,
        };
      }
      
      const startPeriod = parts[0].trim();
      const endPeriod = parts[1].trim();

      if (!startPeriod || !endPeriod) {
        return {
          status: false,
          message:
            "Format de période invalide. Le format attendu est 'MM/YYYY,MM/YYYY' (ex. '10/2024,03/2025').",
          data: null,
        };
      }

      const [startMonth, startYear] = startPeriod
        .split("/")
        .map((n) => parseInt(n, 10));
      const [endMonth, endYear] = endPeriod
        .split("/")
        .map((n) => parseInt(n, 10));

      if (
        isNaN(startMonth) ||
        isNaN(startYear) ||
        isNaN(endMonth) ||
        isNaN(endYear) ||
        startMonth < 1 ||
        startMonth > 12 ||
        endMonth < 1 ||
        endMonth > 12
      ) {
        return {
          status: false,
          message:
            "Format de période invalide. Le mois doit être entre 1 et 12 et l'année doit être valide.",
          data: null,
        };
      }

      // Définir la date de début (1er du mois spécifié)
      startDate = new Date(startYear, startMonth - 1, 1);

      // Définir la date de fin (dernier jour du mois spécifié)
      endDate = new Date(endYear, endMonth, 0);
      endDate.setHours(23, 59, 59, 999);

      // Utiliser la date de fin comme référence pour "aujourd'hui"
      customToday = new Date(endDate);
      
      // Calculer le nombre de mois entre les deux dates
      customMonthsCount = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;
      
      // Limiter à 6 mois maximum pour la visualisation
      customMonthsCount = Math.min(customMonthsCount, 6);
    } else {
      // Par défaut, utiliser le mois courant et les 5 mois précédents
      endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      endDate.setHours(23, 59, 59, 999);

      startDate = new Date(today.getFullYear(), today.getMonth() - 5, 1);
    }

    // Filtres de base
    const programFilter = program ? { "program.programId": program } : {};
    const studentFilter = { 
      "program.programId": { $in: pref.program.map(p => p.programId) },
      "assignment.department.departmentId": { $in: departmentIds }
    };

    // Générer la structure des mois pour l'analyse
    // Tableau des noms des mois en français
    const monthNames = [
      "Janvier", "Février", "Mars", "Avril", "Mai", "Juin",
      "Juillet", "Août", "Septembre", "Octobre", "Novembre", "Décembre",
    ];

    const months = [];
    for (let i = 0; i < customMonthsCount; i++) {
      const monthIndex = customToday.getMonth() - i;
      const yearOffset = Math.floor(monthIndex / 12);
      const normalizedMonthIndex = ((monthIndex % 12) + 12) % 12;

      const year = customToday.getFullYear() + yearOffset;
      const monthStartDate = new Date(year, normalizedMonthIndex, 1);
      const monthEndDate = new Date(year, normalizedMonthIndex + 1, 0);
      monthEndDate.setHours(23, 59, 59, 999);

      months.push({
        name: monthNames[normalizedMonthIndex],
        start: monthStartDate,
        end: monthEndDate,
        year,
        month: normalizedMonthIndex,
        monthNumber: normalizedMonthIndex + 1, // Pour correspondre au $month de MongoDB (1-12)
      });
    }

    // Récupérer tous les tuteurs IDs et dates de création en une seule requête
    const [
      tutorAggregationResult, 
      studentAggregationResult,
      tutorIds,
      studentIds
    ] = await Promise.all([
      // 1. Agrégation des tuteurs par mois
      TutorPreferencesModel.aggregate([
        { 
          $match: { 
            ...programFilter, 
            status: "active", 
            createdAt: { $gte: startDate, $lte: endDate } 
          } 
        },
        { 
          $project: { 
            monthNumber: { $month: "$createdAt" }, 
            year: { $year: "$createdAt" }
          } 
        },
        { 
          $group: { 
            _id: { month: "$monthNumber", year: "$year" }, 
            count: { $sum: 1 } 
          } 
        }
      ]),

      // 2. Agrégation des étudiants par mois
      StudentPreferencesModel.aggregate([
        { 
          $match: { 
            ...studentFilter, 
            createdAt: { $gte: startDate, $lte: endDate } 
          } 
        },
        { 
          $project: { 
            monthNumber: { $month: "$createdAt" }, 
            year: { $year: "$createdAt" }
          } 
        },
        { 
          $group: { 
            _id: { month: "$monthNumber", year: "$year" }, 
            count: { $sum: 1 } 
          } 
        }
      ]),

      // 3. Récupérer tous les IDs de tuteurs (pour les requêtes de sessions)
      TutorPreferencesModel.find({
        ...programFilter,
        status: "active"
      })
        .select("userId -_id")
        .lean()
        .then(tutors => tutors.map(tutor => tutor.userId)),

      // 4. Récupérer tous les IDs des étudiants (pour les requêtes de sessions)
      StudentPreferencesModel.find({
        ...studentFilter,
      })
        .select("userId -_id")
        .lean()
        .then(students => students.map(student => student.userId))
    ]);

    // Récupérer et agréger les sessions par mois en une seule requête
    const currentDateTime = new Date();
    
    const sessionsAggregation = await SessionsModel.aggregate([
      {
        // Filtrer les sessions par les étudiants du coordinateur (au lieu des tuteurs)
        $match: {
          "students.userId": { $in: studentIds }, // On utilise les IDs des étudiants filtrés
          "sessionDate.startDate": { $gte: startDate, $lte: endDate }
        }
      },
      {
        // Projeter les champs nécessaires et calculer les indicateurs pour chaque session
        $project: {
          sessionId: 1,
          status: 1,
          monthNumber: { $month: "$sessionDate.startDate" }, // Extraire le mois de la date de début
          year: { $year: "$sessionDate.startDate" }, // Extraire l'année de la date de début
          isPast: { $lt: ["$sessionDate.endDate", currentDateTime] }, // Vérifier si la séance est passée (date de fin < date actuelle)
          isCompleted: { $eq: ["$status", "session-0-to-be-scheduled"] } // Vérifier si la séance est considérée comme réalisée
        }
      },
      {
        // Regrouper les sessions par mois et année, et calculer les statistiques
        $group: {
          _id: { month: "$monthNumber", year: "$year" },
          sessionIds: { $push: "$sessionId" }, // Collecter les IDs de sessions pour traitement ultérieur
          totalSessions: { $sum: 1 }, // Nombre total de séances
          pastSessions: { $sum: { $cond: ["$isPast", 1, 0] } }, // Nombre de séances passées
          completedSessions: { $sum: { $cond: [{ $and: ["$isPast", "$isCompleted"] }, 1, 0] } } // Nombre de séances réalisées (passées et marquées comme terminées)
        }
      }
    ]);

    // Traiter les rapports par lots pour chaque mois
    const monthlyReports = await Promise.all(
      sessionsAggregation.map(async (monthData) => {
        if (!monthData.sessionIds || monthData.sessionIds.length === 0) {
          return {
            ...monthData,
            satisfaction: 0,
            reportCount: 0
          };
        }

        // Utiliser processReportsInBatches pour traiter les rapports par lots
        const reportsStats = await statsUtils.processReportsInBatches(
          monthData.sessionIds, 
          ["confirmed", "entered"]
        );

        const satisfactionRate = reportsStats.gradeCount > 0
          ? parseFloat(((reportsStats.gradeTotal / (reportsStats.gradeCount * 5)) * 100).toFixed(1))
          : 0;

        return {
          ...monthData,
          satisfaction: satisfactionRate,
          reportCount: reportsStats.gradeCount
        };
      })
    );

    // Préparer les résultats au format attendu
    const results = {
      newRegistrations: [],
      completedSessions: [], // Tableau qui contiendra les données de séances réalisées par mois
      satisfactionRate: []
    };

    // Assembler les données par mois
    months.forEach(month => {
      // Trouver les données de tuteurs pour ce mois
      const tutorData = tutorAggregationResult.find(data => 
        data._id.month === month.monthNumber && data._id.year === month.year
      );
      const newTutors = tutorData ? tutorData.count : 0;

      // Trouver les données d'étudiants pour ce mois
      const studentData = studentAggregationResult.find(data => 
        data._id.month === month.monthNumber && data._id.year === month.year
      );
      const newStudents = studentData ? studentData.count : 0;

      // Trouver les données de sessions et rapports pour ce mois
      const sessionData = monthlyReports.find(data => 
        data._id.month === month.monthNumber && data._id.year === month.year
      );
      
      // Récupérer le nombre de séances réalisées pour ce mois
      // Une séance est considérée comme réalisée si:
      // 1. Elle est passée (date de fin < date actuelle)
      // 2. Son statut est "session-0-to-be-scheduled" (indiquant qu'elle a été effectuée)
      const completedSessions = sessionData ? sessionData.completedSessions : 0;
      
      // Récupérer le nombre total de séances pour ce mois (pour contexte/comparaison)
      const totalSessions = sessionData ? sessionData.totalSessions : 0;
      
      const satisfactionRate = sessionData ? sessionData.satisfaction : 0;
      const reportCount = sessionData ? sessionData.reportCount : 0;

      // Ajouter aux résultats
      results.newRegistrations.push({
        month: month.name,
        year: month.year,
        value: newTutors + newStudents,
        tutors: newTutors,
        students: newStudents
      });

      // Ajouter les données de séances réalisées pour ce mois
      results.completedSessions.push({
        month: month.name, // Nom du mois en français
        year: month.year, // Année du mois
        value: completedSessions, // Nombre de séances réalisées ce mois
        totalSessions: totalSessions // Nombre total de séances ce mois (pour contexte)
      });

      results.satisfactionRate.push({
        month: month.name,
        year: month.year,
        value: satisfactionRate,
        totalReports: reportCount
      });
    });

    // Inverser les tableaux pour avoir l'ordre chronologique (du plus ancien au plus récent)
    results.newRegistrations.reverse();
    results.completedSessions.reverse(); // Inverser le tableau des séances réalisées pour l'ordre chronologique
    results.satisfactionRate.reverse();

    return {
      status: true,
      message: "Statistiques d'évolution temporelle récupérées avec succès",
      data: results, // Retourne l'objet results contenant le tableau completedSessions avec les statistiques de séances réalisées
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message:
        "Erreur lors de la récupération des statistiques d'évolution temporelle",
      data: error,
    };
  }
};

/**
 * Calcule les statistiques de tutorat pour un coordinateur
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} [payload.program] - L'ID du programme pour filtrer les statistiques
 * @returns {Object} - Les statistiques calculées
 */
exports.getCoordinatorTutoringStats = async (payload) => {
  try {
    // Vérifier si l'utilisateur est un coordinateur
    const validationError = statsUtils.validateCoordinatorAccess(payload);
    if (validationError) return validationError;
    
    const { userId, program } = payload;

    // Calcul des périodes pour l'évolution (non plus utilisé pour le comptage des nouveaux utilisateurs)
    const currentDate = new Date();

    // Période courante : 31 derniers jours (conservé pour des informations contextuelles dans la réponse)
    const currentPeriodEnd = new Date(currentDate);
    currentPeriodEnd.setHours(23, 59, 59, 999);
    const currentPeriodStart = new Date(currentDate);
    currentPeriodStart.setDate(currentDate.getDate() - 31);
    currentPeriodStart.setHours(0, 0, 0, 0);

    // Période précédente : 31 jours avant la période courante (conservé pour des informations contextuelles)
    const previousPeriodEnd = new Date(currentPeriodStart);
    previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
    previousPeriodEnd.setHours(23, 59, 59, 999);
    const previousPeriodStart = new Date(previousPeriodEnd);
    previousPeriodStart.setDate(previousPeriodEnd.getDate() - 31);
    previousPeriodStart.setHours(0, 0, 0, 0);

    // Obtenir les informations du coordinateur
    const userAdminDetails = await UserAdministrationModel.findOne({
      userId,
    }).lean();
    if (!userAdminDetails || !userAdminDetails.administrationPreferences) {
      return {
        status: false,
        message: "Détails administrateur non trouvés ou incomplets",
        data: null,
      };
    }

    const pref = userAdminDetails.administrationPreferences;

    // Vérifier si le coordinateur a des préférences de programme
    if (!pref.program || pref.program.length === 0) {
      // Au lieu de retourner une erreur, retourner des statistiques à zéro
      return {
        status: true,
        message: "Statistiques de tutorat récupérées avec succès",
        data: {
          tutors: {
            total: 0,
            new: 0,
            percentage: 0,
            available: 0,
          },
          students: {
            total: 0,
            new: 0,
            percentage: 0,
            available: 0,
          },
          newRegistrations: {
            total: 0,
            percentage: 0,
          },
          activePairs: 0,
          periodDetails: {
            currentPeriod: {
              startDate: currentPeriodStart,
              endDate: currentPeriodEnd,
            },
            previousPeriod: {
              startDate: previousPeriodStart,
              endDate: previousPeriodEnd,
            },
          },
        },
      };
    }

    // Récupérer tous les départements du coordinateur
    const departmentIds =
      pref.department && pref.department.length > 0
        ? pref.department.map(dep => dep.departmentId)
        : [];

    if (departmentIds.length === 0) {
      // Au lieu de retourner une erreur, retourner des statistiques à zéro
      return {
        status: true,
        message: "Statistiques de tutorat récupérées avec succès",
        data: {
          tutors: {
            total: 0,
            new: 0,
            percentage: 0,
            available: 0,
          },
          students: {
            total: 0,
            new: 0,
            percentage: 0,
            available: 0,
          },
          newRegistrations: {
            total: 0,
            percentage: 0,
          },
          activePairs: 0,
          periodDetails: {
            currentPeriod: {
              startDate: currentPeriodStart,
              endDate: currentPeriodEnd,
            },
            previousPeriod: {
              startDate: previousPeriodStart,
              endDate: previousPeriodEnd,
            },
          },
        },
      };
    }

    // Filtres de base
    const programFilter = program ? { "program.programId": program } : {};
    const studentFilter = { 
      "program.programId": { $in: pref.program.map(p => p.programId) },
      "assignment.department.departmentId": { $in: departmentIds }
    };

    // Exécuter plusieurs requêtes en parallèle pour optimiser le temps de réponse
    const [
      // 1. Statistiques des tuteurs
      tutorStats,

      // 2. Nouveaux tuteurs selon leur situation
      newTutorsCount,

      // 3. Tuteurs qui ne sont plus nouveaux (situation != 'new')
      previousTutorsCount,

      // 4. Statistiques des étudiants actifs (total uniquement)
      studentStats,
      
      // 4bis. Statistiques des étudiants disponibles (sans tuteur)
      studentsAvailableStats,

      // 5. Nouveaux étudiants selon leur situation
      newStudentsCount,

      // 6. Étudiants qui ne sont plus nouveaux (situation != 'new')
      previousStudentsCount,

      // 7. Récupérer IDs des tuteurs pour d'autres calculs
      tutorIds,

      // 8. Récupérer IDs des étudiants filtrés pour d'autres calculs
      filteredStudentIds,
    ] = await Promise.all([
      // 1. Statistiques des tuteurs (total et disponibles)
      TutorPreferencesModel.aggregate([
        {
          $match: {
            ...programFilter,
            status: "active",
          },
        },
        {
          $project: {
            userId: 1,
            numberOfStudentsToSupport: 1,
            matchedStudentsCount: {
              $size: { $ifNull: ["$matchedStudents", []] },
            },
          },
        },
        {
          $project: {
            userId: 1,
            isAvailable: {
              $lt: ["$matchedStudentsCount", "$numberOfStudentsToSupport"],
            },
          },
        },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
            available: { $sum: { $cond: ["$isAvailable", 1, 0] } },
          },
        },
      ]),

      // 2. Nouveaux tuteurs selon leur situation (et non plus la date de création)
      TutorPreferencesModel.countDocuments({
        ...programFilter,
        situation: "new", // Utiliser l'attribut situation au lieu de createdAt
      }),

      // 3. Tuteurs qui ne sont plus nouveaux (pour calculer le pourcentage d'évolution)
      TutorPreferencesModel.countDocuments({
        ...programFilter,
        situation: { $ne: "new" }, // Tous les tuteurs qui ne sont pas nouveaux
      }),

      // 4. Statistiques des étudiants (total et disponibles)
      StudentPreferencesModel.aggregate([
        {
          $match: {
            ...studentFilter,
            "assignment.department.departmentId": { $in: departmentIds },
            userStatus: "active",
          },
        },
        {
          $group: {
            _id: null,
            total: { $sum: 1 },
          },
        },
      ]),

      // 4bis. Statistiques des étudiants disponibles (sans tuteur - sans filtre de statut)
      StudentPreferencesModel.aggregate([
        {
          $match: {
            ...studentFilter,
            "assignment.department.departmentId": { $in: departmentIds },
            // Pas de filtre userStatus ici
          },
        },
        {
          $project: {
            userId: 1,
            matchedTutorsCount: { $size: { $ifNull: ["$matchedTutors", []] } },
          },
        },
        {
          $match: {
            matchedTutorsCount: 0 // On filtre directement les étudiants sans tuteur
          },
        },
        {
          $group: {
            _id: null,
            available: { $sum: 1 },
          },
        },
      ]),

      // 5. Nouveaux étudiants selon leur situation (et non plus la date de création)
      StudentPreferencesModel.countDocuments({
        ...studentFilter,
        "assignment.department.departmentId": { $in: departmentIds },
        situation: "new", // Utiliser l'attribut situation au lieu de createdAt
      }),

      // 6. Étudiants qui ne sont plus nouveaux (pour calculer le pourcentage d'évolution)
      StudentPreferencesModel.countDocuments({
        ...studentFilter,
        "assignment.department.departmentId": { $in: departmentIds },
        situation: { $ne: "new" }, // Tous les étudiants qui ne sont pas nouveaux
      }),

      // 7. Récupérer IDs des tuteurs pour d'autres calculs
      TutorPreferencesModel.find(programFilter)
        .select("userId")
        .lean()
        .then((docs) => docs.map((doc) => doc.userId)),

      // 8. Récupérer IDs des étudiants filtrés pour d'autres calculs
      StudentPreferencesModel.find({
        ...studentFilter,
        "assignment.department.departmentId": { $in: departmentIds },
      })
        .select("userId")
        .lean()
        .then((docs) => docs.map((doc) => doc.userId)),
    ]);

    // Extraire les valeurs des résultats agrégés
    const activeTutors = tutorStats.length > 0 ? tutorStats[0].total : 0;
    const availableTutors = tutorStats.length > 0 ? tutorStats[0].available : 0;
    const totalStudents = studentStats.length > 0 ? studentStats[0].total : 0;
    const availableStudents = studentsAvailableStats.length > 0 ? studentsAvailableStats[0].available : 0;

    // Récupérer le nombre de binômes actifs optimisé
    let activePairs = 0;

    if (filteredStudentIds.length > 0) {
      // Compter les étudiants actifs qui ont des tuteurs assignés
      const activePairsResult = await StudentPreferencesModel.aggregate([
        {
          $match: {
            userId: { $in: filteredStudentIds },  // On utilise directement les IDs déjà filtrés
            userStatus: "active",
            matchedTutors: { $exists: true, $ne: [] }
          }
        },
        {
          $count: "activePairsCount"
        }
      ]);

      activePairs = activePairsResult.length > 0 ? activePairsResult[0].activePairsCount : 0;
    }

    // Calculer les pourcentages d'évolution en utilisant la fonction utilitaire
    const tutorsPercentage = statsUtils.calculateEvolutionPercentage(
      newTutorsCount, 
      previousTutorsCount
    );
    
    const studentsPercentage = statsUtils.calculateEvolutionPercentage(
      newStudentsCount,
      previousStudentsCount
    );

    // Calculer l'évolution des inscriptions totales
    const newRegistrations = newTutorsCount + newStudentsCount;
    const previousPeriodRegistrations = previousTutorsCount + previousStudentsCount;

    const registrationsPercentage = statsUtils.calculateEvolutionPercentage(
      newRegistrations,
      previousPeriodRegistrations
    );

    // Construire l'objet de résultat
    const result = {
      tutors: {
        total: activeTutors,
        new: newTutorsCount,
        percentage: tutorsPercentage,
        available: availableTutors,
      },
      students: {
        total: totalStudents,
        new: newStudentsCount,
        percentage: studentsPercentage,
        available: availableStudents,
      },
      newRegistrations: {
        total: newRegistrations,
        percentage: registrationsPercentage,
      },
      activePairs: activePairs,
      periodDetails: {
        currentPeriod: {
          startDate: currentPeriodStart,
          endDate: currentPeriodEnd,
        },
        previousPeriod: {
          startDate: previousPeriodStart,
          endDate: previousPeriodEnd,
        },
      },
    };

    return {
      status: true,
      message: "Statistiques de tutorat récupérées avec succès",
      data: result,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de la récupération des statistiques de tutorat",
      data: error,
    };
  }
};

// On remplace la fonction processReportsInBatches par une référence à celle qui est dans stats.utils.js
const processReportsInBatches = statsUtils.processReportsInBatches;
