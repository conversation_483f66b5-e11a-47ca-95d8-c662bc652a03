const sessionModel = require("../../models/Sessions.Model.js");

const sessionsConstants = require("../../utils/constants/sessions.constants.js");

const bigBlueButtonHelper = require("../../controllers/bigBlueButton/bigBlueButton.Helper.js");

const apiResponse = require("../../controllers/apiresponse/ApiResponseHelper.js");

const userHelper = require("../user/User.Helper.js");

const userAdminHelper = require("../user/user.administration.helper.js");

const parentHelper = require("../parent/parent.helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const mailingModel = require("../../models/Mailings.Model.js");
const { JWT } = require("google-auth-library");
const { calendar_v3 } = require("@googleapis/calendar");
const Calender = calendar_v3.Calendar;
const credential = require("../../middleware/firebase/google_meet.json");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");

const sessionJob = require("../../services/aaJobs/sessions.job.js");

const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

//import tutorPreferences
const tutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");

const studentUserConstants = require("../../utils/constants/student.constants.js");
const tutorConstant = require("../../utils/constants/tutor.constants.js");
//studentPreferences model
const studentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const sessionRepotingModel = require("../../models/SessionReporting.Model.js");
const moment = require("moment");

const schoolYearModel = require("../../models/ScholarYear.Model.js");

const tutorHelper = require("../tutor/tutor.helper.js");
const tutorJob = require("../../services/aaJobs/tutor.job.js");
const sendGridConstants = require("../../utils/constants/sendgrid.constants.js");

const studentHelper = require("../students/student.helpers.js");

const crscHelper = require("../reports/cr.helper.js");

const publicHolidaysHelper = require("../holidays/public.holidays.helper.js");

const holidaysZoneHelper = require("../holidays/holidays.zone.helper.js");

const { v4: uuidv4 } = require("uuid");

const UserAdminsModel = require("../../models/User.Administration.Model.js");
const apiLoggerS = require("../../services/logger/LoggerService.js");

const logger = new apiLoggerS("sessions.helper.js");
let link = "";
exports.getParentProfilePerStudent = async (userId) => {
  try {
    const studentProfile = await studentHelper.getStudentPreferences(userId);

    return { status: true, data: studentProfile.data };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

exports.createNewSession = async (
  sessionData,
  calledFromRecurringFunction,
  { email, fullName, userId, userRole },
  origin
) => {
  try {
    const recurrence = this.getRecurrenceObject(
      sessionData?.recurrence?.recurrenceName
        ? sessionData.recurrence.recurrenceName
        : sessionsConstants.sessionsRecurrence.ONE_TIME
    );
    const program = sessionData.program
      ? sessionData.program
      : HOME_CLASSES.programId;

    const sessionType = sessionData.sessionType
      ? sessionData.sessionType
      : sessionsConstants.sessionsTypes.SESSIONS_0;

    const sessionId = sessionData.sessionId ? sessionData.sessionId : null;

    const parentSessionId = sessionData.parentSessionId
      ? sessionData.parentSessionId
      : recurrence.recurrenceName ===
        sessionsConstants.sessionsRecurrence.ONE_TIME
      ? sessionId
      : null;

    //TODO: update the tutor, doc comment: Pre-filled Tutor's first and last name
    const tutor = sessionData.tutors ? sessionData.tutors : [];
    //TODO: update the student, doc comment: Pre-filled Student's first and last name
    const student = sessionData.students ? sessionData.students : [];

    //TODO: update vsc, doc comment: Pre-filled VSC's first and last name
    const vsc = sessionData.vsc ? sessionData.vsc : [];

    //Scheduled Selected by default, Canceled, Failed,  Confirmed
    const status = sessionData.status;

    const school = sessionData.school ? sessionData.school : null;

    const placeOrRoom = sessionData.placeOrRoom
      ? sessionData.placeOrRoom
      : null;
    const sessionDate = this.buildSessionDateObject(
      sessionData.sessionDate ? sessionData.sessionDate : null
    );
    const updatingFutureSessions = sessionData.updatingFutureSessions
      ? sessionData.updatingFutureSessions
      : false;

    const scheduleDuringHolidays = sessionData.scheduleDuringHolidays
      ? sessionData.scheduleDuringHolidays
      : false;

    const report = this.getReportObject(
      sessionData.reportLink ? sessionData.reportLink : null,
      sessionData.description ? sessionData.description : null
    );

    const sessionLink = sessionData.sessionLink
      ? sessionData.sessionLink
      : null;
    // console.log("sessionLinksessionLink", sessionLink);
    //create new Session Model
    const newSession = new sessionModel({
      sessionId: sessionId,
      parentSessionId: parentSessionId,
      program: program,
      sessionType: sessionType,
      tutors: tutor,
      students: student,
      vsc: vsc,
      status: status,
      school: school,
      placeOrRoom: placeOrRoom,
      sessionDate: sessionDate,
      updatingFutureSessions: updatingFutureSessions,
      recurrence: recurrence,
      scheduleDuringHolidays: scheduleDuringHolidays,
      report: report,
      sessionLink: sessionLink,
      createdBy: {
        email,
        fullName,
        userId,
        userRole,
      },
      origin,
    });
    if (!isGoogleMeetLink(sessionLink)) {
      //generate a meeting data, meeting data is used to create a meeting in bigBlueButton
      const newRoom = await bigBlueButtonHelper.createNewRoomInMongoDB(
        program,
        sessionsConstants.sessionsDefaultMessage.WELCOME_MESSAGE
      );
      await bigBlueButtonHelper.createNewRoomInBigBlueButtonServer(newRoom);

      const meetingRoom = this.generateMeetingData(newRoom.meetingID);
      //update meetingRoom in session
      newSession.meetingRoom = meetingRoom;
    } else {
      newSession.meetingRoom = {
        sessionsUrl: sessionData.sessionLink,
        meetingID: sessionId,
      };
    }

    //update tutor matching object

    await this.createOrUpdateMatchingPerTutor(sessionData);

    //update student matching object

    await this.createOrUpdateMatchingPerStudent(sessionData);

    //save new Session Model
    const savedSession0 = await newSession.save();
    const ParentSession = JSON.parse(JSON.stringify(savedSession0));
    if (savedSession0) {
      if (
        savedSession0.status == "session-0-to-be-scheduled" &&
        calledFromRecurringFunction === false &&
        savedSession0.recurrence?.recurrenceName ==
          sessionsConstants.sessionsRecurrence.ONE_TIME
      ) {
        //notify the student and tutor about the session
        await sessionJob.sendNotificationForPresentationSession(
          savedSession0,
          origin
        );
      }

      if (!calledFromRecurringFunction) {
        //generate all recurring sessions
        if (
          savedSession0.recurrence?.recurrenceName !==
          sessionsConstants.sessionsRecurrence.ONE_TIME
        ) {
          const tutorUserId = savedSession0.tutors[0].userId;

          const recurringType = savedSession0.recurrence?.recurrenceName;

          const recurentSession =
            await this.generateAllNextSessionsForRecurringType(
              tutorUserId,
              savedSession0,
              recurringType,
              false
            );
          if (recurentSession?.status == false) {
            return {
              status: false,
              resp: apiResponse.apiConstants.API_REQUEST_FAILED,
              message: "Session 0 creation failed",
              data: null,
            };
          }
          ParentSession.sessionLink = link;
          // console.log('ParentSession', ParentSession)
          if (ParentSession.status == "session-0-to-be-scheduled") {
            await sessionJob.sendNotificationForPresentationSession(
              ParentSession,
              origin
            );
          }
        }
      }

      return {
        status: true,
        resp: apiResponse.apiConstants.API_REQUEST_SUCCESS,
        message: "Session 0 created successfully",
        data: savedSession0,
      };
    } else {
      return {
        status: false,
        resp: apiResponse.apiConstants.API_REQUEST_FAILED,
        message: "Session 0 creation failed",
        data: null,
      };
    }
  } catch (error) {
    logger.newLog(logger.getLoggerTypeConstants().sessions).error({
      message: `Error in createNewSession:`,
      request: {
        requestBody: error,
      },
    });
    console.log(error);
    return { status: false, message: error.message };
  }
};

//generate new sessionId for recurring sessions : first two digits is from sessionConstants.initialDynamicIds
exports.generateNewSessionId = () => {
  return (
    sessionsConstants.initialDynamicIds.SESSION_ID_FIRST_TWO_DIGITS +
    uuidv4().substring(0, 8)
  );
};

//build date and time object
exports.buildSessionDateObject = (sessionDateObject) => {
  if (sessionDateObject) {
    const startDate = dateTimeHelper.convertStringDateToFullDateTime(
      sessionDateObject.startDate
    );
    const endDate = dateTimeHelper.convertStringDateToFullDateTime(
      sessionDateObject.endDate
    );

    const month = startDate.format("MM");
    const dayOfTheWeek = startDate.day() === 0 ? 6 : startDate.day() - 1;
    const sessionDate = {
      startDate: startDate,
      endDate: endDate,
      month: month,
      dayOfTheWeek: dayOfTheWeek,
      startHour: startDate.hour(), // 0 to 23
      endHour: endDate.hour(), // 0 to 23
    };
    return sessionDate;
  } else {
    return null;
  }
};

//update session
exports.updateSession = async (
  sessionId,
  sessionDocumentObject,
  { email, fullName, userId, userRole }
) => {
  try {
    const sessionToUpdate = await sessionModel.findOne({ sessionId });
    if (!sessionToUpdate) {
      return { status: false, message: "Session not found", data: null };
    }

    const sessionDate = this.buildSessionDateObject(
      sessionDocumentObject.sessionDate
    );

    sessionDocumentObject.sessionDate = sessionDate;
    sessionDocumentObject.lastUpdatedAt =
      dateTimeHelper.getCurrentDateTimeInParisZone();
    sessionDocumentObject.lastUpdatedBy = {
      email,
      fullName,
      userId,
      userRole,
    };

    const updateFutureSessions = sessionDocumentObject.updatingFutureSessions;
    // if(sessionDocumentObject.status == sessionsConstants.sessionsStatus.CANCELED){
    //   await this.updateMatchingPreferences(sessionObjectFields)
    // }
    const parentSession = await SessionsModel.findOne({
      parentSessionId: null,
      sessionId: sessionToUpdate.parentSessionId,
    });
    const recurrenceType = parentSession?.recurrence?.recurrenceName;
    if (
      updateFutureSessions &&
      (recurrenceType == sessionsConstants.sessionsRecurrence.BI_MONTHLY ||
        recurrenceType == sessionsConstants.sessionsRecurrence.WEEKLY)
    ) {
      logger
        .newLog(logger.getLoggerTypeConstants().sessions)
        .info(
          `all future sessions will be updated, first delete all child sessions and then update the parent session`
        );
      //set a log with the message : all future sessions will be updated, first delete all child sessions and then update the parent session
      console.log(
        "all future sessions will be updated, first delete all child sessions and then update the parent session"
      );

      const sessionObjects = {
        email,
        fullName,
        userId,
        userRole,
        ...sessionDocumentObject,
      };
      await this.updateAllFutureSessions(
        sessionId,
        sessionObjects,
        sessionDocumentObject.tutors[0].userId,
        recurrenceType
      );
    }

    //update tutor matching object
    await this.createOrUpdateMatchingPerTutor(sessionDocumentObject);

    //update student matching object
    await this.createOrUpdateMatchingPerStudent(sessionDocumentObject);
    if (!sessionDocumentObject.meet) {
      //generate a meeting data, meeting data is used to create a meeting in bigBlueButton
      const newRoom = await bigBlueButtonHelper.createNewRoomInMongoDB(
        sessionDocumentObject.program,
        sessionsConstants.sessionsDefaultMessage.WELCOME_MESSAGE
      );
      await bigBlueButtonHelper.createNewRoomInBigBlueButtonServer(newRoom);

      const meetingRoom = this.generateMeetingData(newRoom.meetingID);
      //update meetingRoom in session
      sessionDocumentObject.meetingRoom = meetingRoom;
    }

    const sessionsDocument = updateFutureSessions
      ? sessionDocumentObject
      : await sessionModel
          .findOneAndUpdate({ sessionId: sessionId }, sessionDocumentObject, {
            new: true,
          })
          .exec();
    if (sessionsDocument) {
      if (
        sessionDocumentObject.status !== sessionToUpdate.status &&
        sessionDocumentObject.status ===
          sessionsConstants.sessionsStatus.SESSIONS_0_ABANDONED
      ) {
        await sessionJob.sendNotificationForCancelledSession(sessionsDocument);
      }
      //generate all recurring sessions
      /*  if (
        updateFutureSessions &&
        sessionsDocument.recurrence?.recurrenceName !==
          sessionsConstants.sessionsRecurrence.ONE_TIME
      ) {
        const tutorUserId = sessionsDocument.tutors[0].userId;

        const recurringType = sessionsDocument.recurrence?.recurrenceName;

        await this.generateAllNextSessionsForRecurringType(
          tutorUserId,
          sessionsDocument,
          recurringType
        );
      } */
      return {
        status: true,
        message: "Session updated successfully",
        data: sessionsDocument,
      };
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    logger
      .newLog(logger.getLoggerTypeConstants().sessions)
      .error("erroe in updateSession", error);
    console.log(error);
    return { status: false, message: error.message };
  }
};

//Create or Update matching object in tutorPreferences collection
exports.createOrUpdateMatchingPerTutor = async (sessionDocument) => {
  try {
    const students = sessionDocument.students;
    const tutorId = sessionDocument.tutors[0].userId;
    const sessionId = sessionDocument.sessionId;
    const sessionStatus = sessionDocument.status;
    let listOfStudentIds = [];
    // if(sessionStatus != sessionsConstants.sessionsStatus.CANCELED) {
    students.forEach((student) => {
      // if (student.userStatus === studentUserConstants.userStatus.ACTIVE) {
      listOfStudentIds.push(student.userId);
      // }
    });
    // }
    //find matching object in tutorPreferences collection base on userId and sessionId
    const matchingObject = await tutorPreferencesModel.findOne({
      userId: tutorId,
      matching: { $elemMatch: { sessionId: sessionId } },
    });
    //if session status is session-0-abandoned or session-0-finish-stop delete matching object
    if (
      sessionStatus === sessionsConstants.sessionsStatus.SESSIONS_0_ABANDONED ||
      sessionStatus === sessionsConstants.sessionsStatus.SESSIONS_0_FINISH_STOP
    ) {
      console.log("delete matching object in tutorPreferences collection");
      if (matchingObject) {
        //remove matching object
        matchingObject.matching = matchingObject.matching.filter(
          (matching) => matching.sessionId !== sessionId
        );

        //save updated matching object
        const tutorPreferences = await matchingObject.save();

        return {
          status: true,
          message: "Matching object deleted",
          data: tutorPreferences,
        };
      }
    }

    //if no matching object found then create new matching object
    if (!matchingObject) {
      const matching = {
        sessionId: sessionId,
        studentIds: listOfStudentIds,
        status: sessionStatus,
      };

      const tutorPreferences = await tutorPreferencesModel.findOneAndUpdate(
        { userId: tutorId },
        { $push: { matching: matching } },
        { new: true }
      );

      /* await tutorJob.sendNotificationToTutor({
        email: tutorPreferences.contactDetails.email,
        data: {
          firstName: tutorPreferences.contactDetails.firstName,
        },
        templateId: sendGridConstants.EmailTemplates.TEMPLATE_TUTOR_EMAIL_STUDENT_1_FOUND_STATUT
      }) */

      return {
        status: true,
        message: "Matching object created",
        data: tutorPreferences,
      };
    }

    //if matching object found then update it
    for (let i = 0; i < matchingObject.matching.length; i++) {
      if (matchingObject.matching[i].sessionId === sessionId) {
        matchingObject.matching[i].studentIds = listOfStudentIds;
        matchingObject.matching[i].status = sessionStatus;
      }
    }
    //save updated matching object
    const tutorPreferences = await matchingObject.save();

    return {
      status: true,
      message: "Matching object updated",
      data: tutorPreferences,
    };
  } catch (error) {
    logger
      .newLog(logger.getLoggerTypeConstants().sessions)
      .error(`error in createOrUpdateMatchingPerSession : `, error);
    console.log("error in createOrUpdateMatchingPerSession : ", error);
    return { status: false, message: error.message };
  }
};
exports.deleteMatchingInTutorPreferences = async (sessionId) => {
  try {
    const updateResult = await tutorPreferencesModel.findOneAndUpdate(
      {
        "matching.sessionId": sessionId,
      },
      { $pull: { matching: { sessionId: sessionId } } },

      { new: true } // Return the modified document after update
    );
    if (updateResult) {
      return {
        status: true,
        message: "Matching object deleted",
        data: updateResult,
      };
    } else {
      return { status: true, message: "Matching object not found", data: null };
    }
  } catch (error) {
    console.log("error in deleteMatchingInTutorPreferences : ", error);
    return { status: false, message: error.message };
  }
};
exports.restoreAvailabilitiesInTutorPreferences = async (sessionId) => {
  try {
    const parentSession = await SessionsModel.findOne({
      parentSessionId: null,
      sessionId,
    });
    if (parentSession) {
      const updateResult = await tutorPreferencesModel.updateOne(
        {
          "matching.sessionId": sessionId,
          availability: {
            $elemMatch: {
              dayOfTheWeek: parentSession?.sessionDate?.dayOfTheWeek,
              startTime: moment(parentSession?.sessionDate?.startDate).format(
                "HH:mm"
              ),
            },
          },
        },
        [
          {
            $set: {
              availability: {
                $map: {
                  input: "$availability",
                  as: "avail",
                  in: {
                    $cond: {
                      if: {
                        $and: [
                          {
                            $eq: [
                              "$$avail.dayOfTheWeek",
                              parentSession?.sessionDate?.dayOfTheWeek,
                            ],
                          },
                          {
                            $eq: [
                              "$$avail.startTime",
                              moment(
                                parentSession?.sessionDate?.startDate
                              ).format("HH:mm"),
                            ],
                          },
                        ],
                      },
                      then: { $mergeObjects: ["$$avail", { taken: false }] },
                      else: "$$avail",
                    },
                  },
                },
              },
            },
          },
        ]
      );
      if (updateResult.modifiedCount > 0) {
        return {
          status: true,
          message: "Matching object deleted",
          data: updateResult,
        };
      } else {
        return {
          status: true,
          message: "Matching object not found",
          data: null,
        };
      }
    }
  } catch (error) {
    console.log("error in deleteMatchingInTutorPreferences : ", error);
    return { status: false, message: error.message };
  }
};
//Create or Update matching object in  studentPreferences collection
exports.createOrUpdateMatchingPerStudent = async (sessionDocument) => {
  try {
    const studentUserIds = sessionDocument.students.map(
      (student) => student.userId
    );
    const tutorId = sessionDocument.tutors[0].userId;
    const sessionId = sessionDocument.sessionId;
    const sessionStatus = sessionDocument.status;
    // Recherche des objets de matching dans la collection studentPreferences pour tous les students (userId)
    const matchingObjects = await studentPreferencesModel.find({
      userId: { $in: studentUserIds },
      matching: { $elemMatch: { sessionId: sessionId } },
      userStatus: studentUserConstants.userStatus.ACTIVE,
    });

    // Si le statut de la session est "abandoned" ou "finish-stop", supprimez l'objet de matching
    if (
      sessionStatus === sessionsConstants.sessionsStatus.SESSIONS_0_ABANDONED ||
      sessionStatus === sessionsConstants.sessionsStatus.SESSIONS_0_FINISH_STOP
    ) {
      console.log(
        "Deleting matching objects from studentPreferences collection"
      );

      // Parcourez tous les objets de matching pour chaque étudiant et supprimez l'objet de matching
      for (let matchingObject of matchingObjects) {
        matchingObject.matching = matchingObject.matching.filter(
          (matching) => matching.sessionId !== sessionId
        );

        // Enregistrez l'objet mis à jour
        await matchingObject.save();
      }

      return {
        status: true,
        message: "Matching object(s) deleted",
      };
    }

    // Si aucun matching n'est trouvé, créez un nouvel objet de matching pour chaque étudiant
    if (matchingObjects.length === 0) {
      const matching = {
        sessionId: sessionId,
        tutorId: tutorId,
        status: sessionStatus,
      };
      // console.log("Creating new matching object:", matching);
      let studentPreferences;
      // Pour chaque étudiant, ajoutez le matching dans leur tableau "matching"
      for (let studentUserId of studentUserIds) {
        studentPreferences = await studentPreferencesModel.findOneAndUpdate(
          { userId: studentUserId },
          { $push: { matching: matching } },
          { new: true }
        );
        // console.log(`Matching object created for student ${studentUserId}`);
      }

      return {
        status: true,
        message: "Matching objects created",
        data: studentPreferences,
      };
    }
    let studentPreferences;
    // Si des objets de matching existent, mettez à jour ces objets
    for (let matchingObject of matchingObjects) {
      for (let i = 0; i < matchingObject.matching.length; i++) {
        if (matchingObject.matching[i].sessionId === sessionId) {
          matchingObject.matching[i].tutorId = tutorId;
          matchingObject.matching[i].status = sessionStatus;
        }
      }

      // Enregistrez l'objet mis à jour
      studentPreferences = await matchingObject.save();
    }

    return {
      status: true,
      message: "Matching object(s) updated",
      data: studentPreferences,
    };
  } catch (error) {
    logger.newLog(logger.getLoggerTypeConstants().sessions).error({
      message: `Error in createOrUpdateMatchingPerSession:`,
      request: {
        requestBody: error,
      },
    });
    console.log("Error in createOrUpdateMatchingPerSession:", error);
    return { status: false, message: error.message };
  }
};

//delete matching object from studentPreferences collection
exports.deleteMatchingInStudentPreferences = async (sessionId) => {
  try {
    // retirer l'objet ayant un sessionId du matching
    const updateResult = await studentPreferencesModel.findOneAndUpdate(
      { "matching.sessionId": sessionId },
      { $pull: { matching: { sessionId: sessionId } } },
      { new: true } // Return the modified document after update
    );

    if (updateResult) {
      return {
        status: true,
        message: "Matching object deleted",
        data: updateResult,
      };
    } else {
      return { status: true, message: "Matching object not found", data: null };
    }
  } catch (error) {
    console.log("error in deleteMatchingInStudentPreferences : ", error);
    return { status: false, message: error.message };
  }
};

exports.deleteAllSession = async () => {
  //get all sessions
  const sessions = await sessionModel.find({}).exec();

  //delete all sessions
  for (let i = 0; i < sessions.length; i++) {
    const session = sessions[i];
    const sessionId = session.sessionId;
    await this.deleteSession(sessionId);
  }
};

//delete session
exports.deleteSession = async (data) => {
  try {
    await this.deleteMatchingInTutorPreferences(data.sessionId);
    await this.deleteMatchingInStudentPreferences(data.sessionId);
    // await crscHelper.deleteScCrReportBySessionId(data.sessionId);

    const userAdmin = await UserAdminsModel.findOne({ userId: data.userId });
    const sessionsDocument = await sessionModel
      .findOneAndUpdate(
        { sessionId: data.sessionId },
        {
          status: sessionsConstants.sessionsStatus.CANCELED,
          cancelingDetails: {
            cancelingReason: data.cancelingReason,
            cancelingDate: new Date(),
            userId: data.userId,
            userRole: data.userRole,
            userFullName:
              userAdmin.contactDetails.firstName +
              " " +
              userAdmin.contactDetails.lastName,
          },
        }
      )
      .exec();
    await this.updateMatchingPreferences(sessionsDocument);
    //delete student from tutor matchedStudents array

    if (sessionsDocument) {
      return {
        status: true,
        message: "Session deleted successfully",
        data: sessionsDocument,
      };
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
//batch delete sessions
exports.batchDeleteSessions = async (sessionIds, shouldDelete) => {
  try {
    // Step 1: Run all preference-related tasks in parallel for each session ID
    const preferenceTasks = sessionIds.map((sessionId) => {
      const tasks = [this.restoreAvailabilitiesInTutorPreferences(sessionId)];

      // Conditionally add delete tasks if shouldDelete is true
      if (shouldDelete) {
        tasks.push(
          this.deleteMatchingInTutorPreferences(sessionId),
          this.deleteMatchingInStudentPreferences(sessionId),
          crscHelper.deleteScCrReportBySessionId(sessionId)
        );
      }

      return Promise.all(tasks);
    });

    // Wait for all preferences to be restored or deleted
    await Promise.all(preferenceTasks);

    // Step 2: Fetch sessions in one query to avoid multiple calls
    const sessionsToDelete = await sessionModel
      .find({ sessionId: { $in: sessionIds } })
      .exec();

    // Step 3: Delete the sessions in one go
    const deleteResult = await sessionModel.deleteMany({
      sessionId: { $in: sessionIds },
    });

    // Step 4: Update preferences for each session
    const updateTasks = sessionsToDelete.map((session) =>
      this.updateMatchingPreferences(session)
    );

    await Promise.all(updateTasks);

    // Step 5: Return success/failure based on the delete result
    if (deleteResult.deletedCount > 0) {
      return {
        status: true,
        message: "Sessions deleted successfully",
        data: sessionsToDelete,
      };
    } else {
      return { status: false, message: "Sessions not found", data: null };
    }
  } catch (error) {
    console.error(error);
    return { status: false, message: error.message };
  }
};

//delete all child sessions base on sessionId and sessionDate.startDate greater than current date
exports.deleteAllChildSessions = async (sessionId) => {
  try {
    const currentDate = dateTimeHelper.getCurrentDateTimeInParisZone();

    //find all child sessions base on sessionId and sessionDate.startDate
    const sessionsDocument = await sessionModel
      .find({
        parentSessionId: sessionId,
        "sessionDate.startDate": { $gte: currentDate },
      })
      .exec();

    //delete all child sessions
    for (let i = 0; i < sessionsDocument.length; i++) {
      const session = sessionsDocument[i];
      const sessionId = session.sessionId;
      await this.deleteSession(sessionId);
    }

    console.log(
      `Number of child sessions deleted ♽ : ${sessionsDocument.length}`
    );
    if (sessionsDocument) {
      return {
        status: true,
        message: "Session deleted successfully",
        data: sessionsDocument,
      };
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
exports.updateAllFutureSessions = async (
  sessionId,
  sessionDocumentObject,
  tutorId,
  recurrenceType
) => {
  try {
    // Find the session with the given sessionId
    const sessionsToUpdate = await sessionModel.findOne({ sessionId });
    if (!sessionsToUpdate) {
      return { status: false, message: "Session not found", data: null };
    }

    // Find all future sessions with the same parentSessionId and startDate greater than the current session's startDate
    let futureSessions = [];
    if (sessionsToUpdate.parentSessionId) {
      futureSessions = await sessionModel.find({
        parentSessionId: sessionsToUpdate.parentSessionId,
        "sessionDate.startDate": {
          $gte: sessionsToUpdate.sessionDate.startDate,
        },
      });
    }
    //Delete all future childSession
    // let childSessionsToDelete = futureSessions.splice(nextSession.length);
    const childSessionsToDelete = futureSessions.map(
      (session) => session.sessionId
    );
    console.log("childSessionsToDelete", childSessionsToDelete);
    await this.batchDeleteSessions(childSessionsToDelete, false);
    //generate all future session$
    sessionDocumentObject.parentSessionId = sessionsToUpdate.parentSessionId;
    sessionDocumentObject.createdBy = sessionsToUpdate.createdBy;
    sessionParentReccurenceType = recurrenceType;
    await this.generateAllNextSessionsForRecurringType(
      tutorId,
      sessionDocumentObject,
      recurrenceType,
      true
    );
  } catch (error) {
    logger
      .newLog(logger.getLoggerTypeConstants().sessions)
      .error(`updateAllFutureSessions`, updateAllFutureSessions);
    console.log(error);
  }
};
//Get list of sessions for dashboard
exports.sessionsDashboard = async (data) => {
  try {
    const { page, pageSize, filter, sortBy, userId, userRole, coordinatorId } = data;
    let sort = buildSortBy(sortBy);
    let applyFilterData = await buildFilter(filter);
    let applyFilter = filter?.recurrence
      ? { ...applyFilterData.data }
      : {
          ...applyFilterData.data,
          parentSessionId: { $ne: null },
        };

    if (!applyFilterData.status) {
      return { status: false, message: applyFilterData.message };
    }

    const isCoordinatorOrVsc = [
      userRoleConstants.COORDINATOR,
      userRoleConstants.VSC,
    ].includes(userRole);

    let listOfSessions = [];
    let totalOfSessions = 0;

    const targetUserId = coordinatorId || userId;
      const userAdminDetails = await UserAdminsModel.findOne({ userId: targetUserId });
      
      if (!userAdminDetails) {
        return { status: false, message: "Coordinateur non trouvé" };
      }

    // Check if the user is an admin and their role is in [coordinator or vsc]
    if (isCoordinatorOrVsc || coordinatorId) {
      // Si un coordinatorId est fourni, on utilise ses préférences au lieu de celles du coordinateur connecté
      

      // Get the program preferences of the target coordinator
      const pref = userAdminDetails.administrationPreferences;

      // Check if the user has program preferences
      if (pref && pref.program?.length) {
        // Add the program preferences to the filter to match the student preferences with the programId got from admin preferences model
        applyFilter.program = {
          $in: pref.program.map((prog) => prog.programId),
        };

        // Get the useradmin program from the program preferences
        const program = pref.program[0];
        let establishmentIds = [];

        // Check if the program is (DEVOIRS_FAITS or ZUPDEFOOT) Or Home Classe
        if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
          // Tutor solidaire or ZupdeFoot userAdmin
          establishmentIds = pref.establishment.map(
            (est) => est.establishmentId
          );
          if (establishmentIds.length) {
            const studentsList = await studentPreferencesModel.find({
              "program.programId": program.programId,
              "assignment.establishments.establishmentId": {
                $in: establishmentIds,
              },
            });
            const studentIs = studentsList.map((student) => student.userId);
            const query = {
              ...applyFilter,
              "students.userId": { $in: studentIs },
            };
            const result = await sessionModel
              .find(query)
              .sort(sort)
              .skip(pageSize * (page - 1))
              .limit(pageSize)
              .exec();
            listOfSessions = result;
            totalOfSessions = await sessionModel.find(query).countDocuments();
          }
        } else if (
          program.programId == HOME_CLASSES.programId ||
          program.programId == CLASSSES_HOME.programId ||
          program.programId == ZUPDEFOOT.programId
        ) {
          // Home Classe userAdmin
          const departmentIds = pref.department.map((dep) => dep.departmentId);
          if (departmentIds.length) {
            const pipeline = [
              {
                $match: {
                  ...applyFilter,
                  students: { $exists: true, $not: { $size: 0 } },
                },
              },
              {
                $addFields: {
                  student: { $arrayElemAt: ["$students", 0] },
                },
              },
              {
                $lookup: {
                  from: "studentpreferences",
                  let: { studentId: "$student.userId" },
                  pipeline: [
                    {
                      $match: {
                        $expr: {
                          $and: [
                            { $eq: ["$userId", "$$studentId"] },
                            {
                              $in: [
                                "$assignment.department.departmentId",
                                departmentIds,
                              ],
                            },
                          ],
                        },
                      },
                    },
                  ],
                  as: "studentpreferences",
                },
              },
              {
                $match: {
                  studentpreferences: { $exists: true, $not: { $size: 0 } },
                },
              },
              {
                $facet: {
                  metadata: [{ $count: "total" }],
                  data: [
                    { $sort: sort },
                    { $skip: pageSize * (page - 1) },
                    { $limit: pageSize },
                  ],
                },
              },
              {
                $project: {
                  data: 1,
                  total: { $arrayElemAt: ["$metadata.total", 0] },
                },
              },
            ];
            const result = await sessionModel.aggregate(pipeline).exec();

            if (result.length && result[0].data) {
              listOfSessions = result[0].data;
              totalOfSessions = result[0].total;
            } else {
              listOfSessions = [];
              totalOfSessions = 0;
            }
          }
        }
      }
    } else {
      if (filter?.tutorUserId) {
        applyFilter.tutors = { $elemMatch: { userId: filter.tutorUserId } };
      }
      if (filter?.studentUserId) {
        applyFilter.students = { $elemMatch: { userId: filter.studentUserId } };
      }
      listOfSessions = await sessionModel
        .find(applyFilter)
        .sort(sort)
        .skip(pageSize * (page - 1))
        .limit(pageSize)
        .exec();
      totalOfSessions = await sessionModel.countDocuments(applyFilter).exec();
    }

    let listWithSomeFields = listOfSessions?.map((session) => {
      let studentAbsenceNumber = session.students.filter(
        (student) => student.absence === true
      ).length;

      let tutorAbsenceNumber = session.tutors.filter(
        (tutor) => tutor.absence === true
      ).length;
      
      // Récupérer le statut du rapport pour le premier tuteur
      const tutorReport = session.tutors && session.tutors[0] && session.tutors[0].report ? 
        session.tutors[0].report.status : null;

      let sessionObject = {
        sessionId: session.sessionId,
        sessionDate: session.sessionDate,
        status: session.status,
        program: session.program,
        sessionType: session.sessionType,
        students: session.students,
        tutors: session.tutors,
        vsc: session.vsc,
        studentAbsenceNumber: studentAbsenceNumber,
        tutorAbsenceNumber: tutorAbsenceNumber,
        sessionRecurence: session.recurrence,
        report: session.report,
        createdBy: session.createdBy,
        establishment: session.school,
        note: session.note,
        sessionLink: session.sessionLink,
        statusOfReport: tutorReport
      };

      return sessionObject;
    });

    return {
      status: true,
      message: "Sessions list",
      data: listWithSomeFields,
      total: totalOfSessions || 0,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

exports.getPastCoordinatorSessions = async (userId) => {
  try {
    const currentDate = new Date();

    const userAdminDetails = await UserAdminsModel.findOne({ userId });

    if (!userAdminDetails) {
      return {
        status: false,
        message: "No admin details found for user",
        data: 0,
      };
    }

    const pref = userAdminDetails.administrationPreferences;

    if (!pref || !pref.program?.length) {
      return {
        status: false,
        message: "No program preferences found",
        data: 0,
      };
    }

    const program = pref.program[0];
    let establishmentIds = [];

    if (
      [DEVOIRS_FAITS.programId, ZUPDEFOOT.programId].includes(program.programId)
    ) {
      establishmentIds = pref.establishment.map((est) => est.establishmentId);
    } else if (program.programId === HOME_CLASSES.programId) {
      establishmentIds = pref.department.map((dep) => dep.departmentId);
    }

    if (!establishmentIds.length) {
      return {
        status: false,
        message: "No establishments or departments found",
        data: 0,
      };
    }

    const pipeline = [
      {
        $match: {
          "sessionDate.startDate": { $lt: currentDate },
          tutors: { $exists: true, $not: { $size: 0 } },
        },
      },
      {
        $addFields: {
          tutor: { $arrayElemAt: ["$tutors", 0] },
        },
      },
      {
        $lookup: {
          from: "tutorpreferences",
          let: { tutorId: "$tutor.userId" },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$userId", "$$tutorId"] },
                    {
                      $in: [
                        "$assignment.establishments.establishmentId",
                        establishmentIds,
                      ],
                    },
                  ],
                },
              },
            },
          ],
          as: "tutorPreferences",
        },
      },
      {
        $match: {
          tutorPreferences: { $exists: true, $not: { $size: 0 } },
        },
      },
    ];

    const pastSessions = await sessionModel.aggregate(pipeline).exec();

    if (pastSessions.length === 0) {
      return { status: false, message: "No past sessions found", data: 0 };
    }

    return {
      status: true,
      message: "Past sessions found",
      data: pastSessions.length,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

exports.getAllSessionsForCoordinator = async (userId, userRole) => {
  try {
    const applyFilter = {
      parentSessionId: { $ne: null },
      status: { $ne: "canceled" },
    };

    const isCoordinatorOrVsc = [
      userRoleConstants.COORDINATOR,
      userRoleConstants.VSC,
    ].includes(userRole);

    if (isCoordinatorOrVsc) {
      const userAdminDetails = await UserAdminsModel.findOne({ userId });
      const pref = userAdminDetails?.administrationPreferences;

      if (pref?.program?.length) {
        applyFilter.program = {
          $in: pref.program.map((prog) => prog.programId),
        };
 
        const program = pref.program[0];
        if (program.programId === DEVOIRS_FAITS.programId) {
          const establishmentIds = pref.establishment.map(est => est.establishmentId);
 
          if (establishmentIds.length) {
            const studentIds = await studentPreferencesModel.find({
              "program.programId": program.programId,
              "assignment.establishments.establishmentId": { $in: establishmentIds },
            }).distinct("userId");
 
            applyFilter["students.userId"] = { $in: studentIds };
          }
        } else if (
          [HOME_CLASSES.programId, CLASSSES_HOME.programId, ZUPDEFOOT.programId].includes(program.programId)
        ) {
          const departmentIds = pref.department.map(dep => dep.departmentId);
 
          if (departmentIds.length) {
            const studentIds = await studentPreferencesModel.find({
              "program.programId": {
                $in: [HOME_CLASSES.programId, CLASSSES_HOME.programId, ZUPDEFOOT.programId],
              },
              "assignment.department.departmentId": { $in: departmentIds },
            }).distinct("userId");
 
            applyFilter["students.userId"] = { $in: studentIds };
          }
        }
      }
    }
      applyFilter["sessionDate.startDate"] = {
    $gte: moment().startOf('month').toDate(),
    $lte: moment().endOf('month').toDate()
  }

  const sessionList = await sessionModel.find(applyFilter)
      .sort({ "sessionDate.startDate": 1 })
      .lean(); // lean() speeds up read-only queries
    if (!sessionList.length) {
      return { status: false, message: "No sessions found for coordinator" };
    }
 
    const pastSessionsCount = await this.getPastCoordinatorSessions(userId);
 
    // Get unique tutor and student IDs
    const tutorIds = [...new Set(sessionList.map(s => s.tutors?.[0]?.userId).filter(Boolean))];
    const studentIds = [...new Set(sessionList.map(s => s.students?.[0]?.userId).filter(Boolean))];
 
    // Fetch profiles in parallel
    const [tutorProfiles, studentProfiles] = await Promise.all([
      Promise.all(tutorIds.map(id => tutorHelper.getTutorProfile(id))),
      Promise.all(studentIds.map(id => this.getParentProfilePerStudent(id))),
    ]);
 
    const tutorMap = new Map(tutorIds.map((id, i) => [id, tutorProfiles[i]?.data]));
    const studentMap = new Map(studentIds.map((id, i) => [id, studentProfiles[i]?.data]));
 
    const nextSessionList = sessionList.map((session) => {
      const tutorId = session.tutors?.[0]?.userId;
      const studentId = session.students?.[0]?.userId;
      const studentProfile = studentMap.get(studentId) || {};
      const parentData = studentProfile?.parentData || null;
      const levelName = studentProfile?.assignment?.level?.[0]?.levelName || null;
 
      return {
        sessionId: session.sessionId,
        sessionType: session.sessionType,
        program: session.program,
        status: session.status,
        vsc: session.vsc,
        tutorProfile: tutorMap.has(tutorId) ? [tutorMap.get(tutorId)] : [],
        studentFullName: session.students?.[0]?.fullName || null,
        school: session.school,
        sessionLink: session.sessionLink,
        placeOrRoom: session.placeOrRoom,
        sessionDate: session.sessionDate,
        pastSessions: pastSessionsCount.data || 0,
        sessionRecurrence: session.recurrence,
        referent: parentData,
        level: levelName,
      };
    });

    return {
      status: true,
      message: "All sessions for coordinator",
      data: nextSessionList,
    };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//get session by id
exports.getSessionById = async (sessionId) => {
  try {
    const sessionsDocument = await sessionModel
      .findOne({ sessionId: sessionId })
      .exec();

    const studentId = sessionsDocument?.students?.[0]?.userId;

    const parentProfile = await this.getParentProfilePerStudent(studentId);
    // console.log("parentProfile", parentProfile);
    if (sessionsDocument) {
      const sessionReporting = await sessionRepotingModel.findOne({
        meetingID: sessionsDocument.meetingRoom.meetingID,
      });
      const sessionCrReport = await CrReportModel.findOne({
        sessionId: sessionId,
      }).exec();
      const combinedData = {
        ...sessionsDocument?._doc,
        ...sessionReporting?._doc,
        reportId: sessionCrReport?._id ? sessionCrReport._id : null,
        participantCount: sessionReporting?.participantCount,
        attendees: sessionReporting?.attendees,
        referent: parentProfile.status ? parentProfile?.data?.parentData : {},
        level: parentProfile?.data?.assignment?.level,
      };
      return { status: true, message: "Session found", data: combinedData };
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

// get session by studentId and tutorId
exports.getSessionByTuorIdStudentId = async (params) => {
  try {
    const {tutorId, studentId} = params
    let filter = { parentSessionId: { $ne: null } };
    // Ajouter les conditions selon les paramètres fournis
    if (tutorId && studentId) {
      // Si les deux IDs sont fournis, rechercher les deux
      filter["tutors.userId"] = tutorId;
      filter["students.userId"] = studentId;
    } else if (tutorId) {
      // Si seulement tutorId est fourni
      filter["tutors.userId"] = tutorId;
    } else if (studentId) {
      // Si seulement studentId est fourni
      filter["students.userId"] = studentId;
    }
    const sessionsDocument = await sessionModel
      .find(filter)
      .exec();
    if (sessionsDocument.length) {
      const listSessions = sessionsDocument.map(session => ({
        sessionDate: session.sessionDate,
        status : session.status,
        tutors: session.tutors,
        students: session.students,
        sessionLink: session.sessionLink,
        report: session.report,
        program: session.program,
        createdBy: session.createdBy
      }));

      return { status: true, message: "Session found", data: listSessions };
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//generate meetingId for bigBlueButton
exports.generateMeetingData = (meetingID) => {
  const meetingData = {
    sessionsUrl: "https://meet.aarifi.com/b/" + meetingID,
    meetingID: meetingID,
  };

  return meetingData;
};

//get report object
exports.getReportObject = (reportLink, description) => {
  let report = {
    reportLink: reportLink,
    description: description,
  };

  return report;
};

//get recurrence object  from recurrence sessionsConstants.sessionsRecurrence
exports.getRecurrenceObject = (recurrence) => {
  let recurrenceObject = {};
  switch (recurrence) {
    case sessionsConstants.sessionsRecurrence.ONE_TIME:
      recurrenceObject = {
        recurrenceNumber: 1,
        recurrenceName: sessionsConstants.sessionsRecurrence.ONE_TIME,
      };
      break;
    case sessionsConstants.sessionsRecurrence.WEEKLY:
      recurrenceObject = {
        recurrenceNumber: 7,
        recurrenceName: sessionsConstants.sessionsRecurrence.WEEKLY,
      };
      break;
    case sessionsConstants.sessionsRecurrence.BI_MONTHLY:
      recurrenceObject = {
        recurrenceNumber: 15,
        recurrenceName: sessionsConstants.sessionsRecurrence.BI_MONTHLY,
      };
      break;
    default:
      recurrenceObject = {
        recurrenceNumber: 1,
        recurrenceName: sessionsConstants.sessionsRecurrence.ONE_TIME,
      };
      break;
  }

  return recurrenceObject;
};

// Verify if the userId has access to the session by checking if they are listed as a student in the students array of the session document, and matching the session ID.
exports.verifySessionAccess = async (userDocument, sessionId) => {
  try {
    const userId = userDocument.userId;
    let session;
    if (
      isUserRoleIsParent(userDocument) ||
      userDocument.userRole === userRoleConstants.COORDINATOR ||
      userDocument.userRole === userRoleConstants.MANAGER ||
      userDocument.userRole === userRoleConstants.SUPER_ADMINISTRATOR ||
      userDocument.userRole === userRoleConstants.VSC
    ) {
      //get list of children ids for parent
      const childrenIds =
        await userHelper.checkIfParentUserIdIsMatchingWithChildren(userId);

      if (childrenIds.length > 0) {
        const sessionsDocument = await sessionModel.findOne({
          sessionId: sessionId,
        });

        //check if any of the children ids is matching with the session students ids array and the return that student userId
        const studentUserId = sessionsDocument.students.find((student) =>
          childrenIds.includes(student.userId)
        );
        if (studentUserId) {
          const userDocument = await userHelper.getUserDetailsByUserId(
            studentUserId
          );
          return {
            status: apiResponse.apiConstants.SESSIONS_DATA,
            message: "Session access verified",
            data: sessionsDocument,
            userDocument: userDocument,
          };
        } else {
          return {
            status: apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS,
            message: "Session access not verified",
            data: null,
          };
        }
      } else if (
        userDocument.userRole === userRoleConstants.COORDINATOR ||
        userDocument.userRole === userRoleConstants.MANAGER ||
        userDocument.userRole === userRoleConstants.SUPER_ADMINISTRATOR ||
        userDocument.userRole === userRoleConstants.VSC
      ) {
        session = await sessionModel.findOne({
          sessionId: sessionId,
        });
      } else {
        return {
          status: apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS,
          message: "Session access not verified",
          data: null,
        };
      }
    } else {
      session = await sessionModel.findOne({
        sessionId: sessionId,
        $or: [{ "students.userId": userId }, { "tutors.userId": userId }],
      });
    }

    if (session) {
      return {
        status: apiResponse.apiConstants.SESSIONS_DATA,
        message: "Session access verified",
        data: session,
      };
    } else {
      return {
        status: apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS,
        message: "Session access not verified",
        data: null,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      status: apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS,
      message: error.message,
    };
  }
};

function isUserRoleIsParent(userDocument) {
  if (userDocument.userRole == userRoleConstants.ROLE_PARENT) {
    return true;
  } else {
    return false;
  }
}

async function buildFilter(filter) {
  try {
    let applyFilter = {};
    let parsedFilter = {};

    //check if the filter is json or not
    if (filter != undefined && filter != null && filter != "") {
      if (!isJSON(filter)) {
        return { status: false, message: "Filter is not a valid Json " };
      } else {
        parsedFilter = JSON.parse(filter);
      }
    }

    //add userRole to filter

    //filter with value string RegExp
    for (const [key, value] of Object.entries(parsedFilter)) {
      if (key == "school") {
        const schoolName = value;
        //the school property in collection is school: String
        applyFilter["school"] = { $eq: schoolName };
      } else if (key == "sessionType") {
        applyFilter["sessionType"] = value;
      } else if (key == "status" || key == "sessionStatus") {
        applyFilter["status"] = value;
      } else if (key == "program") {
        applyFilter["program"] = value;
      } /* else if (key == "sectorId") {
                    const sectorId = value;
                    const tutorIds = await tutorHelper.getListOfTutorIdsBySectorId(sectorId);

                    const studentIds = await studentHelper.getListOfStudentIdsBySectorId(sectorId);

                    applyFilter = {
                         $or: [{ "tutors.userId": { $in: tutorIds.data } }, { "students.userId": { $in: studentIds.data } }],
                    };
               } */ else if (key == "tutor") {
        applyFilter["tutors.fullName"] = { $regex: value, $options: "i" };
      } else if (key == "student") {
        applyFilter["students.fullName"] = { $regex: value, $options: "i" };
      } else if (key == "globalSearch") {
        // Recherche globale qui combine la recherche sur les noms des tuteurs et des élèves
        applyFilter["$or"] = [
          { "tutors.fullName": { $regex: value, $options: "i" } },
          { "students.fullName": { $regex: value, $options: "i" } }
        ];
      } else if (key == "vsc") {
        applyFilter["vsc.fullName"] = { $regex: value, $options: "i" };
      } else if (key == "byDate") {
        applyFilter["sessionDate.startDate"] = {
          $gte: moment(value.startDate.split(" ")[0]).startOf("day").toDate(),
          $lte: moment(value.endDate.split(" ")[0]).endOf("day").toDate(),
        };
      } else if (key == "recurence") {
        applyFilter["recurrence.recurrenceName"] = value;
        if (value == "one-time") {
          applyFilter = {
            $expr: {
              $eq: ["$sessionId", "$parentSessionId"],
            },
            parentSessionId: { $ne: null },
          };
        } else {
          applyFilter["parentSessionId"] = { $eq: null };
        }
      } else if (key == "establishment") {
        applyFilter["school"] = { $regex: value, $options: "i" };
      } else if (key === "tutorUserId") {
        applyFilter["tutors"] = { $elemMatch: { userId: value } };
      } else if (key === "studentUserId") {
        applyFilter["students.userId"] = value;
      }
    }

    //if filter is empty then add default filter base on status return all sessions excluding      SESSIONS_0_FINISH_STOP: "session-0-finish-stop",  SESSIONS_0_ABANDONED: "session-0-abandoned",
    /* if (Object.keys(applyFilter).length === 0) {
      applyFilter = {
        status: {
          $nin: [
            sessionsConstants.sessionsStatus.SESSIONS_0_FINISH_STOP,
            sessionsConstants.sessionsStatus.SESSIONS_0_ABANDONED,
          ],
        },
      };
    } */

    // applyFilter["parentSessionId"] = { $eq: null };

    return { status: true, data: applyFilter };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);

    //check if sortBy is empty or not
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "dateTime") {
        delete sortByObject[key];
        key = "dateAndTime.timeStamp";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = { "sessionDate.startDate": 1 };
  }

  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    console.log(e);
    return false;
  }
}

//#region  RECURRING SESSIONS

//get school Year from date now
exports.getSchoolYearFromDateNow = async () => {
  try {
    const dateNow = dateTimeHelper.getCurrentDateInParisZone();
    // find the school year in schoolYearModel
    const schoolYearDocument = await schoolYearModel.findOne({
      startDate: { $lte: dateNow },
      endDate: { $gte: dateNow },
    });
    if (schoolYearDocument) {
      return {
        status: true,
        message: "School year for date now found",
        data: schoolYearDocument,
      };
    } else {
      return { status: false, message: "School year not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

const momentTimeZone = require("moment-timezone");
const { statusOfTutors } = require("../../utils/constants/tutor.constants.js");
const { log } = require("winston");
const EducationAnnuaireModel = require("../../models/EducationAnnuaire.Model.js");
const { subtract } = require("lodash");
const SessionsModel = require("../../models/Sessions.Model.js");
const CrReportModel = require("../../models/Cr.Report.Model.js");

//generate all next sessions for recurring type
exports.generateAllNextSessionsForRecurringType = async (
  tutorUserId,
  sessionObject,
  recurringType,
  updating
) => {
  try {
    const sessionId = updating
      ? sessionObject.parentSessionId
      : sessionObject.sessionId;
    //list of all dates for next sessions
    let nextSessions =
      await this.getArrayOfDatesBetweenTwoDatesForRecurringSession(
        tutorUserId,
        sessionObject,
        recurringType
      );
    if (!nextSessions.status || nextSessions.data?.length == 0) {
      return { status: false, message: nextSessions.message, data: null };
    }
    nextSessions = nextSessions.data;
    //for each date in nextSessions generate a new session
    for (let i = 0; i < nextSessions.length; i++) {
      let nextSessionDate = nextSessions[i];

      //set the date and time for the session
      sessionObject.sessionDate.startDate = nextSessionDate.startDate;
      sessionObject.sessionDate.endDate = nextSessionDate.endDate;
      // sessionObject.sessionDate.month = moment(nextSessionDate.startDate).month() + 1
      //update sessionId
      sessionObject.sessionId = this.generateNewSessionId();

      sessionObject.recurrence = {
        recurrenceNumber: 1,
        recurrenceName: sessionsConstants.sessionsRecurrence.ONE_TIME,
      };

      //remove recurring object
      // sessionObject.recurrence = null;

      //set the session id to parentSessionId, if parentSessionId not found then set the current session id
      sessionObject.parentSessionId = sessionId;
      const { createdBy, ...sessionObjectFields } = updating
        ? sessionObject
        : sessionObject._doc;
      sessionObjectFields.sessionLink = isGoogleMeetLink(
        sessionObjectFields.sessionLink
      )
        ? sessionObject.sessionLink
        : `${process.env.APP_BASE_URL}/session/${sessionObject.sessionId}`; //create a new session

      if (i == 0) {
        link = sessionObjectFields.sessionLink;
      }
      //create a new session
      await this.createNewSession(sessionObjectFields, true, createdBy);
      if (
        sessionObjectFields.status == sessionsConstants.sessionsStatus.CANCELED
      ) {
        await this.updateMatchingPreferences(sessionObjectFields);
      }
    }
    return { status: true, message: "Session created successfully", data: [] };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//Get array of dates between two dates School Year and Tutor commitment dates for recurring type weekly and bi-monthly
exports.getArrayOfDatesBetweenTwoDatesForRecurringSession = async (
  tutorUserId,
  sessionObject,
  recurringType
) => {
  try {
    let schoolYearDocument = await this.getSchoolYearFromDateNow();
    let tutorCommitmentObject =
      await tutorHelper.getTutorCommitmentStartDateAndEndDate(tutorUserId);
    if (!schoolYearDocument.status && !tutorCommitmentObject.status) {
      return {
        status: false,
        message: "School year and tutor commitment not found",
        data: null,
      };
    }
    tutorCommitmentObject = tutorCommitmentObject.data;
    schoolYearDocument = schoolYearDocument.data;

    //generate a object with startDate and endDate between school year and tutor commitment
    let startDateAndEndDateObjectForAllSession =
      await this.getStartDateAndEndDateBetweenSchoolYearAndTutorCommitment(
        schoolYearDocument,
        tutorCommitmentObject
      );
    startDateAndEndDateObjectForAllSession =
      startDateAndEndDateObjectForAllSession.data;
    const arrayOfDates = await this.listOfAllRecurringDates(
      startDateAndEndDateObjectForAllSession.startDate,
      startDateAndEndDateObjectForAllSession.endDate,
      sessionObject,
      recurringType
    );
    return {
      status: true,
      message: "Dates generated successfully",
      data: arrayOfDates,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

exports.getStartDateAndEndDateBetweenSchoolYearAndTutorCommitment = async (
  schoolYearDocument,
  tutorCommitmentObject
) => {
  try {
    //school year start date and end date
    const schoolYearStartDate = momentTimeZone(schoolYearDocument.startDate);
    const schoolYearEndDate = momentTimeZone(schoolYearDocument.endDate);

    //tutor commitment start date and end date
    const tutorCommitmentStartDate = momentTimeZone(
      tutorCommitmentObject.commitmentStartDate
    );
    const tutorCommitmentEndDate = momentTimeZone(
      tutorCommitmentObject.commitmentEndDate
    );

    let startDate = null;
    let endDate = null;

    // Check if tutor commitment start date is between school year start and end dates
    if (!tutorCommitmentStartDate) {
      startDate = schoolYearStartDate;
    } else if (
      tutorCommitmentStartDate.isBetween(schoolYearStartDate, schoolYearEndDate)
    ) {
      startDate = tutorCommitmentStartDate;
    } else if (
      tutorCommitmentEndDate.isBetween(schoolYearStartDate, schoolYearEndDate)
    ) {
      // Check if tutor commitment end date is between school year start and end dates
      startDate = schoolYearStartDate;
    } else if (
      schoolYearStartDate.isBetween(
        tutorCommitmentStartDate,
        tutorCommitmentEndDate
      )
    ) {
      // Check if school year start date is between tutor commitment start and end dates
      startDate = schoolYearStartDate;
    }

    // Check if tutor commitment end date is between school year start and end dates
    if (!tutorCommitmentStartDate) {
      endDate = schoolYearEndDate;
    } else if (
      tutorCommitmentEndDate.isBetween(schoolYearStartDate, schoolYearEndDate)
    ) {
      endDate = tutorCommitmentEndDate;
    } else if (
      tutorCommitmentStartDate.isBetween(schoolYearStartDate, schoolYearEndDate)
    ) {
      // Check if tutor commitment start date is between school year start and end dates
      endDate = schoolYearEndDate;
    } else if (
      schoolYearEndDate.isBetween(
        tutorCommitmentStartDate,
        tutorCommitmentEndDate
      )
    ) {
      // Check if school year end date is between tutor commitment start and end dates
      endDate = schoolYearEndDate;
    }

    if (startDate === null || endDate === null) {
      return {
        status: false,
        message: "No date found between school year and tutor commitment",
        data: null,
      };
    }

    return {
      status: true,
      message: "Start and end dates found",
      data: { startDate: startDate.format(), endDate: endDate.format() },
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

exports.listOfAllRecurringDates = async (
  startDate,
  endDate,
  sessionObject,
  recurringType
) => {
  try {
    // Validate input
    if (
      !momentTimeZone(startDate).isValid() ||
      !momentTimeZone(endDate).isValid()
    ) {
      throw new Error("Invalid date input");
    }

    const sessionData = sessionObject.sessionDate;

    //session start date and end date
    const sessionStartDate = momentTimeZone(sessionData.startDate);
    const sessionEndDate = momentTimeZone(sessionData.endDate);
    const dayOfTheWeekForSession =
      dateTimeHelper.getDayOfTheWeekFromDate(sessionStartDate);

    //setup start hour and minute for start date
    const sessionStartHour = sessionStartDate.hour();
    const sessionStartMinute = sessionStartDate.minute();

    //setup start hour and minute for end date
    const sessionEndHour = sessionEndDate.hour();
    const sessionEndMinute = sessionEndDate.minute();

    //check if startDate is before current date; set date to current date
    let currentDate = dateTimeHelper.getCurrentDateInParisZone();
    currentDate = momentTimeZone(currentDate);
    //get startDate of session
    const startDateOfSession = momentTimeZone(sessionData.startDate);

    if (momentTimeZone(startDateOfSession).isBefore(startDate)) {
      currentDate = startDate;
    } else {
      currentDate = startDateOfSession;
    }
    if (recurringType === sessionsConstants.sessionsRecurrence.WEEKLY) {
      // add +1 week to current date
      currentDate = momentTimeZone(currentDate);
      // currentDate =
      //   sessionObject.program === HOME_CLASSES.programId
      //     ? momentTimeZone(currentDate).add(1, "week")
      //     : momentTimeZone(currentDate);
    } else if (
      recurringType === sessionsConstants.sessionsRecurrence.BI_MONTHLY
    ) {
      // add +2 weeks to current date
      currentDate = momentTimeZone(currentDate);
      // sessionObject.program === HOME_CLASSES.programId
      //   ? momentTimeZone(currentDate).add(2, "week")
      //   : momentTimeZone(currentDate);
    }

    //between start date and end date generate array of dates every week with day of the week from input @param dayOfWeek
    let arrayOfDates = [];
    while (currentDate.isBefore(endDate)) {
      //clone current date and set hour and minute from session start date
      let currentDateWithSessionStartHourAndMinute = momentTimeZone(
        currentDate
      ).set({ hour: sessionStartHour, minute: sessionStartMinute });
      //set day of the week to dayOfTheWeekForSession
      // currentDateWithSessionStartHourAndMinute = momentTimeZone(
      //   currentDateWithSessionStartHourAndMinute
      // ).day(dayOfTheWeekForSession + 1);

      //clone current date and set hour and minute from session end date
      let currentDateWithSessionEndHourAndMinute = momentTimeZone(
        currentDate
      ).set({ hour: sessionEndHour, minute: sessionEndMinute });

      //set day of the week to dayOfTheWeekForSession
      // currentDateWithSessionEndHourAndMinute = momentTimeZone(
      //   currentDateWithSessionEndHourAndMinute
      // ).tz("Europe/Paris").day(dayOfTheWeekForSession + 1);

      let oneSessionObject = {
        startDate: currentDateWithSessionStartHourAndMinute.format(),
        endDate: currentDateWithSessionEndHourAndMinute.format(),
        dayOfTheWeek: dayOfTheWeekForSession,
      };

      //check if current date is public holiday and allowing tutoring
      const isPublicHolidayAndAllowingTutoring =
        await publicHolidaysHelper.isPublicHolidayAndAllowingTutoring(
          oneSessionObject.startDate,
          sessionObject.scheduleDuringHolidays
        );

      //check public holiday per School Zone
      const tutorUserId = sessionObject.tutors[0].userId;
      const isPublicHolidayAndAllowingTutoringPerSchoolZone =
        await holidaysZoneHelper.isPublicHolidayAndAllowingTutoringPerSchoolZone(
          tutorUserId,
          oneSessionObject.startDate,
          sessionObject.scheduleDuringHolidays
        );

      // public holiday and allowing tutoring and public holiday per school zone
      if (
        isPublicHolidayAndAllowingTutoring &&
        isPublicHolidayAndAllowingTutoringPerSchoolZone
      ) {
        //push current date with session start hour and minute to array of dates
        arrayOfDates.push(oneSessionObject);
      }
      // console.log("----------------------------------------------------------");

      if (recurringType === sessionsConstants.sessionsRecurrence.WEEKLY) {
        //add +1 week to current date
        currentDate = momentTimeZone(currentDate).add(1, "week");
      } else if (
        recurringType === sessionsConstants.sessionsRecurrence.BI_MONTHLY
      ) {
        //add +1 month to current date
        currentDate = momentTimeZone(currentDate).add(2, "week");
      }
    }
    return arrayOfDates;
  } catch (error) {
    console.log("error", error);
  }
};

//#endregion

//#region SESSIONS & REPORT

//get list of past sessions and report.reportId is null
exports.listOfAllPastSessionsWithoutReport = async () => {
  try {
    const currentDateTime = dateTimeHelper.getCurrentDateTimeInParisZone();

    const sessionsDocument = await sessionModel
      .find({
        "sessionDate.endDate": { $lte: currentDateTime },
        "report.reportId": { $exists: false },
      })
      .exec();

    return { status: true, message: "Sessions list", data: sessionsDocument };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get list of past sessions and report.reportId is null
exports.listOfAllSessionsInAnHour = async () => {
  try {
    const currentDateTime = dateTimeHelper.getCurrentDateTimeInParisZone();
    const sessionsDocument = await sessionModel
      .find({
        "sessionDate.startDate": {
          $gte: moment(currentDateTime)
            .clone()
            .utc()
            .add(1, "minutes")
            .toDate(),
          $lte: moment(currentDateTime)
            .clone()
            .utc()
            .add(31, "minutes")
            .toDate(),
        },
        parentSessionId: { $ne: null },
        status: { $ne: sessionsConstants.sessionsStatus.CANCELED },
      })
      .exec();
    return { status: true, message: "Sessions list", data: sessionsDocument };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
exports.listOfAllEndingSessionsInAnHour = async () => {
  try {
    const currentDateTime = dateTimeHelper.getCurrentDateTimeInParisZone();
    //for example the current date is 14h:15M
    //get all sessions that will end between 14h:00M and 14h:15m

    const sessionsDocument = await sessionModel
      .find({
        "sessionDate.endDate": {
          $gte: moment(currentDateTime).subtract(15, "minute").toDate(),
          $lte: moment(currentDateTime).toDate(),
        },
        parentSessionId: { $ne: null },
      })
      .exec();

    return { status: true, message: "Sessions list", data: sessionsDocument };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//delete report from session document by sessionId
exports.deleteReportFromSession = async (sessionId) => {
  try {
    const sessionsDocument = await sessionModel
      .findOneAndUpdate(
        { sessionId: sessionId },
        { $unset: { report: 1 } },
        { new: true }
      )
      .exec();

    if (sessionsDocument) {
      return {
        status: true,
        message: "Report deleted successfully",
        data: sessionsDocument,
      };
    } else {
      return { status: false, message: "Report not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//update session report
exports.updateSessionReport = async (userId, sessionId, reportObject) => {
  let user;
  try {
    const sessionDocument = await sessionModel
      .findOne({ sessionId: sessionId })
      .exec();

    if (sessionDocument) {
      //check if report is already exist then update it
      /* if (sessionDocument.report) {
                    sessionDocument.report.reportId = reportObject._id;
                    sessionDocument.report.status = reportObject.status;
               } else {
                    //create new report object
                    sessionDocument.report = reportObject;
               } */
      let userRole = null;

      const isAdminUser = await userAdminHelper.getUserAdministrationByUserId(
        userId
      );

      if (isAdminUser.status && isAdminUser.data.userData) {
        user = isAdminUser;
        userRole = user.data.userData.userRole;
      } else {
        console.log("userRole", userRole);
        user = await userHelper.getUserDetailsByUserId(userId);
        userRole = user?.userRole;
      }

      if (userRole === "tutor") {
        const listOfTutors = sessionDocument.tutors;
        for (let tutor of listOfTutors) {
          if (!tutor?.userId) {
            continue;
          }

          const tutorIndex = sessionDocument.tutors.findIndex(
            (item) => String(item.userId) === String(userId)
          );
          if (tutorIndex !== -1) {
            sessionDocument.tutors[tutorIndex].report.reportId =
              reportObject._id;
            sessionDocument.tutors[tutorIndex].report.status =
              reportObject.status;
          }
        }
      }

      if (userRole === "vsc") {
        const listOfVSC = sessionDocument.vsc;
        for (let vsc of listOfVSC) {
          if (!vsc?.userId) {
            continue;
          }

          const vscIndex = sessionDocument.vsc.findIndex(
            (item) => String(item.userId) === String(userId)
          );
          if (vscIndex !== -1) {
            sessionDocument.vsc[vscIndex].report.reportId = reportObject._id;
            sessionDocument.vsc[vscIndex].report.status = reportObject.status;
          }
        }
      }

      if (userRole !== "vsc" && userRole !== "tutor") {
        const listOfTutors = sessionDocument.tutors;
        for (let [idx, tutor] of listOfTutors.entries()) {
          if (!tutor?.userId) {
            continue;
          }

          sessionDocument.tutors[idx].report.reportId = reportObject._id;
          sessionDocument.tutors[idx].report.status = reportObject.status;
        }
        const listOfVSC = sessionDocument.vsc;
        for (let [idx, vsc] of listOfVSC.entries()) {
          if (!vsc?.userId) {
            continue;
          }

          sessionDocument.vsc[vscIndex].report.reportId = reportObject._id;
          sessionDocument.vsc[vscIndex].report.status = reportObject.status;
        }
      }

      //update student absence
      const listOfStudents = reportObject.students;
      for (let i = 0; i < listOfStudents.length; i++) {
        const studentItem = listOfStudents[i];
        const studentId = studentItem?.userId;

        if (!studentId) {
          continue;
        }
        //update student absence in sessionDocument
        const studentIndex = sessionDocument.students.findIndex(
          (item) => item.userId === studentId
        );

        if (studentIndex !== -1) {
          sessionDocument.students[studentIndex].absence = studentItem.absence;
        }
      }

      const updatedSessionDocument = await sessionDocument.save();

      if (updatedSessionDocument) {
        return {
          status: true,
          message: "Session updated successfully",
          data: updatedSessionDocument.report,
        };
      } else {
        return { status: false, message: "Session not updated", data: null };
      }
    } else {
      return { status: false, message: "Session not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
exports.listOfAllSessionsToday = async () => {
  try {
    const currentDateTime = dateTimeHelper.getCurrentDateTimeInParisZone();
    const sessionsDocument = await sessionModel
      .find({
        "sessionDate.startDate": {
          $gte: moment(currentDateTime).startOf("day"),
          $lte: moment(currentDateTime).endOf("day"),
        },
        parentSessionId: { $ne: null },
        $or: [
          { parentSessionId: { $ne: null } },
          { "recurrence.recurrenceName": { $ne: "one-time" } },
        ],
      })
      .exec();
    if (sessionsDocument.length > 0) {
      return { status: true, message: "Sessions list", data: sessionsDocument };
    } else {
      return { status: false, message: "no session available", data: null };
    }
  } catch (error) {
    return { status: false, message: error.message, data: null };
  }
};
exports.getRecieversMail = async () => {
  try {
    // Fetch all mailings in one go with a single query
    const allMailings = await mailingModel.find({}).exec();
    return {
      status: true,
      message: "Data retrieved successfully",
      data: allMailings,
    };
  } catch (error) {
    return { status: false, message: error.message, data: null };
  }
};
exports.createDynamicTemplateData = (session) => {
  const { tutors, students, sessionDate, createdBy, report } = session;
  const sessionStartDate = moment(sessionDate.startDate).format("DD MMMM YYYY");
  const sessionEndDate = moment(sessionDate.endDate).format("DD MMMM YYYY");
  const startHour = moment(sessionDate.startDate).format("HH:mm");
  const endHour = moment(sessionDate.endDate).format("HH:mm");
  const tutor = tutors[0]["fullName"];
  const student = students[0]["fullName"];
  const tutorAttendance = tutors[0]["absence"] ? "Absent" : "Présent";
  const studentPresence = students.filter((student) => !student.absence);
  const attendanceCount = studentPresence.length;
  let studentCount = 0;
  studentCount = students?.length;
  let studentAttendance;
  if (attendanceCount == 1) {
    studentAttendance = tutors[0]["absence"] ? "Absent" : "Présent";
  } else if (attendanceCount > 1) {
    studentAttendance = `${attendanceCount}/${studentCount} présent(s)`;
  } else {
    studentAttendance = "Absent";
  }
  const created = createdBy.fullName;
  const reported =
    !report.reportLink && !report.description ? "Non saisi" : "Saisi";
  return {
    sessionStartDate,
    sessionEndDate,
    startHour,
    endHour,
    tutor,
    student,
    tutorAttendance,
    studentAttendance,
    created,
    reported,
    attendanceCount,
    studentCount,
  };
};
function isGoogleMeetLink(url) {
  if (url) {
    const googleMeetRegex =
      /^(https?:\/\/)?(www\.)?(meet\.google\.com|google\.com\/meet)\/.+$/;
    return googleMeetRegex.test(url);
  }
}

// For more detail you can visit https://developers.google.com/calendar/api/v3/reference/events
exports.createMeetEvent = async () => {
  try {
    const requestId = Math.floor(Math.random() * 100000);

    // Making auth object to tell that we are authenticated and for authentication purpose we are using info from service-accoun.json file
    const auth = new JWT(
      credential.client_email,
      null,
      credential.private_key,
      ["https://www.googleapis.com/auth/calendar"],
      "<EMAIL>", // You google admin account email will come here.... / also you can use sub accounts of your workspace account
      credential.client_id
    );
    const calendar = new Calender({ version: "v3", auth });

    const event = {
      summary: "Appointment",
      description: "Meeting with client",
      //   Start time for meeting
      start: {
        dateTime: "2024-07-19T12:00:00",
        timeZone: "Europe/Paris",
      },
      //   End time for meeting
      end: {
        dateTime: "2024-07-19T12:30:00",
        timeZone: "Europe/Paris",
      },
      //   conferenceData is  the object inwhich we specify all meeting link related things.
      conferenceData: {
        createRequest: {
          requestId,
          conferenceSolutionKey: {
            type: "hangoutsMeet", // Type can be amon these eventHangout,eventNamedHangout,hangoutsMeet,addOn
            // if you want to create google meet conference you must be Setting Up the Domain-Wide Delegation
          },
        },
      },
      //   Participants who can join.
      // attendees: [
      //   { email: "<EMAIL>" },
      //   { email: "<EMAIL>" },
      // ],
      visibility: "public", // Make sure the event itself is public
      guestsCanInviteOthers: true, // Allow guests to invite others
      guestsCanModify: false, // Guests can't modify the event
      guestsCanSeeOtherGuests: true, // Guests can see other attendees
    };

    // Inserting event this will also add event to attendees calender as we are using google admin account email and service-account.json
    const response = await calendar.events.insert({
      calendarId: "primary",
      resource: event,
      conferenceDataVersion: 1,
    });

    return response.data.hangoutLink;
  } catch (error) {
    console.log(error);
  }
};

exports.updateMatchingTuntorStudent = async (userId, sessionData) => {
  console.log("updateMatchingTuntorStudent in progress", userId, sessionData);
  await tutorPreferencesModel.updateOne(
    { userId: userId },
    {
      $set: {
        status: tutorConstant.statusOfTutors.TUTOR_ACTIVE,
      },
      $push: {
        matchedStudents: { $each: sessionData.students },
      },
    }
  );
  const bulkOps = sessionData.students.map((student) => {
    return {
      updateOne: {
        filter: { userId: student.userId },
        update: {
          $set: {
            matchedTutors: sessionData.tutors,
          },
        },
        upsert: false, // Si vous ne voulez pas insérer si l'élément n'existe pas
      },
    };
  });

  // Exécutez toutes les opérations en une seule fois
  await studentPreferencesModel.bulkWrite(bulkOps);
};
exports.updateMatchingPreferences = async (sessionsDocument) => {
  logger.newLog(logger.getLoggerTypeConstants().sessions).info({
    message: `updateMatchingPreferences progressing`,
    request: {
      requestBody: sessionsDocument,
    },
  });
  console.log("sessionsDocument", sessionsDocument);
  const currentDate = dateTimeHelper.getCurrentDateTimeInParisZone();
  const OtherSession = await sessionModel.findOne({
    parentSessionId: { $ne: null },
    "sessionDate.startDate": { $gte: currentDate },
    sessionId: { $ne: sessionsDocument?.sessionId },
    status: { $nin: sessionsConstants.excludedStatuses },
    "tutors.userId": sessionsDocument?.tutors[0]?.userId,
    students: {
      $elemMatch: {
        userId: {
          $in: sessionsDocument.students.map((student) => student.userId),
        },
      },
    },
  });
  if (!OtherSession) {
    logger.newLog(logger.getLoggerTypeConstants().sessions).info({
      message: `updateMatchingPreferences `,
      request: {
        requestBody: "Other sessions not found",
      },
    });
    await tutorPreferencesModel.findOneAndUpdate(
      {
        userId: sessionsDocument.tutors[0].userId,
      },
      {
        $pull: {
          matchedStudents: {
            userId: {
              $in: sessionsDocument.students.map((student) => student.userId),
            },
          },
        },
      }
    );
    //delete tutor from student matchedTutors array
    await studentPreferencesModel.findOneAndUpdate(
      {
        userId: sessionsDocument.students[0].userId,
      },
      {
        $pull: {
          matchedTutors: { userId: sessionsDocument.tutors[0].userId },
        },
      }
    );
  } else {
    logger.newLog(logger.getLoggerTypeConstants().sessions).info({
      message: `updateMatchingPreferences `,
      request: {
        requestBody: "Other sessions found",
      },
    });
    const updates = sessionsDocument.students.map((student) => {
      return tutorPreferencesModel.updateOne(
        {
          userId: sessionsDocument.tutors[0].userId,
          "matchedStudents.userId": { $ne: student.userId },
        },
        {
          $push: {
            matchedStudents: {
              userId: student.userId,
              fullName: student.fullName,
              absence: false,
            },
          },
        }
      );
    });

    await Promise.all(updates);
    //delete tutor from student matchedTutors array
    await studentPreferencesModel.findOneAndUpdate(
      {
        userId: sessionsDocument.students[0].userId,
      },
      {
        $set: {
          matchedTutors: sessionsDocument.tutors.map((tutor) => {
            return {
              userId: tutor.userId,
              fullName: tutor.fullName,
              absence: false,
            };
          }),
        },
      }
    );
  }
};

/**
 * Calcule les statistiques des séances selon le rôle de l'utilisateur
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userId - L'ID de l'utilisateur connecté
 * @param {string} payload.userRole - Le rôle de l'utilisateur connecté
 * @param {string} [payload.coordinatorId] - ID optionnel du coordinateur pour lequel calculer les stats
 * @returns {Object} - Les statistiques calculées
 */
exports.getSessionsStats = async (payload) => {
  try {
    const { userId, userRole, coordinatorId } = payload;

    // Vérifier si l'utilisateur a les droits nécessaires
    if (!["vsc", "coordinator", "super-administrator"].includes(userRole)) {
      return {
        status: false,
        message: "Vous n'avez pas les droits nécessaires pour accéder à ces statistiques",
        data: null,
      };
    }

    const isCoordinatorOrVsc = [
      userRoleConstants.COORDINATOR,
      userRoleConstants.VSC,
    ].includes(userRole);

    let studentFilter = {};

    // Même logique que sessionsDashboard pour coordinatorId et rôles
    const targetUserId = coordinatorId || userId;
    const userAdminDetails = await UserAdminsModel.findOne({ userId: targetUserId });
    
    if (!userAdminDetails) {
      return {
        status: false,
        message: "Coordinateur non trouvé",
        data: null,
      };
    }

    if (isCoordinatorOrVsc || coordinatorId) {
      const pref = userAdminDetails.administrationPreferences;
      
      if (!pref || !pref.program?.length) {
        return {
          status: true,
          message: "Aucune statistique disponible",
          data: {
            canceledSessions: 0,
            completedSessions: 0
          }
        };
      }

      studentFilter = {
        "program.programId": { $in: pref.program.map(p => p.programId) },
        "assignment.department.departmentId": { $in: pref.department.map(d => d.departmentId) }
      };
    }

    // Récupérer les IDs des étudiants selon le filtre
    const studentIds = await StudentPreferencesModel.find(studentFilter)
      .select("userId")
      .lean()
      .then(students => students.map(student => student.userId));

    if (!studentIds.length) {
      return {
        status: true,
        message: "Aucune statistique disponible",
        data: {
          canceledSessions: 0,
          completedSessions: 0
        }
      };
    }

    // Calculer les statistiques
    const stats = await SessionsModel.aggregate([
      {
        $match: {
          "students.userId": { $in: studentIds }
        }
      },
      {
        $group: {
          _id: "$status",
          count: { $sum: 1 }
        }
      }
    ]);

    // Formater les résultats
    const result = {
      canceledSessions: 0,
      completedSessions: 0
    };

    stats.forEach(stat => {
      if (stat._id === "canceled") {
        result.canceledSessions = stat.count;
      } else if (stat._id === "session-0-to-be-scheduled") {
        result.completedSessions = stat.count;
      }
    });

    return {
      status: true,
      message: "Statistiques récupérées avec succès",
      data: result
    };

  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de la récupération des statistiques",
      data: error
    };
  }
};
