{"info": {"name": "ZupdeCo API Collection", "description": "Collection of Express.js routes for testing in Postman", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "/save", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/save", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "save"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/availability", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/availability", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "availability"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/availabilities", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/availabilities", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "availabilities"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/availability", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/availability", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "availability"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/availability", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/availability", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "availability"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/webhooks/createWebhooks", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/webhooks/createWebhooks", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/webhooks/createWebhooks"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/webhooks/deleteWebhook", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/webhooks/deleteWebhook", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/webhooks/deleteWebhook"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/webhooks/hooksList", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/webhooks/hooksList", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/webhooks/hooksList"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/getMeetingInfo", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/getMeetingInfo", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/getMeetingInfo"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/getMeetings", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/getMeetings", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/getMeetings"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/newRoom", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/newRoom", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/newRoom"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/joinUrl", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/joinUrl", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/joinUrl"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/bigBlueButton/getRecordings", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/bigBlueButton/getRecordings", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "bigBlueButton/getRecordings"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/crscreport", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/crscreport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "crscreport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/crscreport", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/crscreport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "crscreport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/crscreport", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/crscreport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "crscreport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/crscreport", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/crscreport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "crscreport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/crscreport/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/crscreport/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "crscreport/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/establishment", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/establishment", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/establishment", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/dashboard/phonebook", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/dashboard/phonebook", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/dashboard/phonebook"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/dashboard/annuaire", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/dashboard/annuaire", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/dashboard/annuaire"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/checkEstablishmentName", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/checkEstablishmentName", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/checkEstablishmentName"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/dashboard/csv", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/dashboard/csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/dashboard/csv"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/autoComplete", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/autoComplete", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/autoComplete"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/establishment/baseOnSectors", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/establishment/baseOnSectors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "establishment/baseOnSectors"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/class/baseOnLevel", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/class/baseOnLevel", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "class/baseOnLevel"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/is", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/tutor/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/is", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/tutor/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/is/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/is/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/is/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/is", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/is", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/tutor/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/is", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/parent/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/update-pass", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/parent/update-pass", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/update-pass"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/update-pass", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/tutor/update-pass", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/update-pass"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/is", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/parent/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/is/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/is/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/is/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/is", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/is", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/parent/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/is", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/student/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/is", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/student/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/is/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/is/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/is/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/is", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/is", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/student/is", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/is"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/externalUser/acceptInvitation", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/externalUser/acceptInvitation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "externalUser/acceptInvitation"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/externalUser/register", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/externalUser/register", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "externalUser/register"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/testHardware", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/testHardware", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "testHardware"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/testHardware", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/testHardware", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "testHardware"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/level", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/level", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "level"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/levels", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/levels", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "levels"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/level", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/level", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "level"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/level", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/level", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "level"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/tutor/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/tutor/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/tutor/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/tutor/students-matched", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/tutor/students-matched", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/tutor/students-matched"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/tutor/students-unmatched", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/tutor/students-unmatched", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/tutor/students-unmatched"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/student/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/student/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/student/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/student/tutor-matched", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/student/tutor-matched", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/student/tutor-matched"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/matching/student/tutors-unmatched", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/matching/student/tutors-unmatched", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "matching/student/tutors-unmatched"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/maxicours", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/maxicours", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "maxicours"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/maxicours/decode", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/maxicours/decode", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "maxicours/decode"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/parent", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/user/parent", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/parent"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/parent/last-step", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/user/parent/last-step", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/parent/last-step"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/parent/child", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/myspace", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/parent/myspace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/myspace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/myspace", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/myspace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/myspace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/child", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child/details", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/child/details", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child/details"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/parent/child", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/parent/child", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/send-invitation", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/parent/send-invitation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/send-invitation"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/child/sessions", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/parent/child/sessions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/child/sessions"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/parent/testHardware", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/parent/testHardware", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "parent/testHardware"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/publicHoliday", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/publicHoliday", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "publicHoliday"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/publicHolidays", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/publicHolidays", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "publicHolidays"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/publicHoliday", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/publicHoliday", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "publicHoliday"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/publicHoliday", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/publicHoliday", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "publicHoliday"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/publicHoliday", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/publicHoliday", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "publicHoliday"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scReport", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/scReport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scReport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scReport", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/scReport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scReport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scReport", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/scReport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scReport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scReport", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/scReport", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scReport"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scReport/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/scReport/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scReport/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scholarYear", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/scholarYear", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scholar<PERSON>ear"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scholarYear/all", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/scholarYear/all", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scholarYear/all"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scholarYear", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/scholarYear", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scholar<PERSON>ear"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scholarYear", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/scholarYear", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scholar<PERSON>ear"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/scholarYear", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/scholarYear", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "scholar<PERSON>ear"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/schoolZone", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/schoolZone", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "schoolZone"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/schoolZones", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/schoolZones", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "schoolZones"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/schoolZone", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/schoolZone", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "schoolZone"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/schoolZone", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/schoolZone", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "schoolZone"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/schoolZone", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/schoolZone", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "schoolZone"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector/dashboard/csv", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector/dashboard/csv", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector/dashboard/csv"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/sector", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/gov/sectors", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/gov/sectors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "gov/sectors"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sectors", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sectors", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sectors"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/sector", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/sector", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector/autoComplete", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector/autoComplete", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector/autoComplete"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector/baseOnEstablishments", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector/baseOnEstablishments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector/baseOnEstablishments"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sector/basedOnDepartments", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sector/basedOnDepartments", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sector/basedOnDepartments"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sessions/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/sessions/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sessions/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/session", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/sessions/sessions-zero", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/sessions/sessions-zero", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "sessions/sessions-zero"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/session", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/session", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session/batchDelete", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/session/batchDelete", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session/batchDelete"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session/join-url", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/session/join-url", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session/join-url"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/session/test", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/session/test", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "session/test"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/specialties-level", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/specialties-level", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "specialties-level"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/student", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/user/student", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/student"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/accept", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/student/accept", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/accept"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/myspace", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/student/myspace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/myspace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/myspace", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/myspace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/myspace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/detail", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/detail", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/detail"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/session", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/session", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/session"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/student/search", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/student/search", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "student/search"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/tutor", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/user/tutor", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/tutor"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/tutor/last-step", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/user/tutor/last-step", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/tutor/last-step"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/onboarding", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/tutor/onboarding", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/onboarding"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/onboarding", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/onboarding", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/onboarding"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/documents", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/tutor/documents", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/documents"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/documents", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/documents", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/documents"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/documents", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/tutor/documents", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/documents"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/detail", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/detail", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/detail"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/myStudents", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/myStudents", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/myStudents"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/mySessions", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/mySessions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/mySessions"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/mySpace", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/mySpace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/mySpace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/mySpace", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/tutor/mySpace", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/mySpace"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/search", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/search", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/search"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/reports/dashboard", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/tutor/reports/dashboard", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/reports/dashboard"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/tutor/save-dates", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/tutor/save-dates", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "tutor/save-dates"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministrations", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/userAdministrations", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministrations"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministrations/stats", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/userAdministrations/stats", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministrations/stats"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/userAdministration", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration", "request": {"method": "put", "url": {"raw": "http://localhost:3000/api/v1/userAdministration", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/userAdministration", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration", "request": {"method": "delete", "url": {"raw": "http://localhost:3000/api/v1/userAdministration", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministrations/search", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/userAdministrations/search", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministrations/search"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/admin/signIn", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/user/admin/signIn", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/admin/signIn"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration/sendInvitationEmail", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/userAdministration/sendInvitationEmail", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration/sendInvitationEmail"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration/acceptInvitation", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/userAdministration/acceptInvitation", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration/acceptInvitation"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userAdministration/register", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/userAdministration/register", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userAdministration/register"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/twilio/sms", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/twilio/sms", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "twilio/sms"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/user", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/signIn", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/user/signIn", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/signIn"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/resetPassword", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/user/resetPassword", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/resetPassword"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/emailVerification", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/user/emailVerification", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/emailVerification"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/savePhoneNumber", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/user/savePhoneNumber", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/savePhoneNumber"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/checkIfTheUserExistsInDB", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/user/checkIfTheUserExistsInDB", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/checkIfTheUserExistsInDB"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/user/emailVerificationCode", "request": {"method": "get", "url": {"raw": "http://localhost:3000/api/v1/user/emailVerificationCode", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "user/emailVerificationCode"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/userRole", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/userRole", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "userRole"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}, {"name": "/contactZupDeco", "request": {"method": "post", "url": {"raw": "http://localhost:3000/api/v1/contactZupDeco", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "v1", "contactZupDeco"]}, "header": [{"key": "Content-Type", "value": "application/json"}], "body": {}, "description": ""}, "response": []}]}