exports.getTutorsCountFromPreferences = (filters) => [
  {
    $match: {
      userRole: "tutor",
    },
  },
  {
    $group: {
      _id: "$userRole",
      count: {
        $sum: 1,
      },
      userId: {
        $push: "$userId",
      },
    },
  },
  {
    $unwind: {
      path: "$userId",
    },
  },
  {
    $project: {
      _id: 0,
      count: 0,
    },
  },
  {
    $lookup: {
      from: "tutorpreferences",
      let: { userId: "$userId" },
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ["$userId", "$$userId"],
            },
          },
        },
        {
          // Additional match criteria if needed
          $match: filters || {},
        },
      ],
      as: "tutorpreferences",
    },
  },
  {
    $match: {
      $expr: {
        $gt: [{ $size: "$tutorpreferences" }, 0],
      },
    },
  },
  {
    $group: {
      _id: "null",
      tutorWithPreferences: {
        $sum: 1,
      },
    },
  },
];
