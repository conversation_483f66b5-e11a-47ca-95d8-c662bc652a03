const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("user.administration.routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const userRoleConstants = require("../utils/constants/userRolesConstants.js");

//import userHelper.js from path src/controllers/user/user.Helper.js
const userHelper = require("../controllers/user/User.Helper.js");

const tutorHelper = require("../controllers/tutor/tutor.helper.js");

const parentHelper = require("../controllers/parent/parent.helper.js");

const userAdministrationHelper = require("../controllers/user/user.administration.helper.js");
const statsHelper = require("../controllers/user/stats.helper.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const userAdministrationModel = require("../models/User.Administration.Model.js");
const userModel = require("../models/User.Models.js");

const sectorsModel = require("../models/Sectors.Model.js");

const sendGrid = require("@sendgrid/mail");

const appConstants = require("../utils/constants/app.constants.js");

const sendGridHelper = require("../middleware/mailServiceSendGrid/EmailHelper.js");
const userConst = require("../utils/constants/user.const.js");

const userRoleStatusConstants = require("../utils/constants/user.role.status.constants.js");

const userRoleTranslated = require("../utils/constants/user.roles.translated.js");
const programConstants = require("../utils/constants/program.constants.js");
const SectorModel = require("../models/Sectors.Model.js");
const jwt = require("jsonwebtoken");
const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  SUPER_ADMINISTRATOR,
  VSC,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");
const UserAdministrationModel = require("../models/User.Administration.Model.js");
const EstablishmentModel = require("../models/Establishment.Model.js");
const EducationAnnuaireModel = require("../models/EducationAnnuaire.Model.js");
const { ObjectId } = require("mongodb");
const e = require("express");

// router.use((req, res, next) => {
//      verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.USER_ADMINISTRATION);
// });

//get all user administration with userRole super_administrator
/**
 * @swagger
 * /api/v1/userAdministrations:
 *   get:
 *     summary: Retrieve user administrations
 *     description: Retrieve a list of user administrations based on provided filters and pagination options.
 *     tags:
 *       - User Administrations
 *     parameters:
 *       - in: query
 *         name: userRole
 *         required: true
 *         description: Role of the user.
 *         schema:
 *           type: string
 *       - in: query
 *         name: pageSize
 *         required: false
 *         description: Number of items per page.
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         required: false
 *         description: Page number.
 *         schema:
 *           type: integer
 *       - in: query
 *         name: filter
 *         required: false
 *         description: Filter criteria.
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         required: false
 *         description: Sort criteria.
 *         schema:
 *           type: string
 *       - in: query
 *         name: exportCsv
 *         required: false
 *         description: Export as CSV.
 *         schema:
 *           type: boolean
 *       - in: query
 *         name: nameAndUserId
 *         required: false
 *         description: Include name and user ID only.
 *         schema:
 *           type: boolean
 *     responses:
 *       200:
 *         description: User administration list retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administration list retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: 12345
 *                       name:
 *                         type: string
 *                         example: John Doe
 *                       # Additional properties can be included as needed
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalCount:
 *                       type: integer
 *                       example: 100
 *       500:
 *         description: Error retrieving user administration list
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error retrieving user administration list
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/userAdministrations",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      let userRole = req.query.userRole;
      let pageSize = apiResponse.buildPageSize(req.query.pageSize);
      let page = apiResponse.buildPage(req.query.page);
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const exportCsv = req.query.exportCsv === "true";
      const nameAndUserId = req.query.nameAndUserId === "true";

      const reqUserRole = req.userRole;
      const userId = req.userId;

      if (exportCsv) {
        pageSize = 10000000;
        page = 1;
      } else if (nameAndUserId) {
        pageSize = 10000000;
        page = 1;
      }
      const userList =
        await userAdministrationHelper.getAllAdminUsersByUserRole({
          page,
          pageSize,
          filter,
          sortBy,
          userRole,
          userId,
          reqUserRole,
        });

      let listOfAdmins = [];

      if (nameAndUserId) {
        listOfAdmins = userList.data.map((user) => {
          return {
            userId: user.userId,
            name: user.firstName + " " + user.lastName,
          };
        });
      } else {
        listOfAdmins = userList.data;
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `User administration list retrieved successfully`,
        listOfAdmins,
        page,
        pageSize,
        userList.totalCount
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Error retrieving user administration list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

// get count of all user
/**
 * @swagger
 * /api/v1/userAdministrations/stats:
 *   get:
 *     tags:
 *       - User Administrations
 *     summary: Retrieve user administration statistics
 *     description: Retrieve statistics of user administrations.
 *     responses:
 *       200:
 *         description: User administration statistics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administration list retrieved successfully
 *                 data:
 *                   type: object
 *                   description: Statistics of user administrations
 *                   properties:
 *                     totalAdmins:
 *                       type: integer
 *                       example: 42
 *                     activeAdmins:
 *                       type: integer
 *                       example: 40
 *                     inactiveAdmins:
 *                       type: integer
 *                       example: 2
 *       500:
 *         description: Error retrieving user administration statistics
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error retrieving user administration statistics
 *                 error:
 *                   type: string
 *                   example: Error details
 */
router.get(
  "/userAdministrations/stats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const total = await userAdministrationHelper.getAdminStats({
        userId,
        userRole,
      });
      console.log("FINAL TOTAL: ", total);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `User administration list retrieved successfully`,
        total
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Error retrieving user administration list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

// get bimonelIst
/**
 * @swagger
 * /BinimesList:
 *   get:
 *     summary: Retrieve list of tutor-student pairs (binomes)
 *     description: |
 *       This endpoint retrieves the list of tutor-student pairs (binomes) based on the connected user's role and ID.
 *       Access is restricted to super administrators, VSCs, coordinators, and managers.
 *     tags:
 *       - Binomes
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     responses:
 *       200:
 *         description: Binome list successfully retrieved
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [success]
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Binome list retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       tutor:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: zu-1826953c-0523-4728-af87-360fe614064d
 *                           fullName:
 *                             type: string
 *                             example: Tutor Name
 *                       student:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: zu-1a8f0dfa-9dad-4c11-86ad-ec7bb8dd0f98
 *                           fullName:
 *                             type: string
 *                             example: Student Name
 *                       sessions:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             sessionId:
 *                               type: string
 *                               example: ss-2-0173956659
 *                             _id:
 *                               type: object
 *                               properties:
 *                                 $oid:
 *                                   type: string
 *                                   example: 67afae31d586dcdf3eb9fca5
 *                             date:
 *                               type: string
 *                               format: date-time
 *                               example: 2025-02-17T15:00:00.000Z
 *                             isFuture:
 *                               type: boolean
 *                               example: true
 *                       status:
 *                         type: string
 *                         enum: [active, inactive]
 *                         example: active
 *                         description: Status of the binome - active if there's at least one future session with status 'session-0-to-be-scheduled'
 *       401:
 *         description: Unauthorized - Missing or invalid authentication
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [error]
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Authentication required
 *       403:
 *         description: Forbidden - User doesn't have required permissions
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [error]
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Insufficient permissions
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [error]
 *                   example: error
 *                 message:
 *                   type: string
 *                   example: Error retrieving Binome list
 *                 data:
 *                   type: object
 *                   description: Error details
 */
router.get(
  "/BinimesList",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const total = await userAdministrationHelper.getBinomesList({
        userId,
        userRole,
      });
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Binome list retrieved successfully`,
        total
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Error retrieving Binome list`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//add new user administration
/**
 * @swagger
 * /api/v1/userAdministration:
 *   post:
 *     tags:
 *       - User Administrations
 *     summary: Add new user administration
 *     description: Add a new user administration and send an invitation email.
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userAdministrationData:
 *                 type: string
 *                 description: JSON string of user administration data
 *                 example: '{"email": "<EMAIL>", "contactDetails": {"firstName": "John", "lastName": "Doe", "email": "<EMAIL>"}, "userRole": "admin"}'
 *     responses:
 *       200:
 *         description: New user administration added successfully. Please notify the user to check email for the invitation link.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "New user administration added successfully. Please notify the user to check email for the invitation link."
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: "12345"
 *                     contactDetails:
 *                       type: object
 *                       properties:
 *                         firstName:
 *                           type: string
 *                           example: "John"
 *                         lastName:
 *                           type: string
 *                           example: "Doe"
 *                         email:
 *                           type: string
 *                           example: "<EMAIL>"
 *                     userRole:
 *                       type: string
 *                       example: "admin"
 *       400:
 *         description: Email address already registered. Please provide another email address.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Email address already registered. Please provide another email address."
 *                 error:
 *                   type: string
 *                   example: "EMAIL_IS_ALREADY_REGISTERED"
 *       500:
 *         description: Error adding new user administration.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error adding new user administration."
 *                 error:
 *                   type: object
 *                   example: {}
 */
router.post(
  "/userAdministration",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res, next) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        let userAdministrationData = fields.userAdministrationData;
        //check if userAdministrationData is empty or undefined or null
        if (!userAdministrationData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User administration data is empty or undefined or null`,
            `Please provide user administration data`
          );
          return res.status(500).json(response);
        }

        userAdministrationData = JSON.parse(userAdministrationData);
        const email = userAdministrationData.email;

        //check if email address is already registered
        const ifEmailExists =
          await userAdministrationHelper.checkIfEmailIsAlreadyRegistered(email);

        if (ifEmailExists.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Email address already registered`,
            `Please provide another email address`,
            apiResponse.apiConstants.EMAIL_IS_ALREADY_REGISTERED
          );
          return res.status(200).json(response);
        }

        const newUserAdministration =
          await userAdministrationHelper.addNewUserAdministration(
            userAdministrationData
          );
        // if the user is in Tutorat solidaire and is a coordinator  add the user to the coordinators array of the selected establishments
        if (
          newUserAdministration.status &&
          newUserAdministration.data.userRole ===
            userRoleConstants.COORDINATOR &&
          newUserAdministration?.data?.administrationPreferences?.program[0][
            "programId"
          ] === "az-fac-5f87232"
        ) {
          const establishments =
            newUserAdministration?.data?.administrationPreferences
              ?.establishment;
          if (establishments && establishments.length > 0) {
            for (let establishment of establishments) {
              await EducationAnnuaireModel.findOneAndUpdate(
                {
                  _id: ObjectId(establishment.establishmentId),
                },
                {
                  coordinator: {
                    userId: newUserAdministration.data.userId,
                    fullName:
                      newUserAdministration.data.contactDetails.firstName +
                      " " +
                      newUserAdministration.data.contactDetails.lastName,
                  },
                }
              );
            }
          }
        }
        if (newUserAdministration.status) {
          this.sendInvitationEmail(newUserAdministration.data, true);

          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `New user administration added successfully.Please notify the user to check email for the invitation link`,
            newUserAdministration.data
          );

          // logger
          //   .newLog(logger.getLoggerTypeConstants().admin)
          //   .info(
          //     `New user administration added successfully: fullName ${
          //       newUserAdministration.data.contactDetails.firstName +
          //       " " +
          //       newUserAdministration.data.contactDetails.lastName
          //     } userId: ${newUserAdministration.data.userId}, email: ${
          //       newUserAdministration.data.contactDetails.email
          //     },userRole: ${newUserAdministration.data.userRole}`
          //   );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error adding new user administration`,
            newUserAdministration.error
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error adding new user administration`,
          error
        );
        return res.status(500).json(response);
      }
    });
  }
);

// update user administration password
/**
 * @swagger
 * /api/v1/userAdministration/change-password:
 *   patch:
 *     tags:
 *       - User Administrations
 *     summary: Update user administration password
 *     description: Update the password of a user administration.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: ID of the user administration.
 *                 example: "12345"
 *               password:
 *                 type: string
 *                 description: New password for the user administration.
 *                 example: "newPassword123"
 *     responses:
 *       200:
 *         description: User administration password updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User administration password updated successfully."
 *       400:
 *         description: Invalid user administration ID or password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Invalid user administration ID or password."
 *       500:
 *         description: Error updating user administration password.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Error updating user administration password."
 *                 error:
 *                   type: object
 *                   example: {}
 */
router.patch(
  "/userAdministration/change-password",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "UserId is required, please provide a valid userId",
          400
        );
        return res.status(400).json(response);
      }
      const user = await userHelper.getUserDetailsByUserId(userId);
      if (!user || !user.contactDetails.email) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found: ${userId}, please provide a valid userId`,
          400
        );
        return res.status(400).json(response);
      }
      const { password } = req.body;
      if (!password) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New password is empty`,
          400
        );
        return res.status(400).json(response);
      }

      const userRecord = await firebaseHelper.changePassword(
        user.contactDetails.email,
        password
      );

      if (!userRecord) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating password`,
          400
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Account password successfully updated`
      );
      return res.status(200).json(response);
    } catch (error) {
      let errorMessage = `Error while updating password: ${error}`;
      console.log(errorMessage);
      let response = apiResponse.response(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Error while getting tutor preferences",
        errorMessage
      );
      return res.status(500).json(response);
    }
  }
);

//update user administration
/**
 * @swagger
 * /api/v1/userAdministration:
 *   put:
 *     summary: Update user administration
 *     description: Update the user administration data based on provided user details.
 *     tags:
 *       - User Administration
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userAdministrationData:
 *                 type: string
 *                 description: JSON string containing user administration data.
 *                 example: '{"userId": "12345", "email": "<EMAIL>", "firstName": "John", "lastName": "Doe"}'
 *     responses:
 *       200:
 *         description: User administration updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administration updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: 12345
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     firstName:
 *                       type: string
 *                       example: John
 *                     lastName:
 *                       type: string
 *                       example: Doe
 *       500:
 *         description: Error updating user administration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error updating user administration
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.put(
  "/userAdministration",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res, next) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        let userAdministrationData = fields.userAdministrationData;
        //check if userAdministrationData is empty or undefined or null
        if (!userAdministrationData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User administration data is empty or undefined or null`,
            `Please provide user administration data`
          );
          return res.status(500).json(response);
        }

        userAdministrationData = JSON.parse(userAdministrationData);

        const email = userAdministrationData.email;
        const userId = userAdministrationData.userId;
        if (!userId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User id is empty or undefined or null`,
            `Please provide user id`
          );
          return res.status(500).json(response);
        }

        //check if email address is already registered by another user

        const ifEmailExists =
          await userAdministrationHelper.checkIfEmailIsAlreadyRegisteredInUpdateUser(
            email,
            userId
          );
        if (ifEmailExists.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Email address already registered by another user`,
            `Please provide another email address`,
            apiResponse.apiConstants.EMAIL_IS_ALREADY_REGISTERED
          );
          return res.status(200).json(response);
        }

        const updatedUserAdministration =
          await userAdministrationHelper.updateUserAdministration(
            userAdministrationData
          );
        console.log("updatedUserAdministration", updatedUserAdministration);
        if (
          updatedUserAdministration.status &&
          updatedUserAdministration.data.userRole ===
            userRoleConstants.COORDINATOR &&
          updatedUserAdministration?.data?.administrationPreferences
            ?.program[0]["programId"] === "az-fac-5f87232"
        ) {
          const establishments =
            updatedUserAdministration?.data?.administrationPreferences
              ?.establishment;
          if (establishments && establishments.length > 0) {
            for (let establishment of establishments) {
              await EducationAnnuaireModel.findOneAndUpdate(
                {
                  _id: ObjectId(establishment.establishmentId),
                },
                {
                  coordinator: {
                    userId: updatedUserAdministration.data.userId,
                    fullName:
                      updatedUserAdministration.data.contactDetails.firstName +
                      " " +
                      updatedUserAdministration.data.contactDetails.lastName,
                  },
                }
              );
            }
          }
        }
        if (updatedUserAdministration) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User administration updated successfully`,
            updatedUserAdministration.data
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error updating user administration`,
            updatedUserAdministration.data
          );
          return res.status(500).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error updating user administration`,
          error
        );
        return res.status(500).json(response);
      }
    });
  }
);

//get user administration by id with userAdministration Preference data
/**
 * @swagger
 * /api/v1/userAdministration:
 *   get:
 *     summary: Retrieve user administration by user ID
 *     description: Retrieve user administration details based on provided user ID.
 *     tags:
 *       - User Administration
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user administration to retrieve.
 *         schema:
 *           type: string
 *           example: 12345
 *     responses:
 *       200:
 *         description: User administration retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administration retrieved successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: 12345
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     firstName:
 *                       type: string
 *                       example: John
 *                     lastName:
 *                       type: string
 *                       example: Doe
 *       500:
 *         description: Error retrieving user administration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error retrieving user administration
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/userAdministration",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    try {
      const userAdministrationId = req.query.userId;

      const userAdministration =
        await userAdministrationHelper.getUserAdministrationByUserId(
          userAdministrationId
        );

      if (!userAdministration.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User administration not found`,
          userAdministration.data
        );
        return res.status(500).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `User administration retrieved successfully`,
        userAdministration.data
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving user administration`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
router.get(
  "/userAdministrationRole",
  [verifyApiKey],
  async (req, res, next) => {
    try {
      const userAdministrationId = req.query.userId;

      const userAdministration =
        await userAdministrationHelper.getUserAdministrationByUserId(
          userAdministrationId
        );

      if (!userAdministration.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User administration not found`,
          userAdministration.data
        );
        return res.status(500).json(response);
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `User administration role retrieved successfully`,
        userAdministration?.data?.userData?.userRole
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error retrieving user administration role`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//delete user administration by id
/**
 * @swagger
 * /api/v1/userAdministration:
 *   delete:
 *     summary: Delete user administration by user ID
 *     description: Delete a user administration record based on the provided user ID.
 *     tags:
 *       - User Administration
 *     parameters:
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user administration to delete.
 *         schema:
 *           type: string
 *           example: 12345
 *     responses:
 *       200:
 *         description: User administration deleted successfully or error message
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administration deleted successfully
 *                 data:
 *                   type: object
 *                   example: null
 *                 error:
 *                   type: string
 *                   example: Error message details
 *       500:
 *         description: Error deleting user administration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error deleting user administration
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.delete(
  "/userAdministration",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userAdministrationId = req.query.userId;
      if (!userAdministrationId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User administration id is empty or undefined or null`,
          `Please provide user administration id`
        );
        return res.status(200).json(response);
      }

      const deletedUserAdministration =
        await userAdministrationHelper.deleteUserAdministration(
          userAdministrationId
        );

      if (deletedUserAdministration.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User administration deleted successfully`,
          deletedUserAdministration.data
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error deleting user administration`,
          deletedUserAdministration.data
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting user administration`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//filter user administration by userRole and  firstName or lastName or email
/**
 * @swagger
 * /api/v1/userAdministrations/search:
 *   get:
 *     summary: Search user administrations
 *     description: Retrieve a list of user administrations based on search criteria and user role.
 *     tags:
 *       - User Administrations
 *     parameters:
 *       - in: query
 *         name: search
 *         required: false
 *         description: Search term to filter user administrations by first name or last name.
 *         schema:
 *           type: string
 *       - in: query
 *         name: userRole
 *         required: false
 *         description: Role of the user to filter the search results.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User administrations retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User administrators retrieved successfully
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       firstName:
 *                         type: string
 *                         example: John
 *                       lastName:
 *                         type: string
 *                         example: Doe
 *                       userId:
 *                         type: string
 *                         example: 12345
 *                       userRole:
 *                         type: string
 *                         example: Administrator
 *       400:
 *         description: No user administrations found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: No administrators found
 *                 error:
 *                   type: string
 *                   example: Please try with different search.
 *       500:
 *         description: Error in getting user administrations
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error in getting administrators
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/userAdministrations/search",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    const search = req.query.search;
    const userRole = req.query.userRole;
    const program = req.query.program;
    try {
      let listOfAdmins = [];

      if (search && userRole && program !== "undefined") {
        console.log("1");
        listOfAdmins = await userAdministrationModel
          .find({
            $and: [
              {
                userRole: userRole,
                "administrationPreferences.program.programId": program,
              },

              {
                $or: [
                  {
                    "contactDetails.firstName": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "contactDetails.lastName": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                ],
              },
            ],
          })
          .select(
            "contactDetails.firstName contactDetails.lastName userId userId userRole"
          ) // select only firstName, lastName, and userId fields
          .exec();
      } else if (search && userRole && (!program || program == "undefined")) {
        console.log("2");

        listOfAdmins = await userAdministrationModel
          .find({
            $and: [
              {
                userRole: userRole,
              },
              {
                $or: [
                  {
                    "contactDetails.firstName": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                  {
                    "contactDetails.lastName": {
                      $regex: search,
                      $options: "i",
                    },
                  },
                ],
              },
            ],
          })
          .select(
            "contactDetails.firstName contactDetails.lastName userId userId userRole"
          ) // select only firstName, lastName, and userId fields
          .exec();
      } else if (userRole) {
        console.log("3");

        listOfAdmins = await userAdministrationModel
          .find({ userRole: userRole })
          .select(
            "contactDetails.firstName contactDetails.lastName userId userId userRole"
          ) // select only firstName, lastName, and userId fields
          .exec();
      }
      //  else if (program) {
      //   listOfAdmins = await userAdministrationModel
      //     .find({ "administrationPreferences.program.programId": program })
      //     .select(
      //       "contactDetails.firstName contactDetails.lastName userId userId userRole"
      //     ) // select only firstName, lastName, and userId fields
      //     .exec();
      // }
      else {
        console.log("4");
        console.log("no search or userRole provided");
        //return all admins if no search or userRole is provided, base on userRoleConstants
        listOfAdmins = await userAdministrationModel
          .find({
            $or: [
              { userRole: userRoleConstants.SUPER_ADMINISTRATOR },
              { userRole: userRoleConstants.MANAGER },
              { userRole: userRoleConstants.COORDINATOR },
              { userRole: userRoleConstants.VSC },
            ],
          })
          .select(
            "contactDetails.firstName contactDetails.lastName userId userId userRole"
          ) // select only firstName, lastName, and userId fields
          .exec();
      }
      if (!listOfAdmins) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No ${userRole} found.`,
          "Please try with different search."
        );
        return res.status(400).json(response);
      }

      console.log("length of listOfAdmins", listOfAdmins.length);

      const merged = listOfAdmins.map((admin) => {
        const { firstName, lastName } = admin.contactDetails;
        const userId = admin.userId;
        const userRole = admin.userRole;
        return { firstName, lastName, userId, userRole };
      });

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `${userRole} retrieved successfully`,
        merged
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting ${userRole}  : ${error}`
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/user/admin/signIn:
 *   post:
 *     summary: Sign in user
 *     description: Sign in a user based on the provided identifier.
 *     tags:
 *       - User Authentication
 *     security:
 *       - apiKeyAuth: []
 *     parameters:
 *       - in: formData
 *         name: identifier
 *         required: true
 *         description: Unique identifier of the user.
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: User signed in successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User is found with identifier <identifier>
 *                 data:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                       example: 12345
 *       400:
 *         description: Invalid request or missing identifier
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Identifier is null or empty
 *                 error:
 *                   type: string
 *                   example: Identifier is required, please provide a valid identifier
 *       500:
 *         description: Error signing in user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while signing in user
 *                 error:
 *                   type: string
 *                   example: Error message details
 */

router.post(
  "/user/admin/signIn",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const { identifier, token } = req.body;
      const tokenId = token;
      const verifiedPayload = await userHelper.verifyToken(tokenId);
      //check if identifier is present in the request
      if (
        !identifier ||
        identifier !== verifiedPayload?.firebase?.identities?.email[0]
      ) {
        let response = apiResponse.response(
          false,
          `Identifier is required, please provide a valid identifier`
        );
        return res.status(200).json(response);
      }
      //Check if user is already present in the database
      let userData =
        await userAdministrationHelper.getUserDetailsFromFirebaseIdentifier(
          identifier
        );
      //check if the user is present in the database
      if (userData) {
        //delete from object _id
        delete userData._id;

        //delete administrationPreferences from object
        // userData.administrationPreferences = null;

        if (
          userData.status !== userRoleStatusConstants.userAdminStatus.ACTIVE
        ) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `This user is not active. Please contact admin to activate your account.`,
            `User with email: ${identifier} is not active. Their current role is ${userData.userRole}`,
            apiResponse.apiConstants.USER_IS_NOT_ACTIVE
          );
          return res.status(200).json(response);
        }
        //generate access token
        const token = generateAccessToken(userData.userId);
        const refreshToken = generateRefreshToken(userData.userId);

        let response = apiResponse.response(
          true,
          `User is found with identifier ${identifier}`,
          { ...userData?._doc, token }
        );

        res.cookie("refreshToken", refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
        });
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User is not found with identifier ${identifier}`,
          `Please provide a valid identifier`,
          apiResponse.apiConstants.USER_NOT_FOUND
        );
        return res.status(200).json(response);
      }

      //
    } catch (error) {
      console.log("Error while signing in user", error);
      let response = apiResponse.responseWithStatusCode(
        false,
        `Error while signing in user`,
        error,
        apiResponse.apiConstants.ERROR_WHILE_SIGNING_IN_USER
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/userAdministration/sendInvitationEmail:
 *   post:
 *     summary: Send invitation email to user
 *     description: Send an invitation email to a user based on provided email and user role.
 *     tags:
 *       - User Administration
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               email:
 *                 type: string
 *                 description: Email address of the user.
 *               userRole:
 *                 type: string
 *                 description: Role of the user.
 *     responses:
 *       200:
 *         description: Invitation email sent successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Invitation sent successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     email:
 *                       type: string
 *                       example: <EMAIL>
 *                     invitationCode:
 *                       type: string
 *                       example: ABC123
 *       400:
 *         description: Invalid request or missing email or userRole
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Email is null or empty
 *                 error:
 *                   type: string
 *                   example: Email is required, please provide a valid email
 *       500:
 *         description: Error sending invitation email
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while sending invitation email
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.post(
  "/userAdministration/sendInvitationEmail",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields) => {
      try {
        const { email, userRole } = fields;
        // Check if email is present in the request
        if (!email) {
          return res
            .status(400)
            .json(
              apiResponse.response(
                false,
                `Email is null: ${email}`,
                `Email is required, please provide a valid email`
              )
            );
        }

        // Check if userRole is present in the request
        if (!userRole) {
          return res
            .status(200)
            .json(
              apiResponse.response(
                false,
                `userRole is null: ${userRole}`,
                `userRole is required, please provide a valid userRole`
              )
            );
        }

        // Set userHelper and the error message based on the userRole
        let userDetails;
        if (
          [
            userRoleConstants.SUPER_ADMINISTRATOR,
            userRoleConstants.MANAGER,
            userRoleConstants.COORDINATOR,
            userRoleConstants.VSC,
          ].includes(userRole)
        ) {
          userDetails = await userAdministrationModel
            .findOne({
              "contactDetails.email": email,
              userRole: userRole,
            })
            .exec();
        } else {
          userDetails = await userModel
            .findOne({
              "contactDetails.email": email,
              userRole: userRole,
            })
            .exec();
        }

        // Get user details from email

        if (!userDetails) {
          return res
            .status(200)
            .json(
              apiResponse.responseWithStatusCode(
                false,
                `User is not found with email ${email}`,
                `Please provide a valid email`,
                apiResponse.apiConstants.USER_NOT_FOUND
              )
            );
        }
        // Send invitation email to userAdministration
        // userDetails.userRole = 'student';
        const setupInvitation = await this.sendInvitationEmail(userDetails);
        try {
          await sendGrid.send(setupInvitation.setupInvitation);
          return res
            .status(200)
            .json(
              apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_SUCCESS,
                `Invitation sent successfully`,
                `Invitation sent successfully to ${setupInvitation.email}, please notify user-admin to check email. Invitation code: ${setupInvitation.invitationCode}`,
                apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESS
              )
            );
        } catch (error) {
          console.error(error);
          return res
            .status(500)
            .json(
              apiResponse.responseWithStatusCode(
                false,
                `Error while sending invitation email`,
                error,
                apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
              )
            );
        }
      } catch (error) {
        console.error("Error while sending invitation email", error);
        return res
          .status(500)
          .json(
            apiResponse.responseWithStatusCode(
              false,
              `Error while sending invitation email`,
              error,
              apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
            )
          );
      }
    });
  }
);

//accept invitation
/**
 * @swagger
 * /api/v1/userAdministration/acceptInvitation:
 *   post:
 *     summary: Accept invitation to join as a user administrator
 *     description: Accept an invitation to join as a user administrator using the provided invitation code and Firebase identifier.
 *     tags:
 *       - User Administration
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               invitationCode:
 *                 type: string
 *                 description: Invitation code received in the invitation email.
 *               firebaseIdentifier:
 *                 type: string
 *                 description: Firebase identifier of the user.
 *     responses:
 *       200:
 *         description: Invitation accepted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Invitation is accepted successfully
 *                 data:
 *                   type: string
 *                   example: Now you can login to your account
 *       400:
 *         description: Invalid request or missing invitationCode or firebaseIdentifier
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Invitation code is null
 *                 error:
 *                   type: string
 *                   example: Invitation code is required, please provide a valid invitation code
 *       500:
 *         description: Error accepting invitation
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while updating invitation code
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.post(
  "/userAdministration/acceptInvitation",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      const { invitationCode, firebaseIdentifier } = fields;

      //check if invitationCode is present in the request
      if (!invitationCode) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is null: ${invitationCode}`,
          `Invitation code is required, please provide a valid invitation code`
        );
        return res.status(400).json(response);
      }

      //check if firebaseIdentifier is present in the request
      if (!firebaseIdentifier) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase identifier is null: ${firebaseIdentifier}`,
          `Firebase identifier is required, please provide a valid firebase identifier`
        );
        return res.status(400).json(response);
      }

      //check if invitation code is valid
      let invitationCodeIsValid =
        await userAdministrationHelper.checkInvitationCodeValidity(
          invitationCode,
          firebaseIdentifier
        );

      if (!invitationCodeIsValid.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation code is invalid: ${invitationCode}`,
          `Invitation code is invalid, please provide a valid invitation code`
        );
        return res.status(400).json(response);
      }
      const userDocument =
        await userAdministrationHelper.getUserDetailsFromFirebaseIdentifier(
          firebaseIdentifier
        );

      const updateInvitation =
        await userAdministrationHelper.updateUserAdminWithInvitationCode(
          userDocument.userId,
          invitationCode,
          true
        );

      if (!updateInvitation.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating invitation code: ${invitationCode}`,
          `Error while updating invitation code, please try again`,
          apiResponse.apiConstants.ERROR_WHILE_ACCEPTING_INVITATION
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Invitation is accepted successfully`,
        `Now you can login to your account`,
        apiResponse.apiConstants.ADMIN_ACCEPT_INVITATION_SUCCESS
      );
      return res.status(200).json(response);
    });
  }
);

//register userAdministration account
/**
 * @swagger
 * /api/v1/userAdministration/register:
 *   post:
 *     summary: Register userAdministration
 *     description: Register a userAdministration based on provided details.
 *     tags:
 *       - User Administration
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               firebaseIdentifier:
 *                 type: string
 *                 description: Firebase identifier of the user.
 *               firebaseUserId:
 *                 type: string
 *                 description: Firebase user ID.
 *               provider:
 *                 type: string
 *                 description: Authentication provider.
 *               profilePic:
 *                 type: string
 *                 format: binary
 *                 description: Profile picture of the user.
 *               userId:
 *                 type: string
 *                 description: User ID.
 *     responses:
 *       200:
 *         description: UserAdministration registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User is registered successfully
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Firebase identifier is null
 *                 error:
 *                   type: string
 *                   example: Firebase identifier is required, please provide a valid firebase identifier
 *       500:
 *         description: Error registering userAdministration
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while registering userAdministration account
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.post(
  "/userAdministration/register",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const {
          firebaseIdentifier,
          firebaseUserId,
          provider,
          profilePic,
          userId,
        } = fields;

        //check if firebaseIdentifier is present in the request
        if (!firebaseIdentifier) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Firebase identifier is null: ${firebaseIdentifier}`,
            `Firebase identifier is required, please provide a valid firebase identifier`
          );
          return res.status(400).json(response);
        }

        //check if firebaseUserId is present in the request
        if (!firebaseUserId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Firebase user id is null: ${firebaseUserId}`,
            `Firebase user id is required, please provide a valid firebase user id`
          );
          return res.status(400).json(response);
        }

        //check if provider is present in the request
        if (!provider) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Provider is null: ${provider}`,
            `Provider is required, please provide a valid provider`
          );
          return res.status(400).json(response);
        }

        // //get user details from firebaseIdentifier
        // const userDocument = await userAdministrationHelper.getUserDetailsFromUserId(userId);

        // //check if user is existing
        // if (!userDocument) {
        //      let response = apiResponse.responseWithStatusCode(
        //           apiResponse.apiConstants.API_REQUEST_SUCCESS,
        //           `User is not registered: ${firebaseIdentifier}`,
        //           `User is not registered, please register first`,
        //           apiResponse.apiConstants.USER_IS_NOT_REGISTERED
        //      );
        //      return res.status(400).json(response);
        // }

        // //check if user is already registered base on firebaseUserId and firebaseIdentifier
        // const firebaseIds = userDocument.firebaseIds;
        // const isUserRegistered = firebaseIds?.find((firebaseId) => firebaseId.firebaseUserId === firebaseUserId && firebaseId.firebaseIdentifier === firebaseIdentifier);

        // if (isUserRegistered) {
        //      let response = apiResponse.responseWithStatusCode(
        //           apiResponse.apiConstants.API_REQUEST_SUCCESS,
        //           `User is already registered: ${firebaseIdentifier}`,
        //           `User is already registered, please login`,
        //           apiResponse.apiConstants.DATA_ALREADY_EXISTS
        //      );
        //      return res.status(400).json(response);
        // }

        // //update userAdministration with firebaseUserId and firebaseIdentifier
        // const newFirebaseIds = {
        //      firebaseUserId,
        //      firebaseIdentifier,
        //      provider,
        // };

        // //update userAdministration with firebaseUserId and firebaseIdentifier
        // userDocument.firebaseIds[0] = newFirebaseIds;
        // //update userAdministration with profilePic
        // userDocument.profilePic = profilePic;

        // const updateAdmin = await userDocument.save();

        const newFirebaseIds = {
          firebaseUserId,
          identifier: firebaseIdentifier,
          provider,
        };

        const updateFirebaseIdsAndProfilePic =
          await userAdministrationHelper.updateUserAdministrationFirebaseIdsAndProfilePicture(
            userId,
            newFirebaseIds,
            profilePic
          );

        if (!updateFirebaseIdsAndProfilePic.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Error while registering userAdministration account: ${firebaseIdentifier}`,
            `Error while registering userAdministration account, please try again`,
            apiResponse.apiConstants
              .ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT
          );
          return res.status(400).json(response);
        }
        const token = generateAccessToken(userId);
        const refreshToken = generateRefreshToken(userId);

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User is registered successfully`,
          {
            data: updateFirebaseIdsAndProfilePic.data,
            message: "User is registered successfully",
            token,
          },
          apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
        );
        res.cookie("refreshToken", refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === "production",
          path: "/",
        });
        return res.status(200).json(response);
      } catch (error) {
        console.log(
          "Error while registering userAdministration account",
          error
        );
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error while registering userAdministration account`,
          error,
          apiResponse.apiConstants
            .ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT
        );
        return res.status(500).json(response);
      }
    });
  }
);

/**
 * Setup and send invitation email to a user administrator.
 *
 * @param {Object} userDetails - The user's details.
 * @param {boolean} sendEmailOnCreate - Whether to send the email on create.
 * @returns {Object} An object containing the status and data of the invitation process.
 */
exports.sendInvitationEmail = async (userDetails, sendEmailOnCreate) => {
  try {
    const { email } = userDetails.contactDetails;
    const { userId } = userDetails;
    const { label: role } = userRoleTranslated.find(
      (role) => role.id === userDetails.userRole
    );
    const fullName = `${userDetails.contactDetails.firstName} ${userDetails.contactDetails.lastName}`;

    // Generate invitation code
    const invitationCode =
      userAdministrationHelper.generateInvitationCode(email);
    const invitationLink = `${appConstants.APP_BASE_URL}/IS/register/?accept=${invitationCode}&firebaseIdentifier=${email}&userId=${userId}`;
    const emailSubject = `Invited to join zupdeco.org`;
    const setupInvitation = sendGridHelper.invitationToAdmin(
      email,
      emailSubject,
      invitationLink,
      fullName,
      role
    );

    await userAdministrationHelper.updateUserAdminWithInvitationCode(
      userId,
      invitationCode,
      false
    );

    const invitationData = {
      setupInvitation,
      invitationCode,
      email,
    };

    if (sendEmailOnCreate) {
      try {
        await sendGrid.send(invitationData.setupInvitation);
        return apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invitation sent successfully`,
          `Invitation sent successfully to ${invitationData.email}, please notify user-admin to check email. Invitation code: ${invitationData.invitationCode}`,
          apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESS
        );
      } catch (error) {
        console.log("ERROR =>", error);
        if (error.response) {
          console.error(error.response.body);
          console.log("ERROR BODY =>", error.response.body);
        }
        console.error("Error while sending invitation email", error);
        return apiResponse.responseWithStatusCode(
          false,
          `Error while sending invitation email`,
          error,
          apiResponse.apiConstants.ERROR_WHILE_SENDING_INVITATION_EMAIL
        );
      }
    } else {
      return invitationData;
    }
  } catch (error) {
    return { status: false, data: error };
  }
};

//get List of Coordinators base on sectorIds
/**
 * @swagger
 * /api/v1/userAdministration/coordinatorsBaseOnSectors:
 *   get:
 *     summary: Get coordinators based on sectors
 *     description: Retrieve a list of coordinators based on provided sector IDs.
 *     tags:
 *       - User Administration
 *     parameters:
 *       - in: query
 *         name: sectorIds
 *         required: true
 *         description: Comma-separated list of sector IDs.
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: List of coordinators retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: List of coordinators based on sectors
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: 12345
 *                       fullName:
 *                         type: string
 *                         example: John Doe
 *                       email:
 *                         type: string
 *                         example: <EMAIL>
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: sectorIds is null
 *                 error:
 *                   type: string
 *                   example: sectorIds is required, please provide a valid sectorIds
 *       500:
 *         description: Error getting coordinators based on sectors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while getting coordinators base on sectors
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/userAdministration/coordinatorsBaseOnDepartment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const { departmentCode, program } = req.query;
      const parsedDepartmentCode = JSON.parse(departmentCode);
      //check if sectorIds is present in the request
      if (!departmentCode.length) {
        let response = apiResponse.response(
          false,
          `departmentCode is empty: ${parsedDepartmentCode}`,
          `departmentCode is required, please provide a valid departmentCode array`
        );
        return res.status(400).json(response);
      }
      const coordinators = await UserAdministrationModel.find({
        "administrationPreferences.program.programId": program,
        userRole: userRoleConstants.COORDINATOR,
        "administrationPreferences.department.departmentId": {
          $in: parsedDepartmentCode,
        },
      }).exec();
      if (coordinators) {
        const coordinatorsDetails = coordinators.map((coordinator) => {
          return {
            userId: coordinator.userId,
            fullName: `${coordinator.contactDetails.firstName} ${coordinator.contactDetails.lastName}`,
            email: coordinator.contactDetails.email,
          };
        });

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `List of coordinators base on sectors`,
          coordinatorsDetails
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "List of coordinators base on sectors is empty",
          []
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log("Error while getting coordinators base on sectors", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting coordinators base on sectors`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
router.get(
  "/coordinators",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      console.log("userRole =>", req.query);
      const program = req.query.program;
      const depIds =
        req.query.depIds && req.query.depIds !== "undefined"
          ? JSON.parse(req.query.depIds)
          : [];
      const establishmentId =
        req.query.establishmentId && req.query.establishmentId !== "undefined"
          ? JSON.parse(req.query.establishmentId)
          : [];
      const onligneProgram = [
        programConstants.HOME_CLASSES.programId,
        programConstants.CLASSSES_HOME.programId,
        programConstants.ZUPDEFOOT.programId,
      ];
      let query = {
        userRole: COORDINATOR,
        ...(program &&
          program !== "undefined" && {
            "administrationPreferences.program.programId": program,
          }),
      };
      if (COORDINATOR == req.userRole) {
        query["userId"] = req.userId;
      } else {
        if (
          program == programConstants.DEVOIRS_FAITS.programId &&
          establishmentId.length > 0
        ) {
          query["administrationPreferences.establishment.establishmentId"] = {
            $in: establishmentId,
          };
        } else if (onligneProgram.includes(program) && depIds.length > 0) {
          query["administrationPreferences.department.departmentId"] = {
            $in: depIds,
          };
        }
      }
      // Récupération des VSC
      const vsc = await UserAdministrationModel.find({
        "administrationPreferences.program.programId": program,
        userRole: VSC,
      }).exec();

      // Extraction des IDs de coordinateurs
      // const coordinatorIds = vsc?.flatMap((vscItem) =>
      //   vscItem.administrationPreferences.coordinators.map(
      //     (coordinator) => coordinator.coordinatorId
      //   )
      // ) || []; // Assurez-vous que cela retourne un tableau vide si vsc est indéfini
      // console.log('coordinatorIds', coordinatorIds)
      // // Construction de la requête
      // query["userId"] = { $nin: coordinatorIds }; // Non affecté à un VSC
      // Récupération des coordinateurs
      console.log("queryquery", query);
      const coordinators = await UserAdministrationModel.find(query).exec();

      if (coordinators) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `List of coordinators base on sectors`,
          coordinators
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "List of coordinators base on sectors is empty",
          []
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log("Error while getting coordinators base on sectors", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting coordinators base on sectors`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
//Pre-filled list with the first and last names of the VSC's, in charge of the selected establishment, with the possibility of modifying the list by removing or adding or removing VSC's
/**
 * @swagger
 * /api/v1/userAdministration/listofvscsbaseOnestablishment:
 *   get:
 *     summary: Get list of VSCs based on establishment
 *     description: Retrieve a list of VSCs based on provided establishment IDs.
 *     tags:
 *       - User Administration
 *     parameters:
 *       - in: query
 *         name: establishmentIds
 *         required: true
 *         description: Comma-separated list of establishment IDs.
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       200:
 *         description: List of VSCs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: List of VSCs base on establishment
 *                 data:
 *                   type: object
 *                   properties:
 *                     listOfVSCs:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: 12345
 *                           fullName:
 *                             type: string
 *                             example: John Doe
 *                     listOfVscsFromEstablishment:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                             example: 54321
 *                           fullName:
 *                             type: string
 *                             example: Jane Smith
 *       400:
 *         description: Invalid request or missing required fields
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: establishmentIds is null
 *                 error:
 *                   type: string
 *                   example: establishmentIds is required, please provide a valid establishmentIds
 *       500:
 *         description: Error getting list of VSCs based on establishment
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: Error while getting list of VSCs base on establishment
 *                 error:
 *                   type: string
 *                   example: Error message details
 */
router.get(
  "/userAdministration/listofvscsbaseOnestablishment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const { establishmentIds } = req.query;

      //convert establishmentIds to array
      let establishmentIdsArray;
      let listOfVscsFullName = [];
      if (establishmentIds) {
        establishmentIdsArray = JSON.parse(establishmentIds);

        //get list of establishments base on establishmentIds
        let listOfVscs = await userAdministrationModel
          .find({
            "administrationPreferences.establishment.establishmentId": {
              $in: establishmentIdsArray,
            },
            userRole: userRoleConstants.VSC,
          })
          .exec();

        //return full name of vsc in the list and userId
        for (let i = 0; i < listOfVscs.length; i++) {
          const vsc = listOfVscs[i];
          const vscFullName = {
            userId: vsc.userId,
            fullName: `${vsc.contactDetails.firstName} ${vsc.contactDetails.lastName}`,
          };
          listOfVscsFullName.push(vscFullName);
        }
      }
      const listOfVSCs = await userAdministrationModel
        .find({ userRole: userRoleConstants.VSC })
        .exec();

      //return full name of vsc in the list and userId
      let listOfVscsData = [];
      listOfVSCs.forEach((vsc) => {
        const vscFullName = {
          userId: vsc.userId,
          fullName: `${vsc.contactDetails.firstName} ${vsc.contactDetails.lastName}`,
        };
        listOfVscsData.push(vscFullName);
      });

      //combine the list of vscs and the list of vscs base on establishment
      const combinedList = {
        listOfVSCs: listOfVscsData,
        listOfVscsFromEstablishment: listOfVscsFullName,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `List of vscs base on establishment`,
        combinedList
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(
        "Error while getting list of vscs base on establishment",
        error
      );
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting list of vscs base on establishment`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
router.get(
  "/vscsBasedOnDepartment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      console.log("userConnected", req.userId, req.userRole);
      let query = {
        userRole: userRoleConstants.VSC,
      };
      if (req.userRole == userRoleConstants.COORDINATOR) {
        query["administrationPreferences.coordinators.coordinatorId"] =
          req.userId;
      }
      const vscs = await UserAdministrationModel.find(query)
        .select("userId contactDetails")
        .exec();
      if (!vscs.length) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `No VSCs found`,
          []
        );
        return res.status(400).json(response);
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `VSCs found`,
        vscs
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log("Error while getting VSCs based on coordinator", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error while getting VSCs based on coordinator`,
        error
      );
      return res.status(500).json(response);
    }
  }
);
router.post(
  "/refresh_token",
  [
    verifyApiKey,
    checkIfAuthenticated,
    // checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const refreshToken = req.cookies.refreshToken;
    if (!refreshToken) {
      return res.status(401).send({ error: "Unauthenticated refresh token." });
    }
    try {
      jwt.verify(refreshToken, process.env.JWT_SECRET, (err, userInfo) => {
        console.log("userInfo", userInfo);
        if (err) {
          console.log("Error verifying auth token:", err);
          return res.status(401).send({ error: "Unauthenticated." });
        }
        return res
          .status(200)
          .send({ success: true, token: generateAccessToken(userInfo.userId) });
      });
    } catch (error) {
      console.log("Error while refreshing token", error);
      return res.status(401).send({ error: "Unauthenticated." });
    }
  }
);

/**
 * @swagger
 * /api/v1/getCoordinatorTutoringStats:
 *   get:
 *     summary: Récupère les statistiques de tutorat pour un coordinateur
 *     description: Récupère des statistiques sur les tuteurs, les élèves et les binômes sous la responsabilité d'un coordinateur
 *     tags:
 *       - User Administrations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: program
 *         required: false
 *         description: ID du programme pour filtrer les statistiques
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 */
router.get(
  "/getCoordinatorTutoringStats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const program = req.query.program; // ID du programme (facultatif)

      const stats = await statsHelper.getCoordinatorTutoringStats({
        userId,
        userRole,
        program
      });

      if (!stats.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          stats.message,
          stats.data
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        stats.message,
        stats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Erreur lors de la récupération des statistiques de tutorat`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

//get coordinator sessions stats
/**
 * @swagger
 * /api/v1/userAdministrations/getCoordinatorSessionsStats:
 *   get:
 *     summary: Récupère les statistiques de séances pour un coordinateur
 *     description: Récupère des statistiques sur le nombre de séances planifiées, réalisées, et annulées par période
 *     tags:
 *       - User Administrations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         required: true
 *         description: Période pour laquelle récupérer les statistiques (perWeek, perMonth, perTrimester ou perYear)
 *         schema:
 *           type: string
 *           enum: [perWeek, perMonth, perTrimester, perYear]
 *       - in: query
 *         name: program
 *         required: false
 *         description: ID du programme pour filtrer les statistiques
 *         schema:
 *           type: string
 *       - in: query
 *         name: timeslot
 *         required: false
 *         description: Pour filtrer sur une période spécifique. Format "MM/YYYY" pour un mois (ex. "02/2025" pour février 2025)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 */
router.get(
  "/getCoordinatorSessionsStats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const period = req.query.period; // 'perWeek', 'perMonth', 'perTrimester' ou 'perYear'
      const program = req.query.program; // ID du programme (facultatif)
      const timeslot = req.query.timeslot; // Période spécifique (facultatif)

      if (!period) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          "Le paramètre 'period' est requis (perWeek, perMonth, perTrimester ou perYear)",
          null
        );
        return res.status(400).json(response);
      }

      const stats = await statsHelper.getCoordinatorSessionsStats({
        userId,
        userRole,
        period,
        program,
        timeslot,
      });

      if (!stats.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          stats.message,
          stats.data
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        stats.message,
        stats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Erreur lors de la récupération des statistiques des séances`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/userAdministrations/getCoordinatorAttendanceStats:
 *   get:
 *     summary: Récupère les statistiques de présence et d'assiduité pour un coordinateur
 *     description: Récupère des statistiques sur les séances planifiées, réalisées, annulées et avec absences pour les tuteurs sous la responsabilité d'un coordinateur
 *     tags:
 *       - User Administrations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         required: true
 *         description: Période pour laquelle récupérer les statistiques (perWeek, perMonth, perTrimester ou perYear)
 *         schema:
 *           type: string
 *           enum: [perWeek, perMonth, perTrimester, perYear]
 *       - in: query
 *         name: program
 *         required: false
 *         description: ID du programme pour filtrer les statistiques
 *         schema:
 *           type: string
 *       - in: query
 *         name: timeslot
 *         required: false
 *         description: Pour filtrer sur une période spécifique. Format "MM/YYYY" pour un mois (ex. "02/2025" pour février 2025)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Statistiques de présence récupérées avec succès"
 *                 data:
 *                   type: object
 *                   properties:
 *                     period:
 *                       type: string
 *                       example: "Semaine"
 *                     currentPeriod:
 *                       type: object
 *                       properties:
 *                         start:
 *                           type: string
 *                           format: date-time
 *                         end:
 *                           type: string
 *                           format: date-time
 *                     previousPeriod:
 *                       type: object
 *                       properties:
 *                         start:
 *                           type: string
 *                           format: date-time
 *                         end:
 *                           type: string
 *                           format: date-time
 *                     plannedSessions:
 *                       type: object
 *                       properties:
 *                         count:
 *                           type: number
 *                           example: 25
 *                         evolution:
 *                           type: number
 *                           example: 10
 *                     completionRate:
 *                       type: object
 *                       properties:
 *                         rate:
 *                           type: number
 *                           example: 80
 *                         evolution:
 *                           type: number
 *                           example: 5
 *                     absenteeismRate:
 *                       type: object
 *                       properties:
 *                         rate:
 *                           type: number
 *                           example: 15
 *                         evolution:
 *                           type: number
 *                           example: -2
 *                     canceledSessions:
 *                       type: object
 *                       properties:
 *                         count:
 *                           type: number
 *                           example: 5
 *                         evolution:
 *                           type: number
 *                           example: -10
 *       400:
 *         description: Paramètres invalides ou erreur lors de la récupération des statistiques
 *       401:
 *         description: Non autorisé
 *       500:
 *         description: Erreur serveur
 */
router.get(
  "/getCoordinatorAttendanceStats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const period = req.query.period; // 'perWeek', 'perMonth' ou 'perTrimester'
      const program = req.query.program; // ID du programme (facultatif)
      const timeslot = req.query.timeslot; // Période spécifique (facultatif)

      if (!period) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          "Le paramètre 'period' est requis (perWeek, perMonth, perTrimester ou perYear)",
          null
        );
        return res.status(400).json(response);
      }

      const stats = await statsHelper.getCoordinatorAttendanceStats({
        userId,
        userRole,
        period,
        program,
        timeslot,
      });

      if (!stats.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          stats.message,
          stats.data
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        stats.message,
        stats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Erreur lors de la récupération des statistiques de présence et d'assiduité`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/userAdministrations/getCoordinatorPerformanceStats:
 *   get:
 *     summary: Récupère les statistiques de performance pour un coordinateur
 *     description: Récupère les statistiques de satisfaction et d'objectifs atteints pour les tuteurs sous la responsabilité d'un coordinateur
 *     tags:
 *       - User Administrations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: period
 *         required: true
 *         description: Période pour laquelle récupérer les statistiques (perWeek, perMonth, perTrimester ou perYear)
 *         schema:
 *           type: string
 *           enum: [perWeek, perMonth, perTrimester, perYear]
 *       - in: query
 *         name: program
 *         required: false
 *         description: ID du programme pour filtrer les statistiques
 *         schema:
 *           type: string
 *       - in: query
 *         name: timeslot
 *         required: false
 *         description: Pour filtrer sur une période spécifique. Format "MM/YYYY" pour un mois (ex. "02/2025" pour février 2025)
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Statistiques de performance récupérées avec succès"
 *                 data:
 *                   type: object
 *                   properties:
 *                     period:
 *                       type: string
 *                       example: "Mois"
 */
router.get(
  "/getCoordinatorPerformanceStats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const period = req.query.period; // 'perWeek', 'perMonth', 'perTrimester' ou 'perYear'
      const program = req.query.program; // ID du programme (facultatif)
      const timeslot = req.query.timeslot; // Période spécifique (facultatif)

      if (!period) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          "Le paramètre 'period' est requis (perWeek, perMonth, perTrimester ou perYear)",
          null
        );
        return res.status(400).json(response);
      }

      const stats = await statsHelper.getCoordinatorPerformanceStats({
        userId,
        userRole,
        period,
        program,
        timeslot,
      });

      if (!stats.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          stats.message,
          stats.data
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        stats.message,
        stats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Erreur lors de la récupération des statistiques de performance`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/userAdministrations/getCoordinatorTimeEvolutionStats:
 *   get:
 *     summary: Récupère l'évolution temporelle des statistiques sur 6 mois
 *     description: Récupère l'évolution des nouvelles inscriptions, des séances réalisées, et du taux de satisfaction sur les 6 derniers mois
 *     tags:
 *       - User Administrations
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: program
 *         required: false
 *         description: ID du programme pour filtrer les statistiques
 *         schema:
 *           type: string
 *       - in: query
 *         name: timeslot
 *         required: false
 *         description: Pour filtrer sur une période spécifique au format "MM/YYYY,MM/YYYY" (ex. "10/2024,03/2025"). Par défaut, calcule les statistiques sur les 6 derniers mois (mois courant inclus).
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Statistiques récupérées avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Statistiques d'évolution temporelle récupérées avec succès"
 *                 data:
 *                   type: object
 *                   properties:
 *                     newRegistrations:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           month:
 *                             type: string
 *                             example: "Mars"
 *                           year:
 *                             type: number
 *                             example: 2023
 *                           value:
 *                             type: number
 *                             description: Nombre total de nouveaux inscrits
 *                             example: 15
 *                           tutors:
 *                             type: number
 *                             description: Nombre de nouveaux tuteurs
 *                             example: 7
 *                           students:
 *                             type: number
 *                             description: Nombre de nouveaux élèves
 *                             example: 8
 *                     completedSessions:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           month:
 *                             type: string
 *                             example: "Mars"
 *                           year:
 *                             type: number
 *                             example: 2023
 *                           value:
 *                             type: number
 *                             description: Nombre de séances réalisées
 *                             example: 28
 *                           totalSessions:
 *                             type: number
 *                             description: Nombre total de séances
 *                             example: 35
 *                     satisfactionRate:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           month:
 *                             type: string
 *                             example: "Mars"
 *                           year:
 *                             type: number
 *                             example: 2023
 *                           value:
 *                             type: number
 *                             description: Taux de satisfaction (note sur 5)
 *                             example: 4.2
 *                           totalReports:
 *                             type: number
 *                             description: Nombre de rapports avec notes
 *                             example: 25
 *       400:
 *         description: Erreur lors de la récupération des statistiques
 *       401:
 *         description: Non autorisé
 *       500:
 *         description: Erreur serveur
 */
router.get(
  "/getCoordinatorTimeEvolutionStats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const userId = req.userId;
      const userRole = req.userRole;
      const period = req.query.period; // Ajout du paramètre period
      const program = req.query.program; // ID du programme (facultatif)
      const timeslot = req.query.timeSlot || req.query.timeslot; // Période spécifique (facultatif) - accepte les deux notations

      const stats = await statsHelper.getCoordinatorTimeEvolutionStats({
        userId,
        userRole,
        program,
        period,
        timeslot,
      });

      if (!stats.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_ERROR,
          stats.message,
          stats.data
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        stats.message,
        stats.data
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_ERROR,
        `Erreur lors de la récupération des statistiques d'évolution temporelle`,
        error
      );
      return res.status(500).json(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/coordinators/byProgram:
 *   get:
 *     summary: Récupérer la liste des coordinateurs pour un programme spécifique
 *     description: Retourne la liste des coordinateurs avec leur nom complet, email, téléphone et ID pour un programme donné
 *     tags:
 *       - User Administration
 *     parameters:
 *       - in: query
 *         name: programId
 *         required: true
 *         description: L'identifiant du programme
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Liste des coordinateurs récupérée avec succès
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: success
 *                 message:
 *                   type: string
 *                   example: Liste des coordinateurs récupérée avec succès
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                         example: zu-1234567890
 *                       fullName:
 *                         type: string
 *                         example: Jean Dupont
 *                       email:
 *                         type: string
 *                         example: <EMAIL>
 *                       phoneNumber:
 *                         type: string
 *                         example: +33123456789
 *       400:
 *         description: Le paramètre programId est requis
 *       500:
 *         description: Erreur lors de la récupération des coordinateurs
 */
router.get(
  "/coordinators/byProgram",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR]),
  ],
  async (req, res) => {
    try {
      const { programId } = req.query;
      
      // Si programId est fourni, on le parse en tableau (au cas où plusieurs IDs sont fournis)
      let programIds = [];
      if (programId) {
        try {
          programIds = JSON.parse(programId);
          // Si ce n'est pas un tableau, on le convertit en tableau
          if (!Array.isArray(programIds)) {
            programIds = [programIds];
          }
        } catch (e) {
          // Si le parsing échoue, on considère que c'est un seul ID
          programIds = [programId];
        }
      }
      
      // Appel à la fonction helper avec le tableau de programIds
      const result = await userAdministrationHelper.getCoordinatorsByProgram(programIds);
      
      if (!result.status) {
        return res.status(400).json(
          apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            result.message,
            result.data
          )
        );
      }
      
      return res.status(200).json(
        apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          result.message,
          result.data
        )
      );
    } catch (error) {
      console.log("Erreur lors de la récupération des coordinateurs", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Erreur lors de la récupération des coordinateurs",
        error
      );
      return res.status(500).json(response);
    }
  }
);

//export router
module.exports = router;
