const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("api.config.routes.js");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//Default config for invalid routes
exports.defaultRoute = (req, res, next) => {
  //Check if the route is  equal to apiResponse.apiConstants.API_VERSION
  if (req.originalUrl === apiResponse.apiConstants.API_VERSION) {
    this.aboutApiVersion(req, res, next);
    return;
  }
  console.log(`Original Url`, req.originalUrl);
  console.log(`Current api version`, apiResponse.apiConstants.API_VERSION);

  let response = apiResponse.responseWithStatusCode(
    apiResponse.apiConstants.API_REQUEST_FAILED,
    `Route not found 🛑`,
    "The requested route could not be found on the server. Please check the URL and try again 👨🏻‍💻.",
    404
  );
  console.log("Error handler", response);
  let apiLog = {
    response: response,
    request: {
      requestBody: req.body,
      requestUrl: req.originalUrl,
      requestMethod: req.method,
      requestHeaders: req.headers,
      requestParams: req.params,
      requestQuery: req.query,
    },
  };
  logger
    .newLog(logger.getLoggerTypeConstants().api)
    .error(JSON.stringify(apiLog));

  return res.status(500).json(response);
};

//Default error handler
exports.defaultErrorHandler = (err, req, res, next) => {
  let response = apiResponse.responseWithStatusCode(
    apiResponse.apiConstants.API_REQUEST_FAILED,
    `Something went wrong 🛑 err : ${err}, err.message :${err?.message} `,
    " Please try again 😎.",
    500
  );
  let apiLog = {
    response: response,
    request: {
      error: err,
      requestBody: req.body,
      requestUrl: req.originalUrl,
      requestMethod: req.method,
      requestHeaders: req.headers,
      requestParams: req.params,
      requestQuery: req.query,
    },
  };

  logger
    .newLog(logger.getLoggerTypeConstants().api)
    .error(JSON.stringify(apiLog));

  return res.status(500).json(response);
};

//About api version
exports.aboutApiVersion = (req, res, next) => {
  let response = apiResponse.responseWithStatusCode(
    apiResponse.apiConstants.API_REQUEST_SUCCESS,
    `Welcome to the API version ${apiResponse.apiConstants.API_VERSION}| 🚀`,
    " Please check the documentation for more information 📚.",
    200
  );
  return res.status(200).json(response);
};
