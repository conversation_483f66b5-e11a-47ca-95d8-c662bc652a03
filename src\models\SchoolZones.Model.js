const mongoose = require("mongoose");

const Schema = mongoose.Schema;

const SchoolZonesSchema = new Schema(
     {
          zoneName: {
               type: String,
               required: true,
          },
          schoolZoneHolidays: [
               {
                    holidayName: String,
                    holidayStartDate: Date,
                    holidayEndDate: Date,
                    tutoringAllowed: Boolean,
               },
          ],
     },
     { versionKey: false }
);

module.exports = SchoolZones = mongoose.model("schoolZones", SchoolZonesSchema);
