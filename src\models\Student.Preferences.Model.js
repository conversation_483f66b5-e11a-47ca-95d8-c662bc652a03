const mongoose = require("mongoose");

const studentUserConstants = require("../utils/constants/student.constants.js");
const studentConstants = require("../utils/constants/student.constants.js");

const studentPreferencesSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
    index: {
      unique: true,
      sparse: false,
    },
  },
  totalSessionsHours: {
    type: Number,
    default: 0,
  },
  contactDetails: {
    firstName: String,
    lastName: String,
    phoneNumber: String,
    email: String,
  },
  situation: {
    type: String,
    default: studentConstants.studentStatus.NEW,
  },
  parentUserId: {
    type: String,
  },
  typeOfStudent: String,
  address: {
    addressLine1: String,
    city: String,
    country: String,
    zipCode: String,
  },
  program: {
    programId: String,
    programName: String,
    priority: Number,
  },
  userStatus: {
    type: String,
    default: studentUserConstants.userStatus.ON_HOLD,
  },
  isPartner: {
    type: Boolean,
    default: false,
  },
  partner: {
    type: String,
  },
  assignment: {
    tutoringType: [String],
    subjectToStudy: String,
    establishments: [
      {
        establishmentName: String,
        establishmentId: String,
        isMissing: {
          type: Boolean,
          default: false,
        },
      },
    ],
    department: {
      departmentName: String,
      departmentId: String,
    },
    sectors: [
      {
        sectorName: String,
        sectorId: String,
      },
    ],
    department: {
      departmentName: String,
      departmentId: String,
    },
    vsc: [
      {
        vscName: String,
        vscId: String,
      },
    ],
    level: [
      {
        levelName: String,
        levelId: String,
      },
    ],
    specialty: {
      type: String,
    },
    classes: [
      {
        className: String,
        classId: String,
      },
    ],
  },
  availability: [
    {
      dayOfTheWeek: Number,
      startTime: String,
      endTime: String,
      taken: Boolean,
      fullName : String,
    },
  ],
  doesYourChildNeedHelpWith: String,
  invitation: {
    invitationCode: String,
    invitationAccepted: Boolean,
    invitationDate: {
      type: Date,
    },
    invitationAcceptedDate: {
      type: Date,
    },
  },
  comments: String,
  suiviCoordo: String,
  testHardware: {
    isWorkingVideoDevice: Boolean,
    isWorkingAudioDevice: Boolean,
  },
  matching: [
    {
      sessionId: String,
      tutorId: String,
      status: String,
    },
  ],
  matchedTutors: {
    type: [
      {
        fullName: String,
        userId: String,
        absence: Boolean,
        report: Object,
      },
    ],
    default: [],
  },
  gender: String,
  typeOfUser: String,
  iAcceptToBeContactedForParticipatingInAStudy: Boolean,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
  },
  nbrSession : Number, 
  note : Number, 
  pourcentageCr : Number, 
  nbrCr : Number, 
  lastSession : Object,
  futurSessions : Object,
  LastUpdatedBy: {
    userId: String,
    firstName: String,
    lastName: String,
    email: String,
  },
});

module.exports = mongoose.model("studentPreferences", studentPreferencesSchema);
