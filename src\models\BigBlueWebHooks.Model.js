const mongoose = require("mongoose");

const bigBlueWebHooksSchema = new mongoose.Schema(
     {
          webHookId: String,
          callbackURL: String,
          permanentHook: Boolean,
          isUpAndRunning: {
               type: Boolean,
               default: false,
          },
     },
     { versionKey: false }
);

module.exports = mongoose.model("bigBlueWebHooks", bigBlueWebHooksSchema);
