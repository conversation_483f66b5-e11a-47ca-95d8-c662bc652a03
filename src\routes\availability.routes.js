const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("apiLogger.js");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import Availability Model
const availabilityModel = require("../models/Availability.Model.js");

const mongoose = require("mongoose");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

router.use([verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])]);
// router.use((req, res, next) => {
//      verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.PUBLIC_HOLIDAYS);
// });

/**
 * @swagger
 * /api/v1/availability:
 *   post:
 *     summary: Update availability
 *     description: Update availability in the database.
 *     tags: [Availability]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               availability:
 *                 type: string
 *                 format: json
 *                 description: JSON string containing availability data.
 *     responses:
 *       '200':
 *         description: Availability updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Availability updated in database"
 *                   description: A success message.
 *                 data:
 *                   type: object
 *                   description: The availability data saved in the database.
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: The ID of the saved availability data.
 *                     dayOfTheWeek:
 *                       type: string
 *                       description: The day of the week.
 *                     startTime:
 *                       type: string
 *                       description: The start time.
 *                     endTime:
 *                       type: string
 *                       description: The end time.*
 *       '500':
 *         description: Error occurred while updating availability or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Failed to post availability in database"
 *                   description: Error message.
 */
router.post("/availability", (req, res, next) => {
  const form = formidable({ multiples: true });
  form.parse(req, async (err, fields, files) => {
    try {
      const availability = JSON.parse(fields.availability);
      const availabilityList = new availabilityModel({
        availability: availability.availability,
      });
      const availabilityListSaved = await availabilityList.save();
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Availability updated in database`,
        availabilityListSaved
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Failed to post availability in database`,
        error
      );
      return res.status(500).json(response);
    }
  });
});

//get list of availability's from database . Add to route verifyApiKey and verifyUserRole.checkUserRoleAndAccessLevel
/**
 * @swagger
 * /api/v1/availabilities:
 *   get:
 *     summary: Get availability list
 *     description: Retrieve a list of availabilities from the database.
 *     tags: [Availability]
 *     parameters:
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *         description: Filter availabilities by type.
 *     responses:
 *       '200':
 *         description: Successfully retrieved availability list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "List of availabilities from the database"
 *                   description: A success message.
 *                 data:
 *                   type: array
 *                   description: An array containing the availability list.
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: The ID of the availability.
 *                       dayOfTheWeek:
 *                         type: string
 *                         description: The day of the week.
 *                       startTime:
 *                         type: string
 *                         description: The start time.
 *                       endTime:
 *                         type: string
 *                         description: The end time.
 *       '500':
 *         description: Error occurred while retrieving the availability list or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Failed to get list of availabilities from the database"
 *                   description: Error message.
 */
router.get("/availabilities", async (req, res, next) => {
  try {
    const type = req.query.type;
    const query = type ? { type } : {};
    let availabilityList = await availabilityModel.find(query);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `List of availability's from database`,
      availabilityList
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get list of availability's from database`,
      error
    );
    return res.status(500).json(response);
  }
});
/**
 * @swagger
 * /api/v1/availability:
 *   get:
 *     summary: Get availability by ID
 *     description: Retrieve availability by ID from the database.
 *     tags: [Availability]
 *     parameters:
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         description: Filter availabilities by id.
 *     responses:
 *       '200':
 *         description: Successfully retrieved availability list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "List of availabilities from the database"
 *                   description: A success message.
 *                 data:
 *                   type: array
 *                   description: An array containing the availability list.
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: The ID of the availability.
 *                       dayOfTheWeek:
 *                         type: string
 *                         description: The day of the week.
 *                       startTime:
 *                         type: string
 *                         description: The start time.
 *                       endTime:
 *                         type: string
 *                         description: The end time.
 *       '500':
 *         description: Error occurred while retrieving the availability list or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Failed to get list of availabilities from the database"
 *                   description: Error message.
 */
router.get("/availability", async (req, res, next) => {
  try {
    const availabilityId = req.query.availabilityId;
    //check if availabilityId is provided
    if (!availabilityId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `availabilityId is required. Please provide a valid availabilityId.`
      );
      return res.status(400).json(response);
    }

    let availability = await availabilityModel.findOne({
      _id: mongoose.Types.ObjectId(availabilityId),
    });
    if (!availability) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Availability not found.`
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `Availability by id`,
      availability
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log("error", error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get Availability by id`,
      error
    );
    return res.status(500).json(response);
  }
});

//update availability in database . Add to route verifyApiKey and verifyUserRole.checkUserRoleAndAccessLevel
/**
 * @swagger
 * /api/v1/availability:
 *   put:
 *     summary: Put availability by ID
 *     description: Update availability by ID from the database.
 *     tags: [Availability]
 *     parameters:
 *       - in: query
 *         name: id
 *         description: The ID of the availability to be updated.
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               dayOfTheWeek:
 *                 type: number
 *                 description: The day of the week.
 *               startTime:
 *                 type: string
 *                 description: The start time.
 *               endTime:
 *                 type: string
 *                 description: The end time.
 *     responses:
 *       '200':
 *         description: Successfully updated availability list.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "List of availabilities from the database"
 *                   description: A success message.
 *                 data:
 *                   type: array
 *                   description: An array containing the availability list.
 *                   items:
 *                     type: object
 *                     properties:
 *                       _id:
 *                         type: string
 *                         description: The ID of the availability.
 *                       dayOfTheWeek:
 *                         type: string
 *                         description: The day of the week.
 *                       startTime:
 *                         type: string
 *                         description: The start time.
 *                       endTime:
 *                         type: string
 *                         description: The end time.
 *       '500':
 *         description: Error occurred while retrieving the availability list or internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                   description: Indicates the status of the request.
 *                 message:
 *                   type: string
 *                   example: "Failed to get list of availabilities from the database"
 *                   description: Error message.
 */
router.put("/availability", async (req, res, next) => {
  const form = formidable({ multiples: true });
  form.parse(req, async (err, fields, files) => {
    try {
      const availabilityId = fields.availabilityId;
      const availabilities = JSON.parse(fields.availability);

      if (!availabilityId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `availabilityId is required. Please provide a valid availabilityId.`
        );
        return res.status(400).json(response);
      }

      const availability = await availabilityModel.findOne({
        _id: mongoose.Types.ObjectId(availabilityId),
      });
      availability.availability = availabilities;

      await availability.save();

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Availability updated in database`,
        availability
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log("error", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Failed to update availability in database`,
        error
      );
      return res.status(500).json(response);
    }
  });
});

//export router
module.exports = router;
