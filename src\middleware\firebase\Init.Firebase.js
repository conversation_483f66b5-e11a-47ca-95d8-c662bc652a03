const admin = require("firebase-admin");
const uuid = require("uuid");
require("dotenv").config();
console.log(process.env.NODE_ENV);
var serviceAccount;
if (process.env.NODE_ENV === "production") {
  //PROD
  serviceAccount = require("./prod-permissions.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "gs://zupdeco-production.appspot.com",
  });
  console.log("Initializing Firebase Admin App for PROD");
} else if (process.env.NODE_ENV === "preprod") {
  //PREPROD
  serviceAccount = require("./preprod-permissions.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "gs://zupdeco-preprod.appspot.com",
  });
  console.log("Initializing Firebase Admin App for PREPROD");
} else {
  //DEV
  serviceAccount = require("./dev-permissions.json");
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    storageBucket: "gs://zupdeco-development.appspot.com",
  });
  console.log("Initializing Firebase Admin App for DEV");
}

exports.admin = admin;
const bucket = admin.storage().bucket();

const firebaseConstants = require("../../utils/constants/FirebaseConstants");

exports.initFirebase = class InitFirebase {
  static generateRandomGuid() {
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
      /[xy]/g,
      function (c) {
        var r = (Math.random() * 16) | 0,
          v = c == "x" ? r : (r & 0x3) | 0x8;
        return v.toString(16);
      }
    );
  }
  static async uploadFile(file) {
    try {
      var fileName = this.generateRandomGuid();

      var response = await bucket.upload(file.filepath, {
        destination: "generalFile/" + fileName,
        public: true,
        metadata: {
          contentType: file.mimetype,
          cacheControl: "public, max-age=********",
          firebaseStorageDownloadTokens: uuid,
        },
      });

      var file = response[0];
      var download_url = response[0].metadata.mediaLink;

      var preview_url =
        "https://firebasestorage.googleapis.com/v0/b/" +
        bucket.name +
        "/o/" +
        encodeURIComponent(file.name) +
        "?alt=media&token=";
      var file_id = response[0].metadata.id;

      var data = {
        download_url: download_url,
        preview_url: preview_url,
        file_id: file_id,
      };

      return { status: true, data: data };
    } catch (error) {
      console.log(error);
      return { status: false, data: error };
    }
  }

  static async getAuth() {
    try {
      var auth = admin.auth();
      return auth;
    } catch (error) {
      console.log(error);
      return { status: false, data: error };
    }
  }
};
