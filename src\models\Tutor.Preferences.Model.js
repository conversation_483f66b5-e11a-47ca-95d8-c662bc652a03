const mongoose = require("mongoose");

const tutorConstants = require("../utils/constants/tutor.constants");

const dateHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const sessionConstants = require("../utils/constants/sessions.constants");

const tutorPreferencesSchema = new mongoose.Schema(
  {
    userId: {
      type: String,
      required: true,
      index: {
        unique: true,
        sparse: false,
      },
    },
    totalSessionsHours: {
      type: Number,
      default: 0,
    },
    contactDetails: {
      firstName: String,
      lastName: String,
      phoneNumber: String,
      email: String,
    },
    situation: {
      type: String,
      default: tutorConstants.situationOfTutor.NEW,
    },
    situationLastUpdate: {
      type: Date,
      default: dateHelper.getCurrentDateTimeInParisZone(),
    },
    situationLastUpdateBy: String,
    program: {
      programId: String,
      programName: String,
      priority: Number,
    },
    typeOfTutor: [String],
    attendInformationMeeting: {
      type: Boolean,
      default: false,
    },
    attendInformationMeetingDate: Date,
    attendInformationMeeting: Boolean,
    facilityName: String,
    isPartner: {
      type: Boolean,
      default: false,
    },
    partner: {
      type: String,
    },
    assignment: {
      //list of strings
      tutoringType: [String],
      numberOfStudentsToSupport: Number,

      level: [
        {
          levelName: String,
          levelId: String,
        },
      ],
      strongAreas: String,
      establishments: [
        {
          establishmentName: String,
          establishmentId: String,
          isMissing: {
            type: Boolean,
            default: false,
          },
        },
      ],
      department: {
        departmentName: String,
        departmentId: String,
      },
      sectors: [
        {
          sectorName: String,
          sectorId: String,
        },
      ],
      department: {
        departmentName: String,
        departmentId: String,
      },
      vsc: [
        {
          vscName: String,
          vscId: String,
        },
      ],
    },
    invitation: {
      invitationCode: String,
      invitationAccepted: Boolean,
      invitationDate: {
        type: Date,
      },
      invitationAcceptedDate: {
        type: Date,
      },
    },

    activity: {
      establishments: [
        {
          establishmentName: String,
          // establishmentId: String,
        },
      ],
      ufrCurriculum: [String],
      company: [
        {
          establishmentName: String,
          establishmentId: String,
        },
      ],
      fieldOfActivity: String,
      areaOfStudy: String,
      comments: String,
      student: {
        higherEducationEstablishment: [
          {
            establishmentName: String,
            establishmentId: String,
            isMissing: {
              type: Boolean,
              default: false,
            },
          },
        ],
        ufrCurriculum: [String],
        areaOfStudy: String,
        employeeRetireeOrJobSeeker: String,
      },
      tutorActivityStatus: String,
    },
    status: {
      type: String,
      default: tutorConstants.statusOfTutors.TUTOR_ON_HOLD,
    },
    commitment: {
      startDate: {
        type: Date,
        default: dateHelper.getCurrentDateTimeInParisZone(),
      },
      endDate: Date,
      preemptiveEndDate: Date,
      motiveForPreemptiveEnd: String,
    },
    availability: [
      {
        dayOfTheWeek: Number,
        startTime: String,
        endTime: String,
        taken: Boolean,
        fullName : String,
      },
    ],
    subjectToTeach: String,
    engagementLetter: String,
    howDidYouHearAboutUs: String,
    howDidYourHeardAboutUsDetails: String,
    isYourCommitmentValuedByYourInstitution: {
      type: Boolean,
      default: false,
    },
    documents: [
      {
        documentName: String,
        downloadUrl: String,
        previewUrl: String,
        documentType: String,
        documentId: String,
        fileType: String,
        fileSize: String,
      },
    ],
    education: [
      {
        school: String,
        degree: String,
        fieldOfStudy: String,
        from: Date,
        to: Date,
        current: Boolean,
        description: String,
      },
    ],
    createdAt: {
      type: Date,
      immutable: true,
      default: () => Date.now(),
      // default: dateHelper.getCurrentDateTimeInParisZone(),
    },
    updatedAt: {
      type: Date,
    },
    LastUpdatedBy: {
      userId: String,
      firstName: String,
      lastName: String,
      email: String,
    },
    onBoardingStatus: {
      type: String,
      default: tutorConstants.onBoardingStatus.ON_BOARDING_IN_PROGRESS,
    },
    onBoardingStep: {
      type: String,
      default: tutorConstants.onBoardingStep.ON_BOARDING_STEP_1,
    },
    quizScore: {
      type: Number,
      default: 0,
    },
    attempts: {
      type: Number,
      default: 0,
    },
    currentVideo: {
      type: Number,
      default: 1,
    },
    matching: [
      {
        sessionId: String,
        studentIds: [String],
        status: String,
      },
    ],
    matchedStudents: {
      type: [
        {
          userId: String,
          fullName: String,
          absence: Boolean,
        },
      ],
      default: [],
    },
    nbrSession : Number, 
    note : Number, 
    nbrCr : Number, 
    lastSession : Object,
    futurSessions : Object,
    suiviCoordo: String,
    gender: String,
    typeOfUser: String,
    homeDepartment: {
      departmentName: String,
      departmentId: String,
    },
    iAcceptToBeContactedForParticipatingInAStudy: {
      type: Boolean,
      default: false,
    },
  },

  {
    versionKey: false,
  }
);

module.exports = mongoose.model("tutorPreferences", tutorPreferencesSchema);
