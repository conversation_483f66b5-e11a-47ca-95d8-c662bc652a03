const mongoose = require("mongoose");
require("dotenv").config();

let isConnected = false;

const connectToMongoDB = async () => {
  if (isConnected || mongoose.connection.readyState === 1) {
    console.log("MongoDB is already connected.");
    isConnected = true;
    return;
  }

  const connectionStrings = {
    production: process.env.MONGODB_CONNECTION_STRING_PROD,
    preprod: process.env.MONGODB_CONNECTION_STRING_PREPROD,
    development: process.env.MONGODB_CONNECTION_STRING_DEV,
  };

  const connectionString =
    connectionStrings[process.env.NODE_ENV] ||
    process.env.MONGODB_CONNECTION_STRING_DEV;

  try {
    mongoose.set("strictQuery", false);

    await mongoose.connect(connectionString, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      socketTimeoutMS: 45000,
      maxPoolSize: 700,
      ssl: true,
      tls: true,
      tlsAllowInvalidCertificates: true,
      tlsAllowInvalidHostnames: true,
    });

    isConnected = true;
    console.log(`Connected to MongoDB (${process.env.NODE_ENV || "Dev"})`);
  } catch (err) {
    console.error("MongoDB connection error:", err);
    isConnected = false;
  }

  mongoose.connection.on("connected", () => {
    console.log("MongoDB connected.");
  });

  mongoose.connection.on("disconnected", () => {
    console.log("MongoDB disconnected.");
    isConnected = false;
  });

  mongoose.connection.on("error", (err) => {
    console.error("MongoDB error:", err);
  });
};

const closeMongoDBConnection = async () => {
  if (mongoose.connection.readyState !== 0) {
    try {
      await mongoose.disconnect();
      console.log("MongoDB connection closed.");
    } catch (err) {
      console.error("Error closing MongoDB connection:", err);
    }
  }
};

process.on("SIGINT", async () => {
  await closeMongoDBConnection();
  process.exit(0);
});

module.exports = {
  connectToMongoDB,
  closeMongoDBConnection,
};
