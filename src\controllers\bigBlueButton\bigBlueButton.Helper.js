//import BigBlueButton.Room.Model.js
const bigBlueButtonRoomModel = require("../../models/BigBlueButton.Room.Model.js");

//import Init.BigBlueButton.js from middleware/bigbluebutton
const initBigBlueButton = require("../../middleware/bigbluebutton/Init.BigBlueButton.js");

//import axios
const axios = require("axios");

const xmlJs = require("xml-js");

//import appConstants
const bigBlueButtonConstants = require("../../utils/constants/bigbluebutton.constans.js");

//import appConstants
const userRoleConstants = require("../../utils/constants/userRolesConstants.js");
const { apiConstants } = require("../apiresponse/ApiResponseHelper.js");

//import dotenv
require("dotenv").config();

//import Api.Helper.js
const apiHelper = require("../../middleware/apiHelper/Api.Helper.js");
const xmlToJsonHelper = require("../../utils/xmlToJson/xmlToJson.helper.js");

//get string from object params and add to string method name
exports.getStringFromObjectParams = (object, methodName) => {
  let fullQueryString;
  if (object) {
    let str = Object.entries(object)
      .map(([key, val]) => `${key}=${val}`)
      .join("&");

    fullQueryString = `${methodName}${str}`;
  } else {
    fullQueryString = `${methodName}`;
  }

  return fullQueryString;
};

//setup function to create a new room
exports.createNewRoomInMongoDB = async (roomName, welcomeMessage) => {
  //generate a random meetingID from roomName and random 10 digits
  const meetingID = `${
    bigBlueButtonConstants.TWO_CHARACTERS_ROOM_NAME
  }-${Math.floor(Math.random() * 10000000000)}`;

  //bigBlueButtonConstants.DEFAULT_ATTENDEE_PASSWORD add 5 random digits
  const attendeePW = `${
    bigBlueButtonConstants.DEFAULT_ATTENDEE_PASSWORD
  }-${Math.floor(Math.random() * 100000)}`;

  //generate a random moderatorPW from roomName and random 5 digits
  const moderatorPW = `${
    bigBlueButtonConstants.DEFAULT_MODERATOR_PASSWORD
  }-${Math.floor(Math.random() * 100000)}`;

  //create a new room
  const newRoom = new bigBlueButtonRoomModel({
    meetingID: meetingID,
    name: roomName,
    welcome: welcomeMessage,
    attendeePW: attendeePW,
    moderatorPW: moderatorPW,
  });
  return newRoom;
};

//get Room info from bigBlueButton server
exports.getRoomInfo = async (meetingID) => {
  const queryParams = {
    meetingID: meetingID,
  };

  const fullUrl = this.getFullUrl(
    queryParams,
    bigBlueButtonConstants.API_METHOD_NAME_GET_MEETING_INFO
  );
  const roomInfo = await axios.get(fullUrl);

  return roomInfo.data.response;
};

//Check if room is running in bigBlueButton server by meetingID and if it's not  create it
exports.checkIfRoomIsRunning = async (meetingID) => {
  try {
    const queryParams = {
      meetingID: meetingID,
    };

    const fullUrl = this.getFullUrl(
      queryParams,
      bigBlueButtonConstants.API_METHOD_NAME_GET_MEETING_INFO
    );
    //https://zupdeco-bbb.azwedo.com/bigbluebutton/api/getMeetingInfo?meetingID=az-7294174759&checksum=5cff64ded7cbd919764d51cbac813f27037625e9
    const roomInfo = await apiHelper.makeGetRequest(fullUrl);
    console.log(`method: checkIfRoomIsRunning, roomInfo: ${roomInfo}`);
    //if room is not running, create it
    if (roomInfo.messageKey === "notFound") {
      const room = await this.getRoomByMeetingID(meetingID);
      const newRomeInBigBlueButtonServer =
        await this.createNewRoomInBigBlueButtonServer(room);

      return newRomeInBigBlueButtonServer.data;
    } else {
      return roomInfo;
    }
  } catch (error) {}
};

//create a new room in bigBlueButton server
exports.createNewRoomInBigBlueButtonServer = async (newRoomDocument) => {
  const bigBlueButtonMethod = bigBlueButtonConstants.API_METHOD_NAME_CREATE;
  //newRoomDocument is a mongoose document, so we need to convert it to a plain object
  const newRoomObject = newRoomDocument.toObject();
  //get full url
  let fullUrl = this.getFullUrl(newRoomObject, bigBlueButtonMethod);

  const newRoom = await apiHelper.makeGetRequest(fullUrl);
  if (newRoom.returncode === "FAILED") {
    return { status: false, data: newRoom.data.response };
  } else if (newRoom.returncode === "SUCCESS") {
    let internalMeetingID = newRoom.internalMeetingID;
    newRoomDocument.internalMeetingID = internalMeetingID;
    try {
      await newRoomDocument.save();
      return { status: true, data: newRoom };
    } catch (error) {
      console.log(error);
      return { status: false, data: error };
    }
  }
};

//Save new room to mongodb
exports.saveNewRoom = async (newRoom) => {
  try {
    //check if room already exists in mongodb with the same meetingID
    const room = await bigBlueButtonRoomModel.findOne({
      meetingID: newRoom.meetingID,
    });

    //if room already exists, update it
    if (room) {
      room.name = newRoom.name;
      room.welcome = newRoom.welcome;
      room.attendeePW = newRoom.attendeePW;
      room.moderatorPW = newRoom.moderatorPW;
      const savedRoom = await room.save();
      return savedRoom;
    } else {
      //save new room to mongodb
      const savedRoom = await newRoom.save();
      return savedRoom;
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

//get Room by meetingID from mongodb
exports.getRoomByMeetingID = async (meetingID) => {
  const room = await bigBlueButtonRoomModel.findOne({ meetingID: meetingID });
  return room;
};

//getRecordings from bigBlueButton server by meetingID and return one base on internalMeetingID
exports.getRecordings = async (meetingID, internalMeetingID) => {
  console.log(
    `Get recordings for meetingID: ${meetingID} and internalMeetingID: ${internalMeetingID}`
  );
  try {
    const fullUrl = this.getFullUrl(
      "",
      bigBlueButtonConstants.API_METHOD_NAME_GET_RECORDINGS
    );

    //get recordings from bigBlueButton server with axios and convert to json with xml2js
    const recordings = await axios.get(fullUrl);
    const recordingsJson = await xmlJs.parseStringPromise(recordings.data);

    return recordingsJson.response.recordings[0].recording.find((recording) => {
      return (
        recording.meetingID[0] === meetingID &&
        recording.internalMeetingID[0] === internalMeetingID
      );
    });
  } catch (error) {
    let response = {
      status: false,
      message: `Failed to get recordings from bigBlueButton server: ${error}`,
    };
    return response;
  }
};

//Get joinUrl to join a room
exports.generateJoinURL = (roomDocument, userDocument) => {
  //if userRole is moderator, use moderatorPW, else use attendeePW
  const firstName = userDocument.contactDetails.firstName;
  const lastName = userDocument.contactDetails.lastName;
  const fullName = `${firstName} ${lastName}`;
  const queryParams = {
    meetingID: roomDocument.meetingID,
    fullName: fullName,
    avatarURL: userDocument.profilePic,
  };
  if (userDocument.userRole === userRoleConstants.ROLE_TUTOR) {
    queryParams.password = roomDocument.moderatorPW;
  } else {
    queryParams.password = roomDocument.attendeePW;
  }
  queryParams.userID = userDocument.userId;

  const joinUrl = this.getFullUrl(queryParams, "join");
  return joinUrl;
};

//setup function to create a webhooks in bigBlueButton server
exports.createWebhooks = async (calllbackURL) => {
  const queryParams = {
    callbackURL: calllbackURL,
  };

  const fullUrl = this.getFullUrl(
    queryParams,
    bigBlueButtonConstants.API_METHOD_NAME_CREATE_WEBHOOKS
  );
  let url = new URL(fullUrl);

  const webhook = await apiHelper.makeGetRequest(url);

  let responseData = {
    status: true,
    message: "Webhooks created successfully",
    data: webhook,
  };
  return responseData;
};

//delete webhooks from bigBlueButton server
exports.deleteWebhook = async (hookID) => {
  const queryParams = {
    hookID: hookID,
  };

  const fullUrl = this.getFullUrl(
    queryParams,
    bigBlueButtonConstants.API_METHOD_NAME_DELETE_WEBHOOKS
  );
  let url = new URL(fullUrl);

  const webhook = await apiHelper.makeGetRequest(url);

  let responseData = {
    status: true,
    message: "Webhooks deleted successfully",
    data: webhook,
  };
  return responseData;
};

exports.getHooksList = async () => {
  const fullUrl = this.getFullUrl(
    null,
    bigBlueButtonConstants.API_METHOD_NAME_GET_HOOKS_LIST
  );

  const hooksData = await apiHelper.makeGetRequest(fullUrl);

  let hooksList = [];
  if (hooksData.returncode === "SUCCESS") {
    if (Array.isArray(hooksData.hooks.hook)) {
      hooksData.hooks.hook.forEach((hook) => {
        let hookData = {
          hookID: hook.hookID._text,
          callbackURL: hook.callbackURL._cdata,
          permanentHook: hook.permanentHook._text,
          rawData: hook.rawData._text,
        };
        hooksList.push(hookData);
      });
      return {
        status: true,
        message: "Hooks list fetched successfully",
        data: hooksList,
      };
    } else if (hooksData.hooks.hook) {
      const hook = hooksData.hooks.hook;
      let hookData = {
        hookID: hook.hookID._text,
        callbackURL: hook.callbackURL._cdata,
        permanentHook: hook.permanentHook._text,
        rawData: hook.rawData._text,
      };
      hooksList.push(hookData);
      return {
        status: true,
        message: "Hooks list fetched successfully",
        data: hooksList,
      };
    } else {
      return {
        status: true,
        message: "Hooks list fetched successfully",
        data: hooksList,
      };
    }
  } else {
    return { status: false, message: "Failed to fetch hooks list", data: null };
  }
};

//Get full url to make a request to bigBlueButton server
exports.getFullUrl = (objectParam, methodName) => {
  let fullQueryString = this.getStringFromObjectParams(objectParam, methodName);

  //generate checksum
  let checksum = initBigBlueButton.generateChecksum(encodeURI(fullQueryString));

  //remove bigBlueButtonMethod from fullQueryString, because it is not needed in the request is in the first part of the url
  let queryString = fullQueryString.replace(methodName, "");
  //setup url
  let url;
  if (queryString) {
    url = `${process.env.BIGBLUEBUTTON_URL}${methodName}?${queryString}&checksum=${checksum}`;
  } else {
    url = `${process.env.BIGBLUEBUTTON_URL}${methodName}?checksum=${checksum}`;
  }

  //url to utf8
  url = encodeURI(url);
  return url;
};

//get meeting info
exports.getMeetingInfo = async (meetingID) => {
  const objectParam = {
    meetingID: meetingID,
    password: "mp",
  };

  const objectParamString = this.getStringFromObjectParams(
    objectParam,
    "getMeetingInfo"
  );

  let checksum = initBigBlueButton.generateChecksum(objectParamString);

  let queryString = Object.keys(objectParam)
    .map((key) => key + "=" + objectParam[key])
    .join("&");

  //setup url
  let url =
    process.env.BIGBLUEBUTTON_URL +
    "getMeetingInfo?" +
    queryString +
    "&checksum=" +
    checksum;

  try {
    const response = await axios.get(url);

    //check if response is xml
    if (response.headers["content-type"] === "text/xml;charset=utf-8") {
      let dataJson = xmlToJsonHelper.xmlToJson(response.data);
      return dataJson.response;
    } else {
      return response.data;
    }
  } catch (error) {
    console.log("error: ", error);
    throw error; // Rethrow the error so the caller can handle it
  }
};
