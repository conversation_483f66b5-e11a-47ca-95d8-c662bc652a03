//import xml-js
const xmlJs = require("xml-js");

//import dotenv
require("dotenv").config();

function xmlToJson(xml) {
     let options = {
          compact: true,
          trim: true,
          ignoreDeclaration: true,
          ignoreInstruction: true,
          ignoreAttributes: true,
          ignoreComment: true,
          ignoreCdata: true,
          ignoreDoctype: true,
          textFn: removeJsonTextAttribute,
     };
     let json = xmlJs.xml2json(xml, options);

     //parse json
     let parsedJson = JSON.parse(json);

     return parsedJson;
}

function nativeType(value) {
     var nValue = Number(value);
     if (!isNaN(nValue)) {
          return nValue;
     }
     var bValue = value.toLowerCase();
     if (bValue === "true") {
          return true;
     } else if (bValue === "false") {
          return false;
     }
     return value;
}

var removeJsonTextAttribute = function (value, parentElement) {
     try {
          var keyNo = Object.keys(parentElement._parent).length;
          var keyName = Object.keys(parentElement._parent)[keyNo - 1];
          parentElement._parent[keyName] = nativeType(value);
     } catch (e) {}
};

//export module
module.exports = {
     xmlToJson: xmlToJson,
};
