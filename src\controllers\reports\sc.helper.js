const scReportModel = require("../../models/Sc.Report.Model.js");
const UserAdminsModel = require("../../models/User.Administration.Model.js");

const mongoose = require("mongoose");

const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const moment = require("moment-timezone");

const reportConstants = require("../../utils/constants/reports.constants.js");
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const userAdministrationHelper = require("../user/user.administration.helper.js");

//create a new SC report
exports.createScReport = async (scReport) => {
  try {
    const newScReport = new scReportModel(scReport);
    const newReport = await newScReport.save();

    if (newReport) {
      return {
        status: true,
        message: `New SC report created successfully`,
        data: newReport,
      };
    } else {
      return {
        status: false,
        message: `New SC report not created`,
        data: null,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in creating new SC report ${error.message}`,
      data: null,
    };
  }
};

//update SC report
exports.updateScReport = async (scReport) => {
  try {
    const scReportId = scReport._id;
    const objectId = mongoose.Types.ObjectId(scReportId);

    const updatedScReport = await scReportModel
      .findOneAndUpdate({ _id: objectId }, scReport, { new: true })
      .exec();

    if (updatedScReport) {
      return {
        status: true,
        message: `SC report updated successfully`,
        data: updatedScReport,
      };
    } else {
      return { status: false, message: `SC report not updated`, data: null };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in updating SC report ${error.message}`,
      data: null,
    };
  }
};

//get SC report by _id
exports.getScReportById = async (reportId) => {
  try {
    const objectId = mongoose.Types.ObjectId(reportId);

    const report = await scReportModel.findOne({ _id: objectId }).exec();

    if (report) {
      return { status: true, message: `SC report found`, data: report };
    } else {
      return { status: false, message: `SC report not found`, data: null };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting SC report by id ${error.message}`,
      data: null,
    };
  }
};

//delete SC report by _id
exports.deleteScReportById = async (reportId) => {
  try {
    const objectId = mongoose.Types.ObjectId(reportId);
    const deletedReport = await scReportModel
      .findOneAndDelete({ _id: objectId })
      .exec();

    if (deletedReport) {
      return {
        status: true,
        message: `SC report deleted successfully`,
        data: deletedReport,
      };
    } else {
      return { status: false, message: `SC report not deleted`, data: null };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in deleting SC report by id ${error.message}`,
      data: null,
    };
  }
};

//DASHBOARD
exports.dashboard = async (payload) => {
  try {
    let { page, pageSize, filter, sortBy, userId, userRole } = payload;
    let applyFilter = buildFilter(filter);
    if (!applyFilter.status) {
      return { status: false, message: applyFilter.message, data: null };
    }
    applyFilter = applyFilter.data;

    sortBy = buildSortBy(sortBy);

    let skip = (page - 1) * pageSize;
    // Check if the user is an admin and his role in [coordinator or vsc]
    if (
      userRolesConstants.COORDINATOR === userRole ||
      userRole === userRolesConstants.VSC
    ) {
      // Check if the user role is VSC, then get the reports of the VSC by userId
      // get the admin preferences model of the user
      // const userAdminDetails = await UserAdminsModel.findOne({ userId });
      // // get the program preferences of the user
      // const pref = userAdminDetails?.administrationPreferences;
      // check if the user has program preferences
      let vsclist = [];
      if (userRolesConstants.COORDINATOR === userRole) {
        vsclist = await UserAdminsModel.find({
          "administrationPreferences.coordinators.coordinatorId": userId,
          userRole: userRolesConstants.VSC,
        });
        if (vsclist.length > 0) {
          vsclist = vsclist.map((vsc) => vsc.userId);
        }
      }
      applyFilter["vsc.userId"] =
        userRole === userRolesConstants.VSC ? userId : { $in: vsclist };
    }
    // if (pref && pref.program?.length && userRole === userRolesConstants.COORDINATOR) {
    //   const program = pref.program[0];
    //   // Déterminer le type de programme et récupérer les IDs des coordinateurs
    //   if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
    //     // Tutorats solidaire ou ZupdeFoot
    //     // Obtenir les IDs des établissements depuis le modèle des préférences admin
    //     const establishmentIds = pref.establishment.map(est => est.establishmentId);
    //     // Trouver les coordinateurs avec les établissements correspondants
    //     const listCoodro = db.userAdmin.find({
    //       'administrationPreferences.establishment.establishmentId': { $in: establishmentIds }
    //     }).toArray();

    //     coodroIds = listCoodro.map(coodro => coodro.userId);

    //   } else if ([HOME_CLASSES.programId, CLASSSES_HOME.programId, ZUPDEFOOT.programId].includes(program.programId)) {
    //     // Autres types de programmes
    //     const departmentIds = pref.department.map(dep => dep.departmentId);

    //     // Trouver les coordinateurs avec les programmes et départements correspondants
    //     let listCoodro = [];
    //     listCoodro = await UserAdminsModel.find({
    //       'administrationPreferences.program.programId': program.programId,
    //       'administrationPreferences.department.departmentId': { $in: departmentIds }
    //     });
    //     coodroIds = listCoodro.map(coodro => coodro.userId);
    //    }
    //   }
    //   // Appliquer le filtre basé sur les IDs des coordinateurs trouvés

    const reports = await scReportModel
      .find(applyFilter)
      .sort(sortBy)
      .skip(skip)
      .limit(pageSize)
      .exec();
    const totalCount = await scReportModel.countDocuments(applyFilter).exec();

    return {
      status: true,
      message: `SC report found`,
      data: reports,
      total: totalCount,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting SC report by id ${error.message}`,
      data: null,
    };
  }
};

//get Sc Report by vscUserId
exports.getScReportByVscUserId = async (vscUserId) => {
  try {
    const listOfScReports = await scReportModel
      .find({ "vsc.userId": vscUserId })
      .exec();

    return { status: true, message: `SC report found`, data: listOfScReports };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting SC report by id ${error.message}`,
      data: null,
    };
  }
};

//check if the Sc Report is exist for the vscUserId and week between the start and end date
async function checkIfScReportExist(vscUserId, startDate, endDate) {
  try {
    const query = {
      "vsc.userId": vscUserId,
      creation_date: { $gte: startDate, $lte: endDate },
    };
    const listOfScReports = await scReportModel.find(query).exec();

    if (listOfScReports?.length) {
      return {
        status: true,
        message: `SC report found`,
        data: listOfScReports,
      };
    } else {
      return { status: false, message: `SC report not found`, data: null };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in getting SC report by id ${error.message}`,
      data: null,
    };
  }
}

//create a new SC report with default values for the week, input parameters are userId, cordinatorUserId
async function createScReportWithDefaultValues(
  vsc,
  creation_date,
  coordinator
) {
  try {
    const newScReport = new scReportModel({
      vsc: vsc,
      creation_date: creation_date,
      coordinator: coordinator,
      status: reportConstants.crScReportsStatus.toBeEntered.crScReportsStatusId,
    });

    const newReport = await newScReport.save();

    if (newReport) {
      return {
        status: true,
        message: `New SC report created successfully`,
        data: newReport,
      };
    } else {
      return {
        status: false,
        message: `New SC report not created`,
        data: null,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: `Error in creating new SC report ${error.message}`,
      data: null,
    };
  }
}

function buildFilter(filter) {
  let applyFilter = {};

  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (key == "tutorName") {
      applyFilter["tutors"] = {
        $elemMatch: {
          fullName: {
            $regex: value,
            $options: "i",
          },
        },
      };
      delete applyFilter.fullName;
    } else if (key == "studentName") {
      applyFilter["students"] = {
        $elemMatch: {
          fullName: {
            $regex: value,
            $options: "i",
          },
        },
      };
      delete applyFilter.fullName;
    } else if (key == "studentsAttendance") {
      applyFilter["students"] = {
        $elemMatch: {
          absence: value,
        },
      };
      delete applyFilter.studentsAttendance;
    } else if (key == "byDate") {
      const byDate = value;
      applyFilter["creation_date"] = {
        $gte: new Date(byDate.startDate),
        $lte: new Date(byDate.endDate),
      };
      delete applyFilter.byDate;
    } else if (key == "coordinatorName") {
      applyFilter["coordinator.coordinatorFullName"] = {
        $regex: value,
        $options: "i",
      };
      delete applyFilter.coordinatorName;
    } else if (key == "vscName") {
      applyFilter["vsc.vscFullName"] = { $regex: value, $options: "i" };
      delete applyFilter.vscName;
    } else if (key === "status") {
      applyFilter["status"] = value;
    }
  }

  //filter with value string RegExp
  /* for (const [key, value] of Object.entries(applyFilter)) {
          if (typeof value == "string") {
               let regex = new RegExp(value, "i");
               applyFilter[key] = regex;
          }
     } */
  return { status: true, data: applyFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["sessionDate.startDate", -1]];
  }
  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

//create a function to generate the SC reports
exports.generateWeeklyScReports = async () => {
  try {
    //get list of VSCs from the database
    // Obtenir le jour de la semaine en nombre (0 = dimanche, 1 = lundi, ..., 6 = samedi)
    const dayOfWeek = moment().day() - 1;
    const listOfVsc = await userAdministrationHelper.getAllActiveVSCs(
      dayOfWeek
    );
    if (listOfVsc.status) {
      //for each VSC, generate a report
      for (let index = 0; index < listOfVsc.data.length; index++) {
        const vscUserDetails = listOfVsc.data[index];
        await checkIfScReportIsCreatedForThisWeek(vscUserDetails);
      }
    }
  } catch (error) {
    console.log(error);
    //  logger.newLog(logger.getLoggerTypeConstants().crScReportsJob).alert(`Error in generateWeeklyScReports: ${error}`);
  }
};

//Check is SC report is created for this week, if not create it
async function checkIfScReportIsCreatedForThisWeek(vscUserDetails) {
  try {
    const vscUserId = vscUserDetails.userId;

    const vscFullName =
      vscUserDetails?.contactDetails?.firstName +
      " " +
      vscUserDetails?.contactDetails?.lastName;

    moment.updateLocale("fr", {
      week: {
        dow: 1, // Monday
      },
    });
    //get full date and time
    const fullDateAndTime = moment().tz("Europe/Paris");

    //get start time of the week and end time of the week
    const startWeek = fullDateAndTime.startOf("day").format();

    const endWeek = fullDateAndTime.endOf("day").format();
    //check if SC report is created for this week
    const scReportExists = await checkIfScReportExist(
      vscUserId,
      startWeek,
      endWeek
    );

    if (scReportExists.status) {
      console.log(
        `Sc report already exists 😄✅ for this week for the user:${vscFullName} | userId:${vscUserId}`
      );
    } else {
      const vscObject = {
        vscFullName: vscFullName,
        userId: vscUserId,
      };

      //list of coordinators from vscUserDetails
      const listOfCoordinators =
        vscUserDetails?.administrationPreferences?.coordinators;
      const firstCoordinator = listOfCoordinators[0];

      let coordinatorObject = {};
      if (firstCoordinator) {
        coordinatorObject = {
          coordinatorFullName: firstCoordinator.coordinatorName,
          userId: firstCoordinator.coordinatorId,
        };
      }

      console.log(
        `Sc report does not exist 😟🚨 for this week for the user:${vscFullName} | userId:${vscUserId}`
      );
      await createScReportWithDefaultValues(
        vscObject,
        endWeek,
        coordinatorObject
      );
    }
  } catch (error) {
    console.log(error);
    //  logger.newLog(logger.getLoggerTypeConstants().crScReportsJob).alert(`Error in checkIfScReportIsCreatedForThisWeek: ${error}`);
    return {
      status: false,
      message: `Error in checkIfScReportIsCreatedForThisWeek: ${error.message}`,
      data: null,
    };
  }
}
