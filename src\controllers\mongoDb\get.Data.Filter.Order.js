const mongoose = require("mongoose");

const sectorsHelper = require("../sectors/sectors.helper.js");
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
} = require("../../utils/constants/program.constants.js");

exports.getDataWithPaginationAndFilterAndOrder = async (payload) => {
  try {
    let {
      mongooseModel,
      filter,
      sortBy,
      search,
      pageSize,
      page,
      userId,
      userRole,
    } = payload;
    let applyFilter = {};
    let applyFilterData = await buildFilter(filter, mongooseModel);
    if (!applyFilterData.status) {
      return { status: false, message: applyFilter.message };
    }
    //check if the applyFilter is empty object or not
    if (Object.keys(applyFilterData.data).length !== 0) {
      applyFilter = applyFilterData.data;
    } else {
      if (applyFilterData.coordExists === true) {
        // applyFilter = { _id: null }
        let dataObject = {
          listOfData: [],
          totalCount: 0,
          pageSize: pageSize,
          page: page,
        };
        return { status: true, data: dataObject };
      } else {
        applyFilter = {};
      }
    }

    pageSize = buildPageSize(pageSize);

    page = buildPage(page);

    sortBy = buildSortBy(sortBy);

    // if (
    //   userId &&
    //   userRole &&
    //   [userRolesConstants.COORDINATOR, userRolesConstants.VSC].includes(
    //     userRole
    //   )
    // ) {
    //   const userAdminDetails = await UserAdministrationModel.findOne({
    //     userId,
    //   });
    //   const pref = userAdminDetails.administrationPreferences;

    //   if (pref && pref.program?.length) {
    //     const program = pref.program[0];
    //     if (
    //       [DEVOIRS_FAITS.programId, ZUPDEFOOT.programId].includes(
    //         program.programId
    //       )
    //     ) {
    //       //Tutorat solidaire
    //       const establishmentIds = pref.establishment.map(
    //         (est) => new mongoose.Types.ObjectId(est.establishmentId)
    //       );
    //       if (!establishmentIds?.length) {
    //         return {
    //           status: true,
    //           data: { listOfData: [], totalCount: 0, pageSize, page },
    //         };
    //       } else {
    //         //get estabishement details buy establishmentIds from educ annuaire
    //         applyFilter = { ...applyFilter, _id: { $in: establishmentIds } };
    //       }
    //     } else if (program.programId === HOME_CLASSES.programId) {
    //       // Home Classe
    //       const departmentId = pref.department.departmentId;
    //       if (!departmentId) {
    //         return {
    //           status: true,
    //           data: { listOfData: [], totalCount: 0, pageSize, page },
    //         };
    //       } else {
    //         applyFilter["generalInformation.address.postalCode"] = {
    //           $regex: "^" + departmentId,
    //           $options: "i",
    //         };
    //       }
    //     }
    //   } else {
    //     applyFilter = { _id: null };
    //   }
    // }
    let data;
    if (mongooseModel.modelName === "sector") {
      const pipeline = [
        {
          $lookup: {
            from: "useradmins", // The name of the user admins collection
            let: { sectorCode: "$code" },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      {
                        $in: [
                          "$$sectorCode",
                          {
                            $map: {
                              input: {
                                $ifNull: [
                                  "$administrationPreferences.department",
                                  [],
                                ],
                              },
                              as: "dept",
                              in: "$$dept.departmentId",
                            },
                          },
                        ],
                      },
                      { $eq: ["$userRole", "coordinator"] },
                    ],
                  },
                },
              },
              {
                $project: {
                  coordinatorName: {
                    $concat: [
                      "$contactDetails.firstName",
                      " ",
                      "$contactDetails.lastName",
                    ],
                  },
                  userId: 1,
                },
              },
            ],
            as: "coordinatorDetails",
          },
        },
        {
          $addFields: {
            coordinators: {
              $map: {
                input: {
                  $ifNull: ["$coordinatorDetails", []],
                },
                as: "coordinator",
                in: {
                  coordinatorName: "$$coordinator.coordinatorName",
                  userId: "$$coordinator.userId",
                },
              },
            },
          },
        },
        {
          $project: {
            coordinatorDetails: 0, // Exclude the intermediate field if not needed
          },
        },
      ];

      // Execute the pipeline using your MongoDB driver, e.g., with the Node.js MongoDB driver

      // Execute the pipeline using your MongoDB driver, e.g., with the Node.js MongoDB driver
      const result = await mongooseModel.aggregate(pipeline, {
        allowDiskUse: true,
      });
      data = result;
    } else {
      // const sortBy = {establishmentName: 1};
      const pipeline = [
        {
          $match: applyFilter // Filtrer les documents selon les critères donnés
        },
        {
          $sort: { establishmentName: 1 } // Trier les documents par `establishmentName` en ordre croissant
        },
        {
          $skip: pageSize * (page - 1) // Sauter les documents pour la pagination
        },
        {
          $limit: pageSize // Limiter le nombre de documents retournés
        }
      ];
      // Exécuter l'agrégation avec Mongoose
       data = await mongooseModel.aggregate(pipeline).allowDiskUse(true).exec();
      
    }
    //get total count of data in the collection
    let totalCount = await mongooseModel.countDocuments(applyFilter).exec();

    let dataObject = {
      listOfData: data,
      totalCount: totalCount,
      pageSize: pageSize,
      page: page,
    };

    return { status: true, data: dataObject };

    //  applyFilter = buildSearchFilter(mongooseModel, search, applyFilter);

    //start to make request
  } catch (error) {
    console.log("error", error);
    return { status: false, message: `${error}` };
  }
};

function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null) {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null) {
    if (typeof page === "string") {
      page = JSON.parse(page).page;
    }
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }

  return page;
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);

    //check if sortBy is empty or not
    for (const [key, value] of Object.entries(sortByObject)) {
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["createdAt", -1]];
  }

  return sortBy;
}

async function buildFilter(filter, mongooseModel) {
  try {
    let applyFilter = {};
    const mongooseModelName = mongooseModel.modelName;
    //check if the filter is json or not
    if (filter != undefined && filter != null && filter != "") {
      if (!isJSON(filter)) {
        return { status: false, message: "Filter is not a valid Json " };
      } else {
        applyFilter = JSON.parse(filter);
      }
    }

    if (
      !applyFilter?.establishmentName?.length &&
      !applyFilter?.coordinatorUserId &&
      !applyFilter?.coordinators &&
      !applyFilter?.city &&
      !applyFilter?.sectorId &&
      !applyFilter?.typeOfEstablishment
    ) {
      return { status: true, data: {} };
    }
    const coordExists =
      !!applyFilter?.coordinatorUserId?.length || !!applyFilter?.coordinators;
    if (
      applyFilter?.coordinators &&
      typeof applyFilter?.coordinators === "string"
    ) {
      applyFilter.coordinators = JSON.parse(applyFilter?.coordinators);
    }
    if (
      applyFilter?.coordinatorUserId &&
      typeof applyFilter?.coordinatorUserId === "string"
    ) {
      applyFilter.coordinatorUserId = [applyFilter?.coordinatorUserId];
    }
    //check if the applyFilter object contain coordinatorName, if yes  call updateFilterKeyForSectors function
    if (applyFilter.coordinatorUserId != undefined) {
      applyFilter = updateFilterKeyForSectors(applyFilter);
    }

    if (applyFilter.city !== undefined) {
      if (applyFilter.city != "") {
        applyFilter["generalInformation.address.city"] = {
          $regex: applyFilter.city,
          $options: "i",
        };
        delete applyFilter.city;
      } else {
        delete applyFilter.city;
      }
    }

    if (applyFilter.sectorId != undefined) {
      if (mongooseModelName == "sector") {
        if (applyFilter.sectorId != "") {
          let sectorId = mongoose.Types.ObjectId(applyFilter.sectorId);
          applyFilter["_id"] = sectorId;
        } else {
          delete applyFilter.sectorId;
        }
      }

      if (mongooseModelName == "educationannuaires") {
        if (applyFilter.sectorId != "") {
          //update filter for establishmentSector
          applyFilter["generalInformation.address.postalCode"] = {
            $regex: "^" + applyFilter.sectorId,
            $options: "i",
          };
          delete applyFilter.sectorId;
        } else {
          delete applyFilter.sectorId;
        }
      }
    }
    if (applyFilter.typeOfEstablishment != undefined) {
      if (applyFilter.typeOfEstablishment != "") {
        //update filter for establishmentSector
        applyFilter["typeOfEstablishment.typeOfEstablishmentId"] =
          String(applyFilter.typeOfEstablishment);
        delete applyFilter.typeOfEstablishment;
      } else {
        delete applyFilter.typeOfEstablishment;
      }
    }

    if (mongooseModelName == "educationannuaires") {
      if (applyFilter["coordinator.userId"] || applyFilter["coordinators"]) {
        let value =
          applyFilter["coordinator.userId"] ?? applyFilter["coordinators"];
        if (value && typeof value === "string") {
          // applyFilter["coordinators.userId"] = value;
        }

        const sectorsIds = await sectorsHelper.getSectorIdsByCoordinatorIds(
          value
        );
        if (sectorsIds?.data?.length) {
          applyFilter["generalInformation.sector.sectorId"] = {
            $in: sectorsIds.data,
          };
        }
        // applyFilter["coordinators"]
        //   ? delete applyFilter["coordinators"]
        //   : delete applyFilter["coordinators.userId"];
      } else {
        delete applyFilter.coordinatorId;
      }
    }

    if (applyFilter.department) {
      applyFilter["generalInformation.address.postalCode"] = {
        // the "^" is to search for the string that starts with the department code
        $regex: "^" + applyFilter.department,
        $options: "i",
      };
      delete applyFilter.department;
    }
    //establishmentId
    if (applyFilter.establishmentName !== undefined) {
      //check if establishmentId is not empty or null
      if (applyFilter.establishmentName !== "") {
        applyFilter["establishmentName"] = 
        { $regex: new RegExp(applyFilter.establishmentName, 'i')}
      } else {
        delete applyFilter.establishmentName;
      }
    }

    //filter with value string RegExp
    // for (const [key, value] of Object.entries(applyFilter)) {
    //      if (typeof value == "string") {
    //           let regex = new RegExp(value, "i");
    //           applyFilter[key] = regex;
    //      }
    // }
    return { status: true, data: applyFilter, coordExists };
  } catch (error) {
    console.log("error", error);
    return { status: false, message: `${error}` };
  }
}

//make function to check if Filter is contain coordinatorName and update the key to coordinators.coordinatorName
function updateFilterKeyForSectors(filterObject) {
  for (const [key, value] of Object.entries(filterObject)) {
    //Check if the key is coordinatorName, update the key value to coordinators.coordinatorName
    if (key == "coordinatorUserId") {
      if (value != "") {
        const coordinatorUserId = value;
        //regex to search for coordinators.coordinatorName
        filterObject["coordinator.userId"] = coordinatorUserId[0];
        delete filterObject[key];
      } else {
        delete filterObject[key];
      }
    } else if (key == "firstName") {
      let regexValue = new RegExp(value, "i");
      filterObject["contactDetails.firstName"] = regexValue;
      delete filterObject[key];
    }
  }
  applyFilter = filterObject;
  return filterObject;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}
