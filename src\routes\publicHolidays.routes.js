const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("publicHolidays.routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import PublicHolidays Model
const publicHolidays = require("../models/Public.Holidays.Model.js");

//import checkUserRole.js from path src/middleware/apiAuth/checkUser.Role.js
const checkUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");
const { SUPER_ADMINISTRATOR, COORDINATOR, MANAGER } = require("../utils/constants/userRolesConstants.js");

// check user role and access level
router.use((req, res, next) => {
     verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.PUBLIC_HOLIDAYS);
});

//save new PublicHolidays
/**
 * @swagger
 * /api/v1/publicHoliday:
 *   post:
 *     summary: Create a new public holiday
 *     tags: [Public Holidays]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 description: The name of the public holiday
 *               date:
 *                 type: string
 *                 format: date
 *                 description: The date of the public holiday
 *               tutoringAllowed:
 *                 type: boolean
 *                 description: Indicates whether tutoring is allowed on the public holiday
 *               occursEveryYear:
 *                 type: boolean
 *                 description: Indicates whether the public holiday occurs every year
 *     responses:
 *       '200':
 *         description: Successfully created a new public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 data:
 *                   type: object
 *                   description: The newly created public holiday
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: The name of the public holiday
 *                     date:
 *                       type: string
 *                       format: date
 *                       description: The date of the public holiday
 *                     tutoringAllowed:
 *                       type: boolean
 *                       description: Indicates whether tutoring is allowed on the public holiday
 *                     occursEveryYear:
 *                       type: boolean
 *                       description: Indicates whether the public holiday occurs every year
 *       '400':
 *         description: Bad request or failed to create a new public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.post("/publicHoliday", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const publicHolidayName = fields.name;
               const publicHolidayDate = fields.date;
               const publicHolidayTutoringAllowed = fields.tutoringAllowed;
               const publicHolidayOccursEveryYear = fields.occursEveryYear;

               //check if publicHolidayName is provided
               if (!publicHolidayName) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `publicHolidayName is required. Please provide a valid publicHolidayName.`);
                    return res.status(400).json(response);
               }

               //check if publicHolidayDate is provided
               if (!publicHolidayDate) {
                    let response = apiResponse.responseWithStatusCode(
                         apiResponse.apiConstants.API_REQUEST_SUCCESS,
                         `publicHolidayDate is required. Please provide a publicHolidayDate because it is required.`
                    );
                    return res.status(400).json(response);
               }

               //new PublicHolidays
               const newPublicHoliday = new publicHolidays({
                    name: publicHolidayName,
                    date: publicHolidayDate,
                    tutoringAllowed: publicHolidayTutoringAllowed,
                    occursEveryYear: publicHolidayOccursEveryYear,
               });

               let savedPublicHoliday = await newPublicHoliday.save();

               if (!savedPublicHoliday) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Failed to save new Public Holiday. Please try again.`);
                    return res.status(400).json(response);
               } else {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `New Public Holiday saved successfully.`, savedPublicHoliday);
                    return res.status(200).json(response);
               }
          } catch (error) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to save new PublicHolidays", JSON.stringify(error));
               logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
               return res.status(500).json(response);
          }
     });
});

//get all PublicHolidays list
/**
 * @swagger
 * /api/v1/publicHolidays:
 *   get:
 *     summary: Get list of public holidays
 *     tags: [Public Holidays]
 *     responses:
 *       '200':
 *         description: Successfully retrieved the list of public holidays
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 data:
 *                   type: array
 *                   description: List of public holidays
 *                   items:
 *                     type: object
 *                     properties:
 *                       name:
 *                         type: string
 *                         description: The name of the public holiday
 *                       date:
 *                         type: string
 *                         format: date
 *                         description: The date of the public holiday
 *                       tutoringAllowed:
 *                         type: boolean
 *                         description: Indicates whether tutoring is allowed on the public holiday
 *                       occursEveryYear:
 *                         type: boolean
 *                         description: Indicates whether the public holiday occurs every year
 *       '400':
 *         description: No public holidays found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/publicHolidays", async (req, res) => {
     try {
          const publicHolidaysList = await publicHolidays.find({});

          if (!publicHolidaysList) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `No PublicHolidays found.`);
               return res.status(400).json(response);
          } else {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `PublicHolidays list fetched successfully.`, publicHolidaysList);
               return res.status(200).json(response);
          }
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to get PublicHolidays list", JSON.stringify(error));
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
          return res.status(500).json(response);
     }
});

//get PublicHolidays by id
/**
 * @swagger
 * /api/v1/publicHoliday:
 *   get:
 *     summary: Get public holiday by ID
 *     tags: [Public Holidays]
 *     parameters:
 *       - in: query
 *         name: publicHolidayId
 *         schema:
 *           type: string
 *         description: The ID of the public holiday to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved the public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 data:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: The name of the public holiday
 *                     date:
 *                       type: string
 *                       format: date
 *                       description: The date of the public holiday
 *                     tutoringAllowed:
 *                       type: boolean
 *                       description: Indicates whether tutoring is allowed on the public holiday
 *                     occursEveryYear:
 *                       type: boolean
 *                       description: Indicates whether the public holiday occurs every year
 *       '400':
 *         description: No public holiday found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/publicHoliday", async (req, res) => {
     try {
          const publicHolidayId = req.query.publicHolidayId;

          //check if publicHolidayId is provided
          if (!publicHolidayId) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `publicHolidayId is required. Please provide a valid publicHolidayId.`);
               return res.status(400).json(response);
          }

          const publicHoliday = await publicHolidays.findById(publicHolidayId);

          if (!publicHoliday) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `No PublicHoliday found.`);
               return res.status(400).json(response);
          } else {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `PublicHoliday fetched successfully.`, publicHoliday);
               return res.status(200).json(response);
          }
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to get PublicHolidays by id", error);
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response + error);
          return res.status(500).json(response);
     }
});

//update PublicHolidays
/**
 * @swagger
 * /api/v1/publicHoliday:
 *   put:
 *     summary: Update a public holiday
 *     tags: [Public Holidays]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               publicHolidayId:
 *                 type: string
 *                 description: The ID of the public holiday to update
 *               name:
 *                 type: string
 *                 description: The name of the public holiday
 *               date:
 *                 type: string
 *                 format: date
 *                 description: The date of the public holiday
 *               tutoringAllowed:
 *                 type: boolean
 *                 description: Indicates whether tutoring is allowed on the public holiday
 *               occursEveryYear:
 *                 type: boolean
 *                 description: Indicates whether the public holiday occurs every year
 *     responses:
 *       '200':
 *         description: Successfully updated the public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *                 data:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                       description: The updated name of the public holiday
 *                     date:
 *                       type: string
 *                       format: date
 *                       description: The updated date of the public holiday
 *                     tutoringAllowed:
 *                       type: boolean
 *                       description: Indicates whether tutoring is allowed on the public holiday
 *                     occursEveryYear:
 *                       type: boolean
 *                       description: Indicates whether the public holiday occurs every year
 *       '400':
 *         description: Failed to update the public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.put("/publicHoliday", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const publicHolidayId = fields.publicHolidayId;

               const publicHolidayName = fields.name;
               const publicHolidayDate = fields.date;
               const publicHolidayTutoringAllowed = fields.tutoringAllowed;
               const publicHolidayOccursEveryYear = fields.occursEveryYear;

               //check if publicHolidayId is provided
               if (!publicHolidayId) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `publicHolidayId is required. Please provide a valid publicHolidayId.`);
                    return res.status(400).json(response);
               }

               //check if publicHolidayName is provided
               if (!publicHolidayName) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `publicHolidayName is required. Please provide a valid publicHolidayName.`);
                    return res.status(400).json(response);
               }

               //check if publicHolidayDate is provided
               if (!publicHolidayDate) {
                    let response = apiResponse.responseWithStatusCode(
                         apiResponse.apiConstants.API_REQUEST_SUCCESS,
                         `publicHolidayDate is required. Please provide a publicHolidayDate because it is required.`
                    );
                    return res.status(400).json(response);
               }

               //update PublicHolidays
               const updatedPublicHoliday = await publicHolidays.findByIdAndUpdate(
                    publicHolidayId,
                    {
                         name: publicHolidayName,
                         date: publicHolidayDate,
                         tutoringAllowed: publicHolidayTutoringAllowed,
                         occursEveryYear: publicHolidayOccursEveryYear,
                    },
                    { new: true }
               );

               if (!updatedPublicHoliday) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Failed to update Public Holiday. Please try again.`);
                    return res.status(400).json(response);
               } else {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Public Holiday updated successfully.`, updatedPublicHoliday);
                    return res.status(200).json(response);
               }
          } catch (error) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to update PublicHolidays", error);
               logger.newLog(logger.getLoggerTypeConstants().admin).error(response + error);
               return res.status(500).json(response);
          }
     });
});

//delete PublicHolidays
/**
 * @swagger
 * /api/v1/publicHoliday:
 *   delete:
 *     summary: Delete a public holiday
 *     tags: [Public Holidays]
 *     parameters:
 *       - in: query
 *         name: publicHolidayId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the public holiday to delete
 *     responses:
 *       '200':
 *         description: Successfully deleted the public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Success message
 *       '400':
 *         description: Bad request or failed to delete the public holiday
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.delete("/publicHoliday", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     try {
          const publicHolidayId = req.query.publicHolidayId;

          //check if publicHolidayId is provided
          if (!publicHolidayId) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `publicHolidayId is required. Please provide a valid publicHolidayId.`);
               return res.status(400).json(response);
          }

          const deletedPublicHoliday = await publicHolidays.findByIdAndDelete(publicHolidayId);

          if (!deletedPublicHoliday) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Failed to delete Public Holiday. Please try again.`, `No Public Holiday found.`);
               return res.status(400).json(response);
          } else {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Public Holiday deleted successfully`);
               return res.status(200).json(response);
          }
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to delete PublicHolidays", error);
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response + error);
          return res.status(500).json(response);
     }
});

//export router
module.exports = router;
