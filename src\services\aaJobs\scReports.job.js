const moment = require("moment-timezone");
const cron = require("node-cron");
const scHelper = require("../../controllers/reports/sc.helper.js");
const jobStatusModel = require("../../models/JobStatus.Model.js")
cron.schedule(
    "10 0 * * *",
    async () => {
const jobStatus = await jobStatusModel.findOne({ jobName: "scReportsJobLock" });
if (jobStatus && jobStatus.isJobRunning) {
    // Vérifie si le job est déjà en cours
    
        console.log("Job already running. Skipping this execution.");
        return; // Sortie si le job est déjà en cours d'exécution
    }

    try {
        // Marque le job comme en cours
        await jobStatusModel.updateOne(
          { jobName: "scReportsJobLock" },
          { $set: { isJobRunning: true, lastRun: new Date() } },
          { upsert: true }
        );
        // Exécution du job
        await scHelper.generateWeeklyScReports();
    } catch (error) {
        console.log(error);
        // Si une erreur survient, le job reste marqué comme en cours
        // logger.newLog(logger.getLoggerTypeConstants().crScReportsJob).alert(`Error in scReportsCreatedWeekly : ${error}`);
    } finally {
        // Une fois le job terminé (succès ou erreur), on réinitialise l'état
        await jobStatusModel.updateOne(
          { jobName: "scReportsJobLock" },
          { $set: { isJobRunning: false } }
        )
        console.log("Job finished. Resetting the job state.");
    }
});
