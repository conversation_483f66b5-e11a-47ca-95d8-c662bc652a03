function getTypeOfEstablishmentIdAndName(typeName) {
    let typeDetails = { id: null, name: "" };
    const targetType  = 'École supérieure'.toLowerCase().toLowerCase().normalize('NFC');
    switch (typeName.trim().toLowerCase().normalize('NFC')) {
      case targetType:
        typeDetails = { id: "14", name: "ecole superieure" };
        break;
      default:
          typeDetails = { id: "11", name: "Autre établissement" };
          break;
    }
  
  
    return typeDetails;
  }
  
  module.exports = getTypeOfEstablishmentIdAndName;