{"name": "zupdeco-api", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"start": "node app.js && npm run sentry:sourcemaps", "test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon app.js", "local": "cross-env NODE_ENV=production nodemon app.js --ignore 'swagger.json'", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org tobogan --project zupdeco-api ./build_sentry && sentry-cli sourcemaps upload --org tobogan --project zupdeco-api ./build_sentry"}, "repository": {"type": "git", "url": "git+https://github.com/Azwedo/zupdeco-api.git"}, "keywords": [], "author": "", "license": "ISC", "bugs": {"url": "https://github.com/Azwedo/zupdeco-api/issues"}, "homepage": "https://github.com/Azwedo/zupdeco-api#readme", "dependencies": {"@googleapis/calendar": "^9.7.9", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^7.61.0", "@sentry/tracing": "^7.61.0", "axios": "^1.1.2", "body-parser": "^1.20.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "firebase": "^9.12.1", "firebase-admin": "^11.1.0", "flat": "^6.0.1", "formidable": "^2.0.1", "google-auth-library": "^9.14.2", "googleapis": "^144.0.0", "hash-converter": "^1.0.2", "helmet": "^6.0.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.0", "lodash": "^4.17.21", "luxon": "^3.1.1", "moment": "^2.29.4", "moment-timezone": "^0.5.42", "mongodb": "^4.17.2", "mongoose": "^6.13.8", "morgan": "^1.10.0", "node-cron": "^3.0.3", "node-schedule": "^2.1.1", "prom-client": "^15.1.3", "response-time": "^2.3.3", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "twilio": "^4.20.1", "uuid": "^9.0.0", "uuidv4": "^6.2.13", "winston": "^3.8.2", "winston-mongodb": "^5.1.0", "xlsx": "^0.18.5", "xml-js": "^1.6.11", "xml2js": "^0.5.0"}, "devDependencies": {"cross-env": "^7.0.3", "madge": "^6.0.0", "nodemon": "^3.1.3"}}