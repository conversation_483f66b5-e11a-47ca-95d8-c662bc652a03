exports.getStudentsCountFromPreferences = (filters) => [
  {
    $match: {
      userRole: "student",
    },
  },
  {
    $group: {
      _id: "$userRole",
      count: {
        $sum: 1,
      },
      userId: {
        $push: "$userId",
      },
    },
  },
  {
    $unwind: {
      path: "$userId",
    },
  },
  {
    $project: {
      _id: 0,
      count: 0,
    },
  },
  {
    $lookup: {
      from: "studentpreferences",
      let: { userId: "$userId" },
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ["$userId", "$$userId"],
            },
          },
        },
        {
          // Additional match criteria if needed
          $match: filters || {},
        },
      ],
      as: "studentpreferences",
    },
  },
  {
    $match: {
      $expr: {
        $gt: [{ $size: "$studentpreferences" }, 0],
      },
    },
  },
  {
    $group: {
      _id: "null",
      usersWithPreferences: {
        $sum: 1,
      },
    },
  },
];
