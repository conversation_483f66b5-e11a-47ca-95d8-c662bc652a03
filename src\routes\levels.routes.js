const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("levels.routes.js");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const availabilityModel = require("../models/Availability.Model.js");

const levelsModel = require("../models/Levels.Model.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
  allowRequestToPass,
} = require("../middleware/apiAuth/verifyBearer.js");
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");
const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");

//create a new level
/**
 * @swagger
 * /api/v1/level:
 *   post:
 *     summary: Create New Level
 *     description: Create a new level with the provided details.
 *     tags: [Level]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               level:
 *                 type: string
 *                 description: JSON string representing the details of the level to be created.
 *                 example: '{"levelName": "Level 1", "levelDescription": "Description of Level 1"}'
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: New level created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: The unique identifier of the newly created level.
 *                     levelName:
 *                       type: string
 *                       description: The name of the newly created level.
 *                     levelDescription:
 *                       type: string
 *                       description: The description of the newly created level.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.post(
  "/level",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    const form = formidable({ multiples: true });

    form.parse(req, async (err, fields, files) => {
      try {
        const level = JSON.parse(fields.level);

        const newLevelDocument = await levelsModel.create(level);

        const newLevel = await newLevelDocument.save();

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New level created successfully`,
          newLevel
        );
        return res.status(200).json(response);
      } catch (error) {
        console.log(error);

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to create a new level`,
          error
        );
        return res.status(500).json(response);
      }
    });
  }
);

//get all levels
/**
 * @swagger
 * /api/v1/levels:
 *   get:
 *     summary: Get All Levels
 *     description: Retrieve all levels.
 *     tags: [Level]
 *     responses:
 *       200:
 *         description: All levels retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of all levels.
 *                   items:
 *                     type: object
 *                     properties:
 *                       levelName:
 *                         type: string
 *                         description: The name of the level.
 *                       levelDescription:
 *                         type: string
 *                         description: Description of the level.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get("/levels", async (req, res, next) => {
  try {
    const levels = await levelsModel.find({});

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `All levels`,
      levels
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get all levels`,
      error
    );
    return res.status(500).json(response);
  }
});

//get a level by id
/**
 * @swagger
 * /api/v1/level:
 *   get:
 *     summary: Get Level by ID
 *     description: Retrieve a level by its ID.
 *     tags: [Level]
 *     parameters:
 *       - in: query
 *         name: levelId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the level to retrieve.
 *     responses:
 *       200:
 *         description: A level by ID.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: The unique identifier of the level.
 *                     levelName:
 *                       type: string
 *                       description: The name of the level.
 *                     levelDescription:
 *                       type: string
 *                       description: The description of the level.
 *       404:
 *         description: Level not found.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get("/level", async (req, res, next) => {
  try {
    const levelId = req.query.levelId;

    const levelDocument = await levelsModel.findOne({ _id: levelId }).exec();

    if (levelDocument) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `A level by id`,
        levelDocument
      );
      return res.status(200).json(response);
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Failed to get a level by id`,
        `No level with id ${levelId} found`
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get a level by id`,
      error
    );
    return res.status(500).json(response);
  }
});

//update a level by id
/**
 * @swagger
 * /api/v1/level:
 *   put:
 *     summary: Update Level
 *     description: Update a level with the provided details.
 *     tags: [Level]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               level:
 *                 type: string
 *                 description: JSON string representing the updated details of the level.
 *                 example: '{"_id": "level_id", "levelName": "Updated Level Name", "levelDescription": "Updated Level Description"}'
 *               files:
 *                 type: array
 *                 items:
 *                   type: string
 *                   format: binary
 *     responses:
 *       200:
 *         description: Level updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     _id:
 *                       type: string
 *                       description: The unique identifier of the level.
 *                     levelName:
 *                       type: string
 *                       description: The name of the level.
 *                     levelDescription:
 *                       type: string
 *                       description: The description of the level.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.put(
  "/level",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res, next) => {
    const form = formidable({ multiples: true });

    form.parse(req, async (err, fields, files) => {
      try {
        const levelData = JSON.parse(fields.level);

        const levelId = levelData._id;

        //check if levelId is valid
        if (!levelId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Failed to update a level by id`,
            `Level id is required`
          );
          return res.status(500).json(response);
        }

        const levelDocumentUpdate = await levelsModel.findOneAndUpdate(
          { _id: levelId },
          levelData,
          { new: true }
        );

        if (levelDocumentUpdate) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Level is updated successfully`,
            levelDocumentUpdate
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Failed to update a level by id`,
            `No level with id ${levelId} found`
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to update a level by id`,
          error
        );
        return res.status(500).json(response);
      }
    });
  }
);

//export router
module.exports = router;
