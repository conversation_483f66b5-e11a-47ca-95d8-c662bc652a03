const twilio = require("twilio");

//import .env variables
require("dotenv").config();

//Twilio Credentials
const accountSid = process.env.TWILIO_ACCOUNT_SID;
const authToken = process.env.TWILIO_AUTH_TOKEN;

//require the Twilio module and create a REST client
const client = new twilio(accountSid, authToken);

//send sms
exports.sendSms = async (to, body) => {
  console.log("FROM", process.env.TWILIO_PHONE_NUMBER);
  try {
    const message = await client.messages.create({
      shortenUrls: true,
      to: to,
      from: process.env.TWILIO_PHONE_NUMBER,
      body: body,
    });
    return message;
  } catch (error) {
    console.log(error);
    return null;
  }
};
