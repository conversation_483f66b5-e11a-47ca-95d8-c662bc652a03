exports.crReportPipeline = (query, sessionFilter, sortBy, skip, pageSize) => {
  return [
    { $match: query },
    {
      $lookup: {
        from: "sessions",
        let: { sessionId: "$sessionId" },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ["$$sessionId", "$sessionId"],
              },
              ...sessionFilter,
            },
          },
        ],
        as: "session",
      },
    },
    {
      $match: {
        $expr: {
          $gt: [{ $size: "$session" }, 0],
        },
      },
    },
    {
      $facet: {
        data: [
          { $sort: { [sortBy ?? "_id"]: 1 } },
          { $skip: skip },
          { $limit: pageSize },
        ],
        total: [{ $count: "total" }],
      },
    },
  ];
};
