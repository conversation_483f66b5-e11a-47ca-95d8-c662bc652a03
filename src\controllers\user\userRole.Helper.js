//import user Role Model
const userRoleModel = require("../../models/UserRoles.Model.js");

const userModel = require("../../models/User.Models.js");

const userRoleAccessLevelConstants = require("../../utils/constants/userRoleAccessLevel.constants.js");

//check user role and access level in the database
exports.checkUserRoleAndAccessLevel = async (userId, accessLevel) => {
     //get user details from database
     const userDocument = await userModel.findOne({ userId: userId });

     //check if user exists
     if (!userDocument) {
          return false;
     }

     //get user role from user document
     const userRole = userDocument.userRole;

     //get user role details from database
     const userRoleDocument = await userRoleModel.findOne({ userRole: userRole });

     //check if user role exists
     if (!userRoleDocument) {
          return false;
     }
     //get accesses list from user role document
     const accessesList = userRoleDocument.accessesList;

     //check if user role has access to the requested access level
     accessesList.forEach((access) => {});
};

//get all list of userRole
exports.getAllUserRoles = async () => {
     const userRoleDocs = await userRoleModel.find();

     //return just userRole field as an array
     const userRoles = userRoleDocs.map((userRoleDoc) => {
          return userRoleDoc.userRole;
     });

     return userRoles;
};

//check if user Role already exists
exports.checkIfUserRoleExists = async (userRole) => {
     const userRoleDocs = userRoleModel.find();

     if (!userRoleDocs) {
          await userRoleDocs.forEach((userRoleDoc) => {
               if (userRoleDoc.userRole === userRole) {
                    return true;
               }
          });
     }

     return false;
};
