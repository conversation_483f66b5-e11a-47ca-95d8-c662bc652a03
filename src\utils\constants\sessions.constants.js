// Regarding session 0 and matching
// Possible statuses of session 0 would be :
// Session 0 to be scheduled : Date and time of session are to be created
// Session 0 suggested : Mail of the session’s details was sent to the tutor and student
// Session 0 called again : Still no answer from both of them, so email is sent again
// Session 0 confirmed : they both confirmed they would participate to the session
// Pair OK : <PERSON><PERSON> and student have agreed on starting together => Ongoing tutoring
// End (finish/stop) : For whatever reason tutoring has stopped
// Session 0 abandoned : Even after being called again, neither tutor nor student had answered (or one of them), so the session is being abandoned
// On hold : Needs to be rescheduled, still waiting for info
// The system allows the tutor the write a CR (report) once session 0 moves to status “Pair Ok”
// Session 0 can’t be created without a matching carried out beforehand. The pair suggested by the system is either confirmed by the coordinator
//and tutoring can start, or rejected, and in this case another matching is initiated (automatically)

// Unmatched (:: krejt tutors qe skan session0, ose kan session0 por statusi eshte “End” ose “Abandoned”)
// Matching Confirmed (:: Session0 ekziston edhe statusi eshte confirmed)
// All others (:: ka session0 mirepo statusi nuk eshte confirmed)

const sessionsStatus = {
  SESSIONS_0_TO_BE_SCHEDULED: "session-0-to-be-scheduled",
  SESSIONS_0_SUGGESTED: "session-0-suggested",
  SESSIONS_0_CALLED_AGAIN: "session-0-called-again",
  SESSIONS_0_CONFIRMED: "session-0-confirmed",
  SESSIONS_0_PAIR_OK: "session-0-pair-ok",
  SESSIONS_0_FINISH_STOP: "session-0-finish-stop",
  SESSIONS_0_ABANDONED: "session-0-abandoned",
  SESSIONS_0_ON_HOLD: "session-0-on-hold",
  CANCELED: "canceled",
  FAILED: "failed",
  SCHEDULED: "scheduled",
  CONFIRMED: "confirmed",
};

const sessionsStatusInFilter = {
  UNMATCHED: {
    statusValue: "unmatched",
    statusCondition: [
      {
        "matching.status": {
          $in: [
            sessionsStatus.SESSIONS_0_FINISH_STOP,
            sessionsStatus.SESSIONS_0_ABANDONED,
          ],
        },
      },
      { "matching.status": { $exists: false } },
    ],
  },
  MATCHING_CONFIRMED: {
    statusValue: "matching-confirmed",
    statusCondition: [
      { "matching.status": sessionsStatus.SESSIONS_0_CONFIRMED },
    ],
  },
  ALL_OTHERS: {
    statusValue: "all-others",
    statusCondition: [
      { "matching.status": { $exists: true } },
      {
        "matching.status": {
          $ne: sessionsStatus.SESSIONS_0_CONFIRMED,
        },
      },
    ],
  },
};

const sessionsPrograms = {
  HOME_CLASS: "remote",
  DEVOIRS_FAITS: "face-to-face",
};

const sessionsTypes = {
  SESSIONS_0: "session-0",
  GROUP_SESSION: "group-session",
  PRIVATE_SESSION: "private-session",
};

const sessionsRecurrence = {
  ONE_TIME: "one-time", // default value, if not specified | One session
  WEEKLY: "weekly", // One session per week, every week
  BI_MONTHLY: "bi-monthly", // One session every two weeks
};

const initialDynamicIds = {
  SESSION_ID_FIRST_TWO_DIGITS: "ss",
};

const sessionsDefaultMessage = {
  WELCOME_MESSAGE: "Welcome to the session!",
  END_MESSAGE: "The session is over",
};
const excludedStatuses = [
  "session-0-finish-stop",
  "session-0-abandoned",
  "failed",
  "canceled",
  "session-0-on-hold",
];

module.exports = {
  sessionsStatus,
  sessionsPrograms,
  sessionsTypes,
  sessionsRecurrence,
  sessionsDefaultMessage,
  initialDynamicIds,
  sessionsStatusInFilter,
  excludedStatuses
};
