const schedule = require("node-schedule");

var moment = require("moment-timezone");

//import  logger
const apiLoggerS = require("../../services/logger/LoggerService.js");

const logger = new apiLoggerS("monitoringWebhooks.Job.js");

const tutorHelper = require("../../controllers/tutor/tutor.helper.js");

const userHelper = require("../../controllers/user/User.Helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const userRoleStatusConstants = require("../../utils/constants/user.role.status.constants.js");
const tutorConstants = require("../../utils/constants/tutor.constants.js");

const zupDecoDateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");
const sendGridConstants = require("../../utils/constants/sendgrid.constants.js");
const sendGrid = require("@sendgrid/mail");

//every 5 minutes check for the health of the webhooks
const ruleResumeGenerated = new schedule.RecurrenceRule();
//set hour to 8AM and time zone to UTC +2
ruleResumeGenerated.hour = 0;
ruleResumeGenerated.minute = 0;
ruleResumeGenerated.tz = "Europe/Paris";

//I need a job that runs every day at 00:00:00 AM
const checkUserLoginExpirationTime = schedule.scheduleJob(
  ruleResumeGenerated,
  async function () {
    try {
      const theTimeOfStart = moment()
        .tz("Europe/Paris")
        .format("YYYY-MM-DD HH:mm:ss");

      const listOfUser = await tutorLoginExpiryCheck();
      let titleHeader =
        "######################################################";
      let title = `TUTOR checkUserLoginExpirationTime is running at ${theTimeOfStart} `;
      console.log(titleHeader + "\n" + title);
      if (listOfUser && listOfUser.length > 0) {
        //print the list of users that are deactivated, first name, last name
        listOfUser.forEach((user) => {
          console.log(
            `\nUser ${user.contactDetails.firstName} ${user.contactDetails.lastName} is deactivated`
          );
        });
      } else {
        console.log(
          "\nWe have checked all the Tutors and can confirm that none of them are deactivated. This means that everything is running smoothly! 🎉🎉🎉"
        );
      }
      const theTimeOfEnd = moment()
        .tz("Europe/Paris")
        .format("YYYY-MM-DD HH:mm:ss");
      let titleFooter = `######################################################\n`;
      let titleEnd = `\nTUTOR checkUserLoginExpirationTime is finished at ${theTimeOfEnd} `;
      console.log(titleEnd + "\n" + titleFooter);
    } catch (error) {
      console.log(error);
      logger
        .newLog(logger.getLoggerTypeConstants().tutorJob)
        .alert(`Error in checkUserLoginExpirationTime: ${error}`);
    }
  }
);

const ruleTutorSubscription = new schedule.RecurrenceRule();
ruleTutorSubscription.hour = 8;
ruleTutorSubscription.minute = 0;
ruleTutorSubscription.tz = "Europe/Paris";

const sendReminderToTutor = schedule.scheduleJob(
  ruleTutorSubscription,
  async function () {
    try {
      const data1 = await tutorHelper.getTutorForSituationReminderIncomplet1();
      if (data1.status && data1.data.length > 0) {
        for (const tutor of data1.data) {
          const { firstName, email } = tutor.contactDetails;
          const tutorId = tutor.userId;
          const templateId =
            sendGridConstants.SendGridTemplateIds
              .TEMPLATE_TUTOR_REMINDER_INCOMPLET_1;
          const data = {
            firstName: firstName,
            loginLink: `${process.env.APP_BASE_URL}/tutor/login`,
          };
          await sendNotificationToTutor({ email, data, templateId });
          await tutorHelper.updateTutorSituation(
            tutorId,
            tutorConstants.situationOfTutor.INCOMPLETE_RELANCE_1
          );
        }
      }

      const data2 = await tutorHelper.getTutorForSituationReminderIncomplet2();
      if (data2.status && data2.data.length > 0) {
        for (const tutor of data2.data) {
          const { firstName, email } = tutor.contactDetails;
          const tutorId = tutor.userId;
          const templateId =
            sendGridConstants.SendGridTemplateIds
              .TEMPLATE_TUTOR_REMINDER_INCOMPLET_2;
          const data = {
            firstName: firstName,
            loginLink: `${process.env.APP_BASE_URL}/tutor/login`,
          };
          await sendNotificationToTutor({ email, data, templateId });
          await tutorHelper.updateTutorSituation(
            tutorId,
            tutorConstants.situationOfTutor.INCOMPLETE_RELANCE_2
          );
        }
      }
    } catch (error) {
      console.error(error);
      logger
        .newLog(logger.getLoggerTypeConstants().sessionsJob)
        .alert(`Error in sendNotificationToTutors: ${error}`);
    }
  }
);

async function tutorLoginExpiryCheck() {
  try {
    const { data: listOfTutors } =
      await userHelper.getAllUsersByUserRoleAndStatus(
        userRoleConstants.ROLE_TUTOR,
        userRoleStatusConstants.userAdminStatus.ACTIVE
      );
    const listOfTutorsIsDeactivated = [];
    if (listOfTutors && listOfTutors.length > 0) {
      for (let i = 0; i < listOfTutors.length; i++) {
        const userDocument = listOfTutors[i];
        const user = await checkCommitmentEndDate(userDocument);
        if (user) {
          listOfTutorsIsDeactivated.push(user);
        }
      }
    }
    return listOfTutorsIsDeactivated;
  } catch (error) {
    console.log(error);
    logger
      .newLog(logger.getLoggerTypeConstants().tutorJob)
      .alert(`Error in tutorLoginExpiryCheck: ${error}`);
  }
}

//if the commitment end date is less than date.now() then set the user to inactive
async function checkCommitmentEndDate(userDocument) {
  try {
    const userId = userDocument.userId;
    const tutorDocument = await tutorHelper.getTutorPreferences(userId);

    const commitmentEndDate = tutorDocument?.commitment?.endDate;
    const preemptiveEndDate = tutorDocument?.commitment?.preemptiveEndDate;

    const isCommitmentEndDateGreaterThanCurrentDate =
      zupDecoDateTimeHelper.isEndDateGreaterThanNow(
        commitmentEndDate,
        preemptiveEndDate
      );
    if (!isCommitmentEndDateGreaterThanCurrentDate) {
      //set user to inactive in user collection
      const userDocumentAfterChangeStatus = await userHelper.updateUserStatus(
        userId,
        userRoleStatusConstants?.userStatus?.INACTIVE
      );

      if (
        !userDocumentAfterChangeStatus ||
        !userDocumentAfterChangeStatus.status
      ) {
        logger
          .newLog(logger.getLoggerTypeConstants().tutorJob)
          .alert(
            `Something went wrong when trying to set user to inactive in user collection for user with id: ${userId}, error: ${userDocumentAfterChangeStatus.message}`
          );
      }
      //set user to inactive in tutor collection
      const tutorDocumentAfterChangeStatus =
        await tutorHelper.updateTutorPreferencesStatus(
          userId,
          userRoleStatusConstants?.userStatus?.INACTIVE
        );
      if (
        !tutorDocumentAfterChangeStatus ||
        !tutorDocumentAfterChangeStatus.status
      ) {
        logger
          .newLog(logger.getLoggerTypeConstants().tutorJob)
          .alert(
            `Something went wrong when trying to set user to inactive in tutor collection for user with id: ${userId}, error: ${tutorDocumentAfterChangeStatus.message}`
          );
      }
      return userDocumentAfterChangeStatus.data;
    } else {
      return null;
    }
  } catch (error) {
    console.log(error);
    logger
      .newLog(logger.getLoggerTypeConstants().tutorJob)
      .alert(`Error in checkCommitmentEndDate: ${error}`);
  }
}

const sendNotificationToTutor = async ({ email, data, templateId }) => {
  const setupInvitation = {
    to: email,
    dynamic_template_data: data,
    from: sendGridConstants.ZupDecoEmails.EMAIL_TEAM_ZUPDECO,
    templateId: templateId,
  };

  try {
    await sendGrid.send(setupInvitation);
  } catch (error) {
    logger
      .newLog(logger.getLoggerTypeConstants().sessionsJob)
      .alert(`Error in sendNotificationToTutor: ${error}`);
  }
};

//export the job
module.exports = {
  checkUserLoginExpirationTime,
  sendNotificationToTutor,
  sendReminderToTutor,
};
