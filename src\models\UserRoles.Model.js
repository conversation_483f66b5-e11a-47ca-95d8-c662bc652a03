const mongoose = require("mongoose");

//import date helper
const dateHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const userRolesSchema = new mongoose.Schema(
     {
          userRole: {
               type: String,
               required: true,
               unique: true,
          },
          accessList: [
               {
                    name: String,
                    accessPrivilege: {
                         // R for read-only, RW for read and write, and N for no access
                         type: String,
                         default: "N",
                    },
                    //remove _id from the array
                    _id: false,
               },
          ],
          userRoleDescription: {
               type: String,
          },
          userRoleStatus: Boolean,
          userRoleCreatedDate: {
               type: Date,
               default: dateHelper.getCurrentDateTimeInParisZone(),
          },
     },
     { versionKey: false }
);

module.exports = mongoose.model("userRoles", userRolesSchema);
