//setup express
const express = require("express");
const router = express.Router();
const User = require("../models/User.Models.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import formidable
const formidable = require("formidable");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");

//import userRole  constants
const userRoleConstants = require("../utils/constants/userRolesConstants.js");

//import  logger
const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("parent.routes.js");

//import student helper
const studentHelper = require("../controllers/students/student.helpers.js");
const studentPreferencesModel = require("../models/Student.Preferences.Model.js");
const ParentPreferences = require("../models/Parent.Preferences.Model.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");

//Test hardware
/**
 * @swagger
 * /api/v1/testHardware:
 *   put:
 *     summary: Test Hardware
 *     description: Test the hardware devices for the user.
 *     tags: [Hardware]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The ID of the user.
 *               isWorkingVideoDevice:
 *                 type: boolean
 *                 description: Flag indicating whether the video device is working.
 *               isWorkingAudioDevice:
 *                 type: boolean
 *                 description: Flag indicating whether the audio device is working.
 *     responses:
 *       200:
 *         description: Hardware tested successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: string
 *                   description: Message indicating the success of hardware testing.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       404:
 *         description: User not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.put("/testHardware", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               let userId = fields.userId;
               let isWorkingVideoDevice = fields.isWorkingVideoDevice;
               let isWorkingAudioDevice = fields.isWorkingAudioDevice;

               //check if userId is provided in the request
               if (!userId) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `User Id is required`, `Please provide userId, it is required to test hardware`);
                    return res.status(200).json(response);
               }
               console.log("...................");
               //getUser data
               let userDocument = await userHelper.getUserDetailsByUserId(userId);

               console.log("userDocument", userDocument);

               if (userDocument.userRole === userRoleConstants.ROLE_PARENT) {
                    //Get parent Preference
                    let parentPreference = await ParentPreferences.findOne({ userId: userId });

                    //check if parentPreference is null or empty, if yes then create new parentPreference
                    if (parentPreference) {
                         parentPreference.testHardware.isWorkingVideoDevice = isWorkingVideoDevice;
                         parentPreference.testHardware.isWorkingAudioDevice = isWorkingAudioDevice;
                         await parentPreference.save();
                    } else {
                         let newParentPreference = new ParentPreferences({
                              userId: userId,
                              testHardware: {
                                   isWorkingVideoDevice: isWorkingVideoDevice,
                                   isWorkingAudioDevice: isWorkingAudioDevice,
                              },
                         });
                         await newParentPreference.save();
                    }

                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Hardware tested successfully`, `Device is working fine, you can proceed further`);
                    return res.status(200).json(response);
               } else if (userDocument.userRole === userRoleConstants.ROLE_STUDENT) {
                    //Get parent Preference
                    let parentPreference = await studentPreferencesModel.findOne({ userId: userId });

                    //check if parentPreference is null or empty, if yes then create new parentPreference
                    if (parentPreference) {
                         parentPreference.testHardware.isWorkingVideoDevice = isWorkingVideoDevice;
                         parentPreference.testHardware.isWorkingAudioDevice = isWorkingAudioDevice;
                         await parentPreference.save();
                    } else {
                         let newPreference = new studentPreferencesModel({
                              userId: userId,
                              testHardware: {
                                   isWorkingVideoDevice: isWorkingVideoDevice,
                                   isWorkingAudioDevice: isWorkingAudioDevice,
                              },
                         });
                         await newPreference.save();
                    }

                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Hardware tested successfully`, `Device is working fine, you can proceed further`);
                    return res.status(200).json(response);
               } else {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `User role is not valid`, `User role is not valid`);
                    return res.status(200).json(response);
               }
          } catch (error) {
               console.log(error);
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Error occurred while testing hardware`, JSON.stringify(error));
               logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
               return res.status(500).json(response);
          }
     });
});

//get hardware test status
/**
 * @swagger
 * /api/v1/testHardware:
 *   get:
 *     summary: Get Hardware Test Status
 *     description: Retrieve the hardware test status for the user.
 *     tags: [Hardware]
 *     security:
 *       - ApiKeyAuth: []
 *       - BearerAuth: []
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the user.
 *     responses:
 *       200:
 *         description: Hardware details fetched successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   description: Hardware details including video and audio device status.
 *       400:
 *         description: Bad Request.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       404:
 *         description: User not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 *       500:
 *         description: Internal Server Error occurred.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Description of the error.
 */
router.get("/testHardware", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])], async (req, res) => {
     try {
          const userId = req.query.userId;

          //check if userId is provided in the request
          if (!userId) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `User Id is required`, `Please provide userId, it is required to get  hardware details`);
               return res.status(200).json(response);
          }

          //getUser data
          let userDocument = await userHelper.getUserDetailsByUserId(userId);

          if (userDocument.userRole === userRoleConstants.ROLE_PARENT) {
               //Get parent Preference
               let parentPreference = await ParentPreferences.findOne({ userId: userId });

               let hardwareData = parentPreference.testHardware;

               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Hardware details fetched successfully`, hardwareData);
               return res.status(200).json(response);
          } else if (userDocument.userRole === userRoleConstants.ROLE_STUDENT) {
               //Get parent Preference
               let parentPreference = await studentPreferences.findOne({ userId: userId });

               let hardwareData = parentPreference.testHardware;

               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `Hardware details fetched successfully`, hardwareData);
               return res.status(200).json(response);
          } else {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `User role is not valid`, `User role is not valid`);
               return res.status(200).json(response);
          }
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, `Error occurred while getting hardware test status`, JSON.stringify(error));
          logger.newLog(logger.getLoggerTypeConstants().hardware).error(response);
          return res.status(500).json(response);
     }
});

//export router
module.exports = router;
