// routes/session.routes.js
const express = require('express');
const router = express.Router();
const SessionLiveTracking = require('../models/SessionLiveTraking.Model.js');
const sessionModel = require('../models/Sessions.Model.js');
const binomeModel = require('../models/Binomes.Model.js');
const { liveEventSchema, closeSessionSchema } = require('../validations/sessionLiveTraking.validations.js');
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

/**
 * @swagger
 * /api/v1/session/live:
 *   post:
 *     summary: Enregistrer un événement en direct d'une session
 *     description: >
 *       C<PERSON>e ou met à jour les données de suivi en direct (`liveData`) d'une session.  
 *       Cette route est protégée par une clé API transmise via l'en-tête `api-key`.
 *     tags:
 *       - Session
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - idSession
 *               - idBinome
 *               - liveData
 *             properties:
 *               idSession:
 *                 type: string
 *                 description: Identifiant unique de la session.
 *                 example: "ssd980cf46"
 *               idBinome:
 *                 type: string
 *                 description: Identifiant unique du binôme.
 *                 example: "zu-4m8m0a-uu-jh-k4p6hkf9tq_zu-9bhwz1-qj-hc-q13j2y7z0h"
 *               liveData:
 *                 type: object
 *                 required:
 *                   - learnerHourIn
 *                   - learnerHourOut
 *                   - mentorHourIn
 *                   - mentorHourOut
 *                   - mentorStatus
 *                   - learnerStatus
 *                 properties:
 *                   learnerHourIn:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "10:15"
 *                     description: Heure d'entrée de l'élève.
 *                   learnerHourOut:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "11:00"
 *                     description: Heure de sortie de l'élève.
 *                   mentorHourIn:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "10:00"
 *                     description: Heure d'entrée du mentor.
 *                   mentorHourOut:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "11:05"
 *                     description: Heure de sortie du mentor.
 *                   mentorStatus:
 *                     type: string
 *                     enum: ["présent", "absent"]
 *                     example: "présent"
 *                     description: Statut de présence du mentor.
 *                   learnerStatus:
 *                     type: string
 *                     enum: ["présent", "absent"]
 *                     example: "absent"
 *                     description: Statut de présence de l'élève.
 *     responses:
 *       200:
 *         description: Événement session enregistré avec succès.
 *       400:
 *         description: Données invalides (erreur de validation).
 *       401:
 *         description: Clé API invalide ou manquante.
 *       409:
 *         description: Session ou binôme non trouvé dans la base principale.
 *       500:
 *         description: Erreur interne du serveur.
 */
router.post('/session/live', verifyApiKey, async (req, res) => {
    const { error, value } = liveEventSchema.validate(req.body);
    if (error) {
      return res.status(400).json({
        status: 'error',
        message: error.details[0].message,
      });
    }
  
    try {
      // Vérifie si la session existe dans la base principale des sessions
      const existingSession = await sessionModel.findOne({ sessionId: value.idSession });
      if (!existingSession) {
        return res.status(409).json({
          status: 'error',
          message: 'Une session avec cet idSession n\'existe pas',
        });
      }
      const existingBinome = await binomeModel.find({ idBinome: value.idBinome });

      if (!existingBinome.length) {
        return res.status(409).json({
          status: 'error',
          message: 'le binome avec cet idSession n\'existe pas',
        });
      }
      // Vérifie si un tracking existe déjà pour cette session
      const existingTracking = await SessionLiveTracking.findOne({ idSession: value.idSession });
  
      if (existingTracking) {
        // Met à jour uniquement le champ liveData
        await SessionLiveTracking.updateOne(
          { idSession: value.idSession },
          { $set: { liveData: value.liveData } }
        );
      } else {
        // Crée un nouveau document avec liveData
        await SessionLiveTracking.create({
          idSession: value.idSession,
          idBinome: value.idSession,
          liveData: value.liveData
        });
      }
  
      return res.status(200).json({
        status: 'success',
        message: 'Événement session enregistré',
      });
    } catch (err) {
      console.error(err);
      return res.status(500).json({
        status: 'error',
        message: 'Erreur interne du serveur',
      });
    }
  });

/**
 * @swagger
 * /api/v1/session/close:
 *   post:
 *     summary: Clôturer une session
 *     description: >
 *       Met à jour les données de clôture d'une session existante.  
 *       Cette route est protégée : une clé API valide doit être transmise via l'en-tête `api-key`.
 *     tags:
 *       - Session
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - idSession
 *               - idBinome
 *               - closeData
 *             properties:
 *               idSession:
 *                 type: string
 *                 description: L'identifiant unique de la session.
 *                 example: "ssd980cf46"
 *               idBinome:
 *                 type: string
 *                 description: L'identifiant unique du binôme.
 *                 example: "zu-4m8m0a-uu-jh-k4p6hkf9tq_zu-9bhwz1-qj-hc-q13j2y7z0h"
 *               closeData:
 *                 type: object
 *                 required:
 *                   - startTime
 *                   - endTime
 *                   - durationSession
 *                   - totalConnectionTimeLearner
 *                   - totalConnectionTimeMentor
 *                   - statutPresenceLearner
 *                   - statutPresenceMentor
 *                   - seancestatut
 *                 properties:
 *                   startTime:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "10:00"
 *                     description: Heure de début de la session.
 *                   endTime:
 *                     type: string
 *                     pattern: '^([0-1]\\d|2[0-3]):([0-5]\\d)$|^--:--$'
 *                     example: "11:00"
 *                     description: Heure de fin de la session.
 *                   durationSession:
 *                     type: string
 *                     pattern: '^\\d+$'
 *                     example: "60"
 *                     description: Durée totale de la session en minutes.
 *                   totalConnectionTimeLearner:
 *                     type: string
 *                     pattern: '^\\d+$'
 *                     example: "50"
 *                     description: Temps de connexion total de l'élève en minutes.
 *                   totalConnectionTimeMentor:
 *                     type: string
 *                     pattern: '^\\d+$'
 *                     example: "60"
 *                     description: Temps de connexion total du mentor en minutes.
 *                   statutPresenceLearner:
 *                     type: string
 *                     enum: ["présent", "absent"]
 *                     example: "présent"
 *                     description: Statut de présence de l'élève.
 *                   statutPresenceMentor:
 *                     type: string
 *                     enum: ["présent", "absent"]
 *                     example: "présent"
 *                     description: Statut de présence du mentor.
 *                   seancestatut:
 *                     type: string
 *                     enum: ["terminée"]
 *                     example: "terminée"
 *                     description: Statut de la session.
 *     responses:
 *       200:
 *         description: Session clôturée avec succès
 *       400:
 *         description: Erreur de validation
 *       401:
 *         description: Clé API invalide ou manquante
 *       404:
 *         description: Session non trouvée
 *       409:
 *         description: Binôme non trouvé
 *       500:
 *         description: Erreur serveur
 */
router.post('/session/close', verifyApiKey, async (req, res) => {
    const { error } = closeSessionSchema.validate(req.body);
    if (error) {
      return res.status(400).json({ status: 'error', message: error.details[0].message });
    }
  
    const { idSession,idBinome, closeData } = req.body;
  
    try {
      // Vérifie si un tracking existe déjà pour cette session
      const existingTracking = await SessionLiveTracking.findOne({ idSession });
      if (!existingTracking) {
        return res.status(404).json({
          status: 'error',
          message: `Aucune session trouvée avec l'idSession ${idSession}`,
        });
      }
      const existingBinome = await binomeModel.findOne({ idBinome: idBinome });
      if (!existingBinome) {
        return res.status(409).json({
          status: 'error',
          message: 'le binome avec cet idSession n\'existe pas',
        });
      }
      // Met à jour les données de la session
    const updatedSession = await SessionLiveTracking.findOneAndUpdate(
      { idSession },
      { $set: { idBinome, closeData } },
      { new: true }
    );
  
      // Si la session a été mise à jour avec succès
      if (updatedSession) {
        return res.json({
          status: 'success',
          message: 'Données de session mises à jour avec succès.',
          data: updatedSession, // Ajoute les données mises à jour si nécessaire
        });
      } else {
        return res.status(500).json({
          status: 'error',
          message: 'Erreur lors de la mise à jour des données de la session.',
        });
      }
    } catch (err) {
      console.error(err); // Log l'erreur pour le debug
      res.status(500).json({ status: 'error', message: 'Erreur interne du serveur.' });
    }
  });
  

module.exports = router;
