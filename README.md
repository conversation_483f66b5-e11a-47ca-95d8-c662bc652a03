# ZupdeCO Application Overview

## Introduction
This application appears to be an educational management system that involves various users such as students, tutors, parents, and administrators. It facilitates the organization and scheduling of educational sessions, tracking preferences, and managing reports.

## Technical Overview

### Backend and Database

The application is built using **Express.js** for the backend framework, providing a robust environment for developing web applications and APIs. **MongoDB** is employed as the primary database, offering scalability and flexibility with its NoSQL architecture for efficient data storage and querying.

### Authentication

**Firebase** is integrated into the application for handling user authentication. It provides secure authentication and authorization functionalities, ensuring that user identities are managed safely and access is controlled effectively.

### Communication Services

-   **Twilio** is used for SMS services, enabling the application to send text messages for notifications, alerts, and user interactions.
-   **SendGrid** manages email communications, handling tasks such as transactional emails, newsletters, and other email-related functionalities reliably.

### Virtual Meetings

**BigBlueButton** is utilized for virtual meetings within the application. It offers features like video conferencing, real-time chat, screen sharing, and collaborative tools,

### Learning Platform

**Maxicours** is integrated into the application for educational purposes. It provides learning resources and tools, enhancing the educational experience for users through structured content and interactive learning modules.

### API Documentation

**Swagger** is implemented for API documentation, providing a clear and interactive interface for developers to explore endpoints, request parameters, and responses. It ensures comprehensive documentation of API functionalities, improving developer productivity and facilitating seamless integration with external systems.

## Installation Steps

Follow these steps to install and set up the application:

### 1. Download NVM and Install Node.js 16.17.0

- Download NVM (Node Version Manager) from [NVM GitHub Repository](https://github.com/nvm-sh/nvm).
- Install Node.js version 16.17.0 using NVM:
  
  ```bash
    $ nvm install 16.17.0
    $ nvm use 16.17.0
    $ node -v
  ```
### 2. Install Dependencies

- Navigate to your project directory.
- Run the following command to install dependencies using npm:

    ```bash
    $ npm install
    ```

### 3. Configure Environment Variables

- Create a `.env` file in the root of your project.
- Configure the necessary environment variables required by your application. Example:

    ```text
        API_KEY=
        APP_BASE_URL=

        # MongoDB Keys
        MONGODB_CONNECTION_STRING_DEV=
        MONGODB_CONNECTION_STRING_PROD=
        MONGODB_CONNECTION_STRING_PREPROD=

        # SendGrid Keys
        SENDGRID_API_KEY=

        # Twilio Keys
        TWILIO_ACCOUNT_SID=
        TWILIO_AUTH_TOKEN=
        TWILIO_PHONE_NUMBER=

        # Sentry DNS
        SENTRY_DNS=

        # BigBlueButton Keys
        BIGBLUEBUTTON_URL=
        BIGBLUEBUTTON_SECRET=

        # Environment Variables
        NODE_ENV=
        API_VERSION=
        MAXICOURS_SECRET=
        MAXICOURS_URL=
        ROLE_TOKEN_SECRET=
        JWT_SECRET=
    ```
### 4. Run the Application Locally
- Once dependencies are installed and environment variables are configured, start the application locally using npm:

    ```bash
    $ npm run local
    ```

- Your application should now be running locally on the specified port (default is 3000).

### 5. Additional Notes

- Ensure MongoDB is running locally or accessible as per your configuration in `MONGODB_URI`.

- Adjust the `.env` configuration and other settings as per your specific application requirements.

### 6. Accessing Swagger Documentation

You can access Swagger documentation for this API at [http://localhost:3000/api/v1/docs](http://localhost:3000/api/v1/docs).

## Database Schemas & Relations Overview

### Collection Names

- `users`
- `parentPreferences`
- `studentPreferences`
- `tutorPreferences`
- `sessions`
- `establishments`
- `governmentSectors`
- `sectors`
- `educationAnnuaire`
- `crScreports`
- `userAdministation`
- `userAdministationPreferences`

### User Schema
- `userId`: Unique identifier used in other schemas to reference a user.

### ParentPreferences Schema
- `userId`: References the `User` schema.

### StudentPreferences Schema
- `userId`: References the `User` schema.
- `parentUserId`: References the `User` schema for the student's parent.
- `assignment.establishments.establishmentId`: References the `EducationAnnuaire` schema.
- `assignment.department.departmentId`: References the `Department` schema.
- `assignment.sectors.sectorId`: References the `Sector` schema.
- `assignment.level.levelId`: References the `Level` schema.

### TutorPreferences Schema
- `userId`: References the `User` schema.
- `assignment.establishments.establishmentId`: References the `EducationAnnuaire` schema.
- `assignment.department.departmentId`: References the `Department` schema.
- `assignment.sectors.sectorId`: References the `Sector` schema.
- `assignment.vsc.vscId`: References the `UserAdministation` schema for VSC (likely Volunteer Service Coordinator).

### Sessions Schema
- `tutors.userId`: References the `User` schema for tutors.
- `students.userId`: References the `User` schema for students.
- `vsc.userId`: References the `UserAdministation` schema for VSCs.
- `school`: This could be a reference to the `Establishment` schema.
- `createdBy.userId` and `lastUpdatedBy.userId`: References the `User` schema.

### Establishment Schema
- `typeOfEstablishment.typeOfEstablishmentId`: Likely references a separate schema defining establishment types.
- `generalInformation.sector.sectorId`: References the `Sector` schema.
- `class.levelId`: References the `Level` schema.

### GovernmentSectors Schema
- `schoolYear.schoolYearId`: References the `ScholarYear` schema.
- `schoolZone.schoolZoneId`: References the `SchoolZones` schema.
- `coordinators.userId`: References the `UserAdministation` schema.

### Sectors Schema
- `program.programId`: References the `Program` schema.
- `schoolYear.schoolYearId`: References the `ScholarYear` schema.
- `schoolZone.schoolZoneId`: References the `SchoolZones` schema.
- `coordinators.userId`: References the `UserAdministation` schema.

### EducationAnnuaire Schema
- `sessionId`: Likely references the `Sessions` schema.

##### crScreports Schema
- `sessionId`: Likely references the `Sessions` schema.

### UserAdministation Schema
- `administrationPreferences.program.programId`: References the `Program` schema.
- `administrationPreferences.sector.sectorId`: References the `Sector` schema.
- `administrationPreferences.establishment.establishmentId`: References the `EducationAnnuaire` schema.
- `administrationPreferences.department.departmentId`: References the `Department` schema.
- `administrationPreferences.coordinators.coordinatorId`: References the `UserAdministation` schema for coordinators.

### UserAdministationPrefrences Schema
- `program.programId`: References the `Program` schema.
- `sector.sectorId`: References the `Sector` schema.
- `establishment.establishmentId`: References the `EducationAnnuaire` schema.
- `department.departmentId`: References the `Department` schema.
- `coordinators.coordinatorId`: References the `UserAdministation` schema for coordinators.

## Summary of Relationships

- **User**: Referenced by `ParentPreferences`, `StudentPreferences`, `TutorPreferences`, `Sessions`, `GovernmentSectors`, `UserAdministation`, and `UserAdministationPrefrences`.
- **Establishment**: Referenced by `StudentPreferences`, `TutorPreferences`, `Sessions`, `GovernmentSectors`, `UserAdministation`, and `UserAdministationPrefrences`.
- **Sector**: Referenced by `StudentPreferences`, `TutorPreferences`, `EducationAnnuaire`, `GovernmentSectors`, `UserAdministation`, and `UserAdministationPrefrences`.
- **Level**: Referenced by `StudentPreferences`, `TutorPreferences`, and `EducationAnnuaire`.
- **Program**: Referenced by `Sectors`, `UserAdministation`, and `UserAdministationPrefrences`.
- **ScholarYear**: Referenced by `GovernmentSectors` and `Sectors`.
- **SchoolZones**: Referenced by `GovernmentSectors` and `Sectors`.
- **Sessions**: Referenced by `EducationAnnuaire` and `crScreports`.

## Enums

### Programs
- **Home Classes**: Remote learning program.
- **Devoirs Faits**: Face-to-face tutoring program.
- **ZupDeFoot**: Program named "ZupDeFoot".

### CR/SC Report Status
- **To Be Entered**: The report is pending entry.
- **Entered**: The report has been entered.
- **Confirmed**: The report has been confirmed.

### CR/SC Report Types
- **CR Report**: A CR type report.
- **SC Report**: An SC type report.

### Session Status
- **To Be Scheduled**: The session needs to be scheduled.
- **Suggested**: The session has been suggested.
- **Called Again**: The session has been called again.
- **Confirmed**: The session has been confirmed.
- **Pair OK**: The pair for the session is confirmed.
- **Finish Stop**: The session is stopped after finishing.
- **Abandoned**: The session has been abandoned.
- **On Hold**: The session is on hold.
- **Canceled**: The session has been canceled.
- **Failed**: The session failed.
- **Scheduled**: The session is scheduled.
- **Confirmed**: The session is confirmed.

### Session Types
- **Session 0**: Initial session type.
- **Group Session**: Session with a group.
- **Private Session**: One-on-one session.

### Session Recurrence
- **One Time**: A single session.
- **Weekly**: A session every week.
- **Bi-Monthly**: A session every two weeks.

### Session Programs
- **Home Class**: Remote session.
- **Devoirs Faits**: Face-to-face session.

### Type of Tutors
- **Face-to-Face Student Tutor**: Tutor for face-to-face sessions.
- **Remote Student Tutor**: Tutor for remote sessions.

### User Status
- **Active**: User is active.
- **Deactivated**: User is deactivated.
- **On Hold**: User is on hold.

### Student Status
- **New**: New student.
- **Awaiting Tutor**: Student is waiting for a tutor.
- **In Tutoring**: Student is currently in tutoring.
- **Stop**: Tutoring has stopped for the student.

### Tutor Status
- **Active Tutor**: Tutor is active.
- **Deactivated Tutor**: Tutor is deactivated.
- **On Hold Tutor**: Tutor is on hold.
- **Commitment End**: Tutor's commitment has ended.
- **Preemptive End**: Tutor's role ended preemptively.

### Tutoring Type
- **Private Tutoring**: One-on-one tutoring.
- **Group Tutoring**: Group tutoring.

### Onboarding Status
- **In Progress**: Onboarding is in progress.
- **In Review**: Onboarding is being reviewed.
- **Completed**: Onboarding is completed.

### Tutor Situation
- **New**: New tutor.
- **Incomplete Relance 1**: First follow-up for incomplete profile.
- **Incomplete Relance 2**: Second follow-up for incomplete profile.
- **Complete**: Profile is complete.
- **Awaiting Student 1**: Waiting for the first student.
- **Student 1 Found**: First student has been found.
- **Scheduled Session 0**: Initial session is scheduled.
- **In Tutoring**: Tutor is currently tutoring.
- **For Renewal**: Tutor is up for renewal.
- **Pause**: Tutoring is paused.
- **Stop**: Tutoring has stopped.
- **No Further Action**: No further action is required.

### Onboarding Steps
- **Step 0**: Initial step.
- **Step 1**: First step.
- **Step 2**: Second step.

### Tutor Document Types
- **Criminal Record**: Document type for criminal record.
- **Personal Education**: Document type for personal education records.
- **Diplomas Certificates**: Document type for diplomas and certificates.

### Type of Establishments
- **School**: Basic educational institution.
- **Higher School**: Higher educational institution.
- **Structure**: General organizational structure.

### User Administration Status
- **Active**: Active administrative user.
- **Deactivated**: Deactivated administrative user.
- **Deleted**: Deleted administrative user.
- **Suspended**: Suspended administrative user.
- **On Hold**: Administrative user is on hold.
- **Pending**: Administrative user is pending.
- **Inactive**: Administrative user is inactive.

### User Roles
- **Super Administrator**: Top-level administrator.
- **Manager**: Manager role.
- **Coordinator**: Coordinator role.
- **Standard User**: Standard user role.
- **VSC**: Volunteer Service Coordinator.
- **Tutor**: Tutor role.
- **Student**: Student role.
- **Parent**: Parent role.
