//global constants
module.exports = {
  USER_NOT_FOUND: 2004,
  USER_ALREADY_EXISTS: 2005,
  USER_SAVED_SUCCESSFULLY: 2006,
  RESET_PASSWORD_LINK_SENT: 2007,
  EMAIL_IS_INVALID: 2008,
  REDIRECT_URL_IS_INVALID: 2009,
  EMAIL_VERIFICATION_LINK_SENT: 2010,
  USER_DOES_NOT_EXISTS: 2011,
  ERROR_WHILE_SIGNING_IN_USER: 2012,
  USER_DATA_FAILED_TO_UPDATE: 2013,
  PARENT_SEND_INVITATION_SUCCESSFULLY: 2014,
  PARENT_SEND_INVITATION_FAILED: 2015,
  PARENT_SEND_INVITATION_SUCCESS: 2016,
  PARENT_SEND_INVITATION_SUCCESSFULLY: 2017,
  DATA_SAVED_SUCCESSFULLY: 2018,
  INVITATION_CODE_ACCEPTED_SUCCESSFULLY: 2019,
  USER_ID_IS_NULL: 2020,
  DATA_DELETED: 2021,
  ESTABLISHMENT_NAME_ALREADY_EXISTS: 2022,
  SESSIONS_DOES_NOT_EXISTS: 2023,
  SESSIONS_ALREADY_EXISTS: 2024,
  SESSIONS_SAVED_SUCCESSFULLY: 2025,
  SESSIONS_DATA_FAILED_TO_UPDATE: 2026,
  SESSIONS_DATA_FAILED_TO_DELETE: 2027,
  SESSIONS_DATA: 2028,
  DATA_NOT_DELETED: 2029,
  DATA_NOT_UPDATED: 2030,
  DATA_NOT_SAVED: 2031,
  DATA_ALREADY_EXISTS: 2032,
  USER_IS_NOT_ACTIVE: 2033,
  FINISH_REGISTRATION: 2041,
  ERROR_WHILE_SENDING_INVITATION_EMAIL: 2034,
  ERROR_WHILE_ACCEPTING_INVITATION: 2035,
  ADMIN_ACCEPT_INVITATION_SUCCESS: 2036,
  ERROR_WHILE_REGISTERING_USER_ADMINISTRATION_ACCOUNT: 2037,
  USER_IS_NOT_REGISTERED: 2038,
  EMAIL_IS_ALREADY_REGISTERED: 2039,
  USER_EXIST_WITH_SAME_NAME: 2040,
  API_REQUEST_SUCCESS: "success",
  INTERNAL_SERVER_ERROR: `Internal Server Error, please try again later.`,
  API_REQUEST_FAILED: "failed",
  API_VERSION: `/api/v${process.env.API_VERSION}`,
};
