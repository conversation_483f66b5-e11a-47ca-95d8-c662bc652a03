const mongoose = require("mongoose");
const Schema = mongoose.Schema;

const publicHolidaysSchema = new Schema(
     {
          name: {
               type: String,
               required: true,
          },

          date: {
               type: Date,
               required: true,
          },
          tutoringAllowed: Boolean,
          occursEveryYear: <PERSON><PERSON>an,
     },
     { versionKey: false }
);

module.exports = PublicHolidays = mongoose.model("publicHolidays", publicHolidaysSchema);
