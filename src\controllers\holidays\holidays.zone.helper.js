const schoolZoneModel = require("../../models/SchoolZones.Model.js");

const sectorModel = require("../../models/Sectors.Model.js");

const moment = require("moment-timezone");

const mongoose = require("mongoose");

const tutorHelper = require("../tutor/tutor.helper.js");

const sectorHelper = require("../sectors/sectors.helper.js");

exports.isPublicHolidayAndAllowingTutoringPerSchoolZone = async (tutorUserId, inputDate, scheduleDuringHolidays) => {
     try {
          //getTutor details by tutorUserId
          const sectorsOfTutor = await tutorHelper.getSectorsOfTutor(tutorUserId);

          if (!sectorsOfTutor.status) {
               return { status: false, message: sectorsOfTutor.message, data: null };
          }

          //get all school zones base on sectors of tutor
          const schoolZones = await sectorHelper.getSchoolZoneIdsBySectorIds(sectorsOfTutor.data);

          if (!schoolZones.status) {
               return { status: false, message: "Error in getHolidayPerZoneBaseOnTutorUserId function", data: null };
          }

          const listOfSchoolZoneIds = schoolZones.data;

          //convert listOfSchoolZoneIds to ObjectId
          const listOfSchoolZoneIdsObjectId = listOfSchoolZoneIds.map((zoneId) => mongoose.Types.ObjectId(zoneId));

          const yearMonthDay = moment(inputDate).format("YYYY-MM-DD");

          const startDate = moment(inputDate).startOf("day").toDate();
          const endDate = moment(inputDate).endOf("day").toDate();

          const schoolZonesBaseOnTutorSectors = await schoolZoneModel.find(
               {
                    _id: { $in: listOfSchoolZoneIdsObjectId },
                    schoolZoneHolidays: {
                         $elemMatch: {
                              holidayStartDate: { $lte: endDate },
                              holidayEndDate: { $gte: startDate },
                         },
                    },
               },
               {
                    "schoolZoneHolidays.$": 1,
               }
          );
          const listOfHolidays = schoolZonesBaseOnTutorSectors.map((zone) => {
               return zone;
          });
          let tutoringAllowed = false;
          listOfHolidays.forEach((zone) => {
               const holiday = zone.schoolZoneHolidays;
               holiday.forEach((h) => {
                    if (h.tutoringAllowed) {
                         tutoringAllowed = true;
                    }
               });
          });

          const lengthOfHolidays = listOfHolidays.length > 0 ? true : false;
          let tutoringAllowedStatus = false;

          if (scheduleDuringHolidays) {
               if (!lengthOfHolidays || (lengthOfHolidays && tutoringAllowed)) {
                    tutoringAllowedStatus = true;
               } else {
                    //console.log(`Tutoring is not allowed on this zone 🛑 ${yearMonthDay}\n\n`);
                    tutoringAllowedStatus = false;
               }
          } else {
               //if scheduleDuringHolidays is false  and publicHoliday is true then return false
               if (lengthOfHolidays) {
                    tutoringAllowedStatus = false;
               } else {
                    // console.log(`Tutoring is allowed on this zone ✅ ${yearMonthDay}\n\n`);
                    tutoringAllowedStatus = true;
               }
          }
          console.log(`Tutoring is allowed on this zone ✅ ${yearMonthDay}: ${tutoringAllowedStatus}\n\n`);
          return tutoringAllowedStatus;
     } catch (error) {
          console.log(error);
          return { status: false, message: error.message };
     }
};
