///"scientific-and-first", "literary-and-first", "both-subjects"

exports.SUBJECTS = [
  {
    name: "scientific-and-first",
    subjectId: "scientific-and-first",
  },
  {
    name: "literary-and-first",
    subjectId: "literary-and-first",
  },
  {
    name: "both-subjects",
    subjectId: "both-subjects",
  },
];

//return subjectId from subjectId, if subjectId is equal to subjectId = both-subjects return array of both scientific and literary
exports.getSubjectId = (subjectId) => {
  let subjectIds = [];
  if (subjectId === "both-subjects") {
    // subjectIds = null;
    subjectIds = [
      "scientific-and-first",
      "literary-and-first",
      "both-subjects",
    ];
  } else if (subjectId === "scientific-and-first") {
    subjectIds = ["scientific-and-first"];
  } else if (subjectId === "literary-and-first") {
    subjectIds = ["literary-and-first"];
  }
  return subjectIds;
};

exports.getSubjectIdForUnmachedTutors = (subjectId) => {
  let subjectIds = [];
  if (subjectId === "both-subjects") {
    // subjectIds = null;
    subjectIds = [
      // "scientific-and-first",
      // "literary-and-first",
      "both-subjects",
    ];
  } else if (subjectId === "scientific-and-first") {
    subjectIds = ["scientific-and-first", "both-subjects"];
  } else if (subjectId === "literary-and-first") {
    subjectIds = ["literary-and-first", "both-subjects"];
  }
  return subjectIds;
};
