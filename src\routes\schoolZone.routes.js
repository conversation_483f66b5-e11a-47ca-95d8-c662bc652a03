const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("schoolZone.routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import SchoolZones Model
const SchoolZones = require("../models/SchoolZones.Model.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");
const { SUPER_ADMINISTRATOR, COORDINATOR, MANAGER } = require("../utils/constants/userRolesConstants.js");

router.use((req, res, next) => {
     verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.SCHOOL_ZONES);
});

//save new SchoolZones
/**
 * @swagger
 * /api/v1/schoolZone:
 *   post:
 *     summary: Create a new school zone
 *     tags: [School Zones]
 *     requestBody:
 *       content:
 *         application/x-www-form-urlencoded:
 *           schema:
 *             type: object
 *             properties:
 *               zoneName:
 *                 type: string
 *                 description: The name of the school zone
 *     responses:
 *       '200':
 *         description: Successfully created a new school zone
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   $ref: '#/components/schemas/SchoolZone'
 *       '400':
 *         description: Invalid request or missing parameters
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.post("/schoolZone",[verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const schoolZoneName = fields.zoneName;

               //check if schoolZoneName is provided
               if (!schoolZoneName) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `schoolZoneName is required. Please provide a valid schoolZoneName.`);
                    return res.status(400).json(response);
               }

               const newSchoolZone = new SchoolZones({
                    zoneName: schoolZoneName,
                    schoolZoneHolidays: [],
               });

               await newSchoolZone.save();

               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "New SchoolZone saved successfully", newSchoolZone);
               return res.status(200).json(response);
          } catch (error) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to save new SchoolZones", error);
               logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
               return res.status(500).json(response);
          }
     });
});

//Get all SchoolZones
/**
 * @swagger
 * /api/v1/schoolZones:
 *   get:
 *     summary: Get all school zones
 *     tags: [School Zones]
 *     responses:
 *       '200':
 *         description: Successfully retrieved all school zones
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: array
 *                   items:
 *                     $ref: '#/components/schemas/SchoolZone'
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/schoolZones", async (req, res) => {
     try {
          const schoolZones = await SchoolZones.find({});

          //get just schoolZoneName and _id
          let schoolZonesArray = schoolZones.map((schoolZone) => {
               return {
                    _id: schoolZone._id,
                    zoneName: schoolZone.zoneName,
               };
          });

          //order by schoolZoneName
          schoolZonesArray.sort((a, b) => {
               if (a.zoneName < b.zoneName) {
                    return -1;
               }
               if (a.zoneName > b.zoneName) {
                    return 1;
               }
               return 0;
          });

          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "All SchoolZones", schoolZonesArray);
          return res.status(200).json(response);
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to get all SchoolZones", JSON.stringify(error));
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
          return res.status(500).json(response);
     }
});

//Get SchoolZone by id
/**
 * @swagger
 * /api/v1/schoolZone:
 *   get:
 *     summary: Get a school zone by ID
 *     tags: [School Zones]
 *     parameters:
 *       - in: query
 *         name: schoolZoneId
 *         schema:
 *           type: string
 *         required: true
 *         description: ID of the school zone to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved the school zone
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   $ref: '#/components/schemas/SchoolZone'
 *       '400':
 *         description: Bad request, missing schoolZoneId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/schoolZone", async (req, res) => {
     try {
          const schoolZoneId = req.query.schoolZoneId;

          //check if schoolZoneId is provided
          if (!schoolZoneId) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `schoolZoneId is required. Please provide a valid schoolZoneId.`);
               return res.status(400).json(response);
          }

          const schoolZone = await SchoolZones.findOne({ _id: schoolZoneId });

          if (!schoolZone) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `SchoolZone not found.`);
               return res.status(400).json(response);
          }

          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "SchoolZone by id", schoolZone);
          return res.status(200).json(response);
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to get SchoolZone by id", JSON.stringify(error));
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
          return res.status(500).json(response);
     }
});

//update SchoolZone by id
/**
 * @swagger
 * /api/v1/schoolZone:
 *   put:
 *     summary: Update a school zone by ID
 *     tags: [School Zones]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               schoolZoneId:
 *                 type: string
 *                 description: The ID of the school zone to update
 *               schoolZoneHolidays:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: An array of holiday dates for the school zone
 *     responses:
 *       '200':
 *         description: Successfully updated the school zone
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   $ref: '#/components/schemas/SchoolZone'
 *       '400':
 *         description: Bad request, missing schoolZoneId or schoolZoneHolidays
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.put("/schoolZone",[verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     const form = formidable({ multiples: true });
     form.parse(req, async (err, fields, files) => {
          try {
               const schoolZoneId = fields.schoolZoneId;
               const schoolZoneHolidays = JSON.parse(fields.schoolZoneHolidays);

               //check if schoolZoneId is provided
               if (!schoolZoneId) {
                    let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `schoolZoneId is required. Please provide a valid schoolZoneId.`);
                    return res.status(400).json(response);
               }

               const schoolZone = await SchoolZones.findOne({ _id: schoolZoneId });

               schoolZone.schoolZoneHolidays = schoolZoneHolidays;

               //save updated schoolZone
               await schoolZone.save();

               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "SchoolZone updated successfully", schoolZone);
               return res.status(200).json(response);
          } catch (error) {
               console.log(error);
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to update SchoolZone by id", JSON.stringify(error));
               logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
               return res.status(500).json(response);
          }
     });
});

//delete SchoolZone by id
/**
 * @swagger
 * /api/v1/schoolZone:
 *   delete:
 *     summary: Delete a school zone by ID
 *     tags: [School Zones]
 *     parameters:
 *       - in: query
 *         name: schoolZoneId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the school zone to delete
 *     responses:
 *       '200':
 *         description: Successfully deleted the school zone
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: string
 *                   description: Message indicating the deletion was successful
 *       '400':
 *         description: Bad request, missing or invalid schoolZoneId
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.delete("/schoolZone",[verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR,COORDINATOR,MANAGER])], async (req, res) => {
     try {
          const schoolZoneId = req.query.schoolZoneId;

          //check if schoolZoneId is provided
          if (!schoolZoneId) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `schoolZoneId is required. Please provide a valid schoolZoneId.`);
               return res.status(400).json(response);
          }

          const schoolZone = await SchoolZones.findOne({ _id: schoolZoneId });

          //check if schoolZone exists
          if (!schoolZone) {
               let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, `SchoolZone not found, please provide a valid schoolZoneId.`);
               return res.status(400).json(response);
          }

          //delete schoolZone
          await schoolZone.remove();

          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "SchoolZone deleted successfully", apiResponse.apiConstants.DATA_DELETED);
          return res.status(200).json(response);
     } catch (error) {
          let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_FAILED, "Failed to delete SchoolZone by id", JSON.stringify(error));
          logger.newLog(logger.getLoggerTypeConstants().admin).error(response);
          return res.status(500).json(response);
     }
});

//export router
module.exports = router;
