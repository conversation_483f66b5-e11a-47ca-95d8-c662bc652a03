const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("matching.routes.js");

const formidable = require("formidable");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import tutor Helper
const tutorHelper = require("../controllers/tutor/tutor.helper.js");

//import student Helper
const studentHelper = require("../controllers/students/student.helpers.js");

//import Session Helper
const sessionHelper = require("../controllers/sessions/sessions.helper.js");

//import matching Helper
const matchingHelper = require("../controllers/matching/matching.helper.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");
const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
  ROLE_PARENT,
  VSC,
} = require("../utils/constants/userRolesConstants.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");
const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");

//getMatchingStats
/**
 * @swagger
 * /api/v1/matching/stats:
 *   get:
 *     summary: Get Matching Statistics
 *     description: Retrieve the matching statistics for tutors and students.
 *     tags: [Matching]
 *     responses:
 *       200:
 *         description: Matching Statistics retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 totalMatchedTutors:
 *                   type: integer
 *                   description: Total number of matched tutors.
 *                 totalUnMatchedTutors:
 *                   type: integer
 *                   description: Total number of unmatched tutors.
 *                 totalMatchedStudents:
 *                   type: integer
 *                   description: Total number of matched students.
 *                 totalUnMatchedStudents:
 *                   type: integer
 *                   description: Total number of unmatched students.
 *       400:
 *         description: Bad request. The page size or page number is invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/stats",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      const response = await matchingHelper.getMatchingStats();
      return res.status(200).send(response);
    } catch (error) {
      const response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching Stats",
        error
      );
      res.status(500).send(response);
    }
  }
);

//#region  TUTOR > MATCHING > STUDENT
//Matching dashboard : Get all Tutors with list of students who have availability slots same as tutor
/**
 * @swagger
 * /api/v1/matching/tutor/dashboard:
 *   get:
 *     summary: Get Matching Tutor Dashboard
 *     description: Retrieve matching dashboard for tutors based on filters and sorting.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Number of items per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *         description: Page number.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filtering criteria.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sorting criteria.
 *     responses:
 *       200:
 *         description: Matching dashboard retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of tutors matching the criteria.
 *                   items:
 *                     type: object
 *                     description: Tutor details.
 *                 page:
 *                   type: integer
 *                   description: Current page number.
 *                 pageSize:
 *                   type: integer
 *                   description: Number of items per page.
 *                 totalNumberOfTutors:
 *                   type: integer
 *                   description: Total number of tutors matching the criteria.
 *       400:
 *         description: Bad request. The page size or page number is invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/tutor/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      let pageSize = apiResponse.buildPageSize(req.query.pageSize);
      let page = apiResponse.buildPage(req.query.page);
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const userRole = req.userRole;
      const connectedUser = req.userId;
      const matchingDashboard = await matchingHelper.matchDashboardTutor(
        filter,
        sortBy,
        page,
        pageSize,
        userRole,
        connectedUser
      );
      if (!matchingDashboard.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get matching dashboard`,
          matchingDashboard.message
        );
        return res.status(200).send(response);
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Matching dashboard",
        matchingDashboard.data,
        page,
        pageSize,
        matchingDashboard.totalNumberOfTutors
      );
      return res.status(200).send(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

//get all students base on TutorUserId who are matched with tutor
/**
 * @swagger
 * /api/v1/matching/tutor/students-matched:
 *   get:
 *     summary: Get Students Matched with Tutor
 *     description: Retrieve the list of students matched with a tutor based on tutor's availability.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the tutor.
 *       - in: query
 *         name: tutorAvailability
 *         schema:
 *           type: string
 *         description: The availability of the tutor.
 *     responses:
 *       200:
 *         description: List of students matched with the tutor retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of students matched with the tutor.
 *                   items:
 *                     type: object
 *                     description: Student details.
 *       400:
 *         description: Bad request. The tutorUserId is missing.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/tutor/students-matched",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const tutorAvailability = req.query.tutorAvailability;

      //check if tutorUserId is valid
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Failed to get matching dashboard`,
          `Please provide tutorUserId`
        );
        return res.status(200).send(response);
      }

      const matchingDashboard =
        await matchingHelper.getMatchedStudentsByTutorId(
          userId,
          tutorAvailability
        );

      if (!matchingDashboard.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Failed to get matching dashboard`,
          matchingDashboard.message
        );
        return res.status(200).send(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of students who are matched with tutor ${userId}`,
        matchingDashboard.data
      );
      return res.status(200).send(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

//Get list of students to be matched with tutor base on tutor gender, level, sector, availability and subjects
/**
 * @swagger
 * /api/v1/matching/tutor/students-unmatched:
 *   get:
 *     summary: Get Unmatched Students for Tutor
 *     description: Retrieve the list of unmatched students who are ready to be matched with a tutor based on specified criteria.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the tutor.
 *       - in: query
 *         name: gender
 *         schema:
 *           type: string
 *         required: true
 *         description: The gender of the students.
 *       - in: query
 *         name: levelId
 *         schema:
 *           type: string
 *         required: true
 *         description: The level ID of the students.
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         required: true
 *         description: The sector ID of the students.
 *       - in: query
 *         name: tutorAvailability
 *         schema:
 *           type: string
 *         required: true
 *         description: The availability of the tutor.
 *       - in: query
 *         name: subject
 *         schema:
 *           type: string
 *         description: The subject of interest for the students.
 *       - in: query
 *         name: studentName
 *         schema:
 *           type: string
 *         description: The name of the students.
 *       - in: query
 *         name: program
 *         schema:
 *           type: string
 *         description: The program of the students.
 *     responses:
 *       200:
 *         description: List of unmatched students retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of unmatched students.
 *                   items:
 *                     type: object
 *                     description: Student details.
 *       400:
 *         description: Bad request. One or more required parameters are missing.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/tutor/students-unmatched",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const gender = req.query.gender;
      const levelId = req.query.levelId;
      const sectorId = req.query.sectorId;
      const tutorAvailability = req.query.tutorAvailability;
      const subject = req.query.subject;
      const studentName = req.query.studentName;
      const program = req.query.program;
      const departmentId = req.query.departmentId;
      //check if tutorUserId is valid
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `TutorUserId is required`,
          `Please provide tutorUserId`
        );
        return res.status(200).send(response);
      }

      //check if gender is provided
      if (!gender) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Gender is required`,
          `Please provide gender`
        );
        return res.status(200).send(response);
      }
      //check if level is provided
      if (!levelId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `LevelId is required`,
          `Please provide levelId`
        );
        return res.status(200).send(response);
      }

      //check if sector is provided
      if (!sectorId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Sector is required`,
          `Please provide sectorId`
        );
        return res.status(200).send(response);
      }

      //check if tutorAvailability is provided

      if (!tutorAvailability) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Tutor availability is required`,
          `Please provide tutorAvailability`
        );
        return res.status(200).send(response);
      }
      const userIdConnectedUser = req.userId;
      // console.log('rrrrrrrr',userIdConnectedUser )
      const listOfStudentsUnmatched =
        await matchingHelper.getUnmatchedStudentsByTutorId(
          userId,
          gender,
          sectorId,
          levelId,
          tutorAvailability,
          subject,
          studentName,
          program,
          departmentId,
          false,
          userIdConnectedUser
        );

      if (!listOfStudentsUnmatched.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get matching dashboard`,
          listOfStudentsUnmatched.message
        );
        return res.status(200).send(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of students who are are ready to be matched with tutor ${userId}`,
        listOfStudentsUnmatched.data
      );

      return res.status(200).send(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

//#endregion

//#region  STUDENT > MATCHING > TUTOR
//Matching dashboard : Get all Tutors with list of students who have availability slots same as tutor
/**
 * @swagger
 * /api/v1/matching/student/dashboard:
 *   get:
 *     summary: Get Matching Dashboard for Student
 *     description: Retrieve the matching dashboard for a student based on specified filter and sorting criteria.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 10
 *         description: The number of items to return per page.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: The page number.
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: The filter criteria.
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: The field to sort by.
 *     responses:
 *       200:
 *         description: Matching dashboard retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of matching dashboard data for students.
 *                   items:
 *                     type: object
 *                     description: Matching dashboard details for a student.
 *                 page:
 *                   type: integer
 *                   description: The current page number.
 *                 pageSize:
 *                   type: integer
 *                   description: The number of items per page.
 *                 totalNumberOfStudents:
 *                   type: integer
 *                   description: The total number of students matching the criteria.
 *       400:
 *         description: Bad request. One or more required parameters are missing or invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/student/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      let pageSize = apiResponse.buildPageSize(req.query.pageSize);
      let page = apiResponse.buildPage(req.query.page);
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const matchingDashboard = await matchingHelper.matchDashboardStudent(
        filter,
        sortBy,
        page,
        pageSize,
        req.userRole,
        req.userId
      );
      if (!matchingDashboard.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get matching dashboard`,
          matchingDashboard.message
        );
        return res.status(200).send(response);
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Matching dashboard",
        matchingDashboard.data,
        page,
        pageSize,
        matchingDashboard?.totalNumberOfStudents
      );
      return res.status(200).send(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

//get all tutors base on studentUserId who are matched with Student
/**
 * @swagger
 * /api/v1/matching/student/tutor-matched:
 *   get:
 *     summary: Get Matched Tutors for Student
 *     description: Retrieve the list of tutors matched with a student based on specified student availability.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: The user ID of the student.
 *         required: true
 *       - in: query
 *         name: studentAvailability
 *         schema:
 *           type: string
 *         description: The availability of the student.
 *     responses:
 *       200:
 *         description: List of tutors matched with the student retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of tutors matched with the student.
 *                   items:
 *                     type: object
 *                     description: Details of a tutor matched with the student.
 *       400:
 *         description: Bad request. One or more required parameters are missing or invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/student/tutor-matched",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const studentAvailability = req.query.studentAvailability;

      //check if tutorUserId is valid
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Failed to get matching dashboard`,
          `Please provide studentUserId`
        );
        return res.status(200).send(response);
      }

      const matchingDashboard =
        await matchingHelper.getMatchedTutorsByStudentId(
          userId,
          studentAvailability
        );

      if (!matchingDashboard.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Failed to get matching dashboard`,
          matchingDashboard.message
        );
        return res.status(200).send(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of tutors who are matched with Student ${userId}`,
        matchingDashboard.data
      );
      return res.status(200).send(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

//Get list of students to be matched with tutor base on tutor gender, level, sector, availability and subjects
/**
 * @swagger
 * /api/v1/matching/student/tutors-unmatched:
 *   get:
 *     summary: Get Unmatched Tutors for Student
 *     description: Retrieve the list of tutors unmatched with a student based on specified criteria.
 *     tags: [Matching]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         description: The user ID of the student.
 *         required: true
 *       - in: query
 *         name: gender
 *         schema:
 *           type: string
 *         description: The gender of the tutors.
 *         required: true
 *       - in: query
 *         name: levelId
 *         schema:
 *           type: string
 *         description: The ID of the level.
 *         required: true
 *       - in: query
 *         name: sectorId
 *         schema:
 *           type: string
 *         description: The ID of the sector.
 *         required: true
 *       - in: query
 *         name: studentAvailability
 *         schema:
 *           type: string
 *         description: The availability of the student.
 *         required: true
 *       - in: query
 *         name: subject
 *         schema:
 *           type: string
 *         description: The subject of the tutoring.
 *         required: true
 *       - in: query
 *         name: tutorName
 *         schema:
 *           type: string
 *         description: The name of the tutor.
 *       - in: query
 *         name: program
 *         schema:
 *           type: string
 *         description: The program the student is enrolled in.
 *     responses:
 *       200:
 *         description: List of unmatched tutors retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of unmatched tutors.
 *                   items:
 *                     type: object
 *                     description: Details of an unmatched tutor.
 *       400:
 *         description: Bad request. One or more required parameters are missing or invalid.
 *       500:
 *         description: Internal Server Error occurred.
 */
router.get(
  "/matching/student/tutors-unmatched",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      ROLE_PARENT,
      VSC,
    ]),
  ],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const gender = req.query.gender;
      const levelId = req.query.levelId;
      const sectorId = req.query.sectorId;
      const departmentId = req.query.departmentId;
      const studentAvailability = req.query.studentAvailability;
      const subject = req.query.subject;
      const tutorName = req.query.tutorName;
      const program = req.query.program;
      // const is_suzali = req.query.is_suzali;
      //check if tutorUserId is valid
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `StudentUserId is required`,
          `Please provide StudentUserId`
        );
        return res.status(200).json(response);
      }

      //check if gender is provided
      if (!gender) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Gender is required`,
          `Please provide gender`
        );
        return res.status(200).json(response);
      }
      //check if level is provided
      if (!levelId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `LevelId is required`,
          `Please provide levelId`
        );
        return res.status(200).json(response);
      }

      //check if subject is provided
      if (!subject) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Subject is required`,
          `Please provide subject`
        );
        return res.status(200).json(response);
      }

      //check if sector is provided
      if (!sectorId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Sector is required`,
          `Please provide sectorId`
        );
        return res.status(200).json(response);
      }

      //check if tutorAvailability is provided

      if (!studentAvailability) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Student availability is required`,
          `Please provide studentAvailability`
        );
        return res.status(200).json(response);
      }

      const listOfStudentsUnmatched =
        await matchingHelper.getUnmatchedTutorsByStudentId(
          userId,
          gender,
          sectorId,
          levelId,
          studentAvailability,
          subject,
          tutorName,
          program,
          departmentId,
          // is_suzali
        );
      if (!listOfStudentsUnmatched.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to get matching dashboard`,
          listOfStudentsUnmatched.message
        );
        return res.status(200).json(response);
      }
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `The list of tutors who are are ready to be matched with student: ${userId}`,
        listOfStudentsUnmatched.data
      );

      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);

/**
 * @swagger
 * /api/v1/tutor/matchedStudents:
 *   put:
 *     summary: Add Student to Tutor's Matched Students
 *     description: Add a student to the list of matched students for a specified tutor.
 *     tags: [Matching]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               tutorsId:
 *                 type: string
 *                 description: The ID of the tutor.
 *                 example: "60d0fe4f5311236168a109ca"
 *               student:
 *                 type: object
 *                 description: The student object to be added.
 *                 properties:
 *                   userId:
 *                     type: string
 *                     description: The unique identifier of the student.
 *                     example: "60d0fe4f5311236168a109cb"
 *                   name:
 *                     type: string
 *                     description: The name of the student.
 *                     example: "John Doe"
 *     responses:
 *       200:
 *         description: Student added successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Student added successfully"
 *                 tutor:
 *                   type: object
 *                   description: The updated tutor object.
 *       400:
 *         description: Student already exists in matchedStudents.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Student already exists in matchedStudents"
 *       404:
 *         description: Tutor not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Tutor not found"
 *       500:
 *         description: Server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Server error"
 */
router.put(
  "/tutor/matchedStudents",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    try {
      const { tutor: tutorData, student: studentData } = req.body;

      // Validation des données d'entrée
      if (!tutorData?.userId || !studentData?.userId) {
        return res.status(400).json({
          success: false,
          message: "Tutor and student IDs are required",
        });
      }

      // Recherche simultanée du tuteur et de l'étudiant
      const [tutor, student] = await Promise.all([
        TutorPreferencesModel.findOne({ userId: tutorData.userId }),
        StudentPreferencesModel.findOne({ userId: studentData.userId }),
      ]);

      // Vérification de l'existence du tuteur et de l'étudiant
      if (!tutor || !student) {
        return res.status(404).json({
          success: false,
          message: "Tutor or student not found",
        });
      }

      // Vérification des associations existantes
      const studentExists = tutor.matchedStudents?.some(
        (s) => s.userId === studentData.userId
      );
      const tutorExists = student.matchedTutors?.some(
        (t) => t.userId === tutorData.userId
      );

      if (studentExists && tutorExists) {
        return res.status(400).json({
          success: false,
          message: "Association already exists",
        });
      }

      // Mise à jour simultanée des deux collections
      const updatePromises = [];

      if (!studentExists) {
        updatePromises.push(
          TutorPreferencesModel.updateOne(
            { userId: tutorData.userId },
            {
              $addToSet: {
                matchedStudents: {
                  ...studentData,
                  absence: false,
                },
              },
            }
          )
        );
      }

      if (!tutorExists) {
        updatePromises.push(
          StudentPreferencesModel.updateOne(
            { userId: studentData.userId },
            {
              $addToSet: {
                matchedTutors: {
                  ...tutorData,
                  absence: false,
                },
              },
            }
          )
        );
      }

      await Promise.all(updatePromises);

      return res.status(200).json({
        success: true,
        message: "Association created successfully",
        data: {
          tutor: tutorData.userId,
          student: studentData.userId,
        },
      });
    } catch (error) {
      const response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get matching dashboard",
        error
      );
      res.status(500).send(response);
    }
  }
);
router.post(
  "/matching/checkAvailability",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      ROLE_PARENT,
      VSC,
    ]),
  ],
  async (req, res) => {
    try {
       const data = req.body
      const tutor = data.tutor
      const user = data.user
      const requiredParams = [
        { field: "user", message: "user is required" },
        { field: "tutor", message: "Tutor parameter is required" },
      ];

      const missingParam = requiredParams.find(
        (param) => req.body[param.field] = undefined
      );

      if (missingParam) {
        return res
          .status(400)
          .json(
            apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_FAILED,
              missingParam.message,
              `Please provide ${missingParam.field}`,
              400
            )
          );
      }
      let parsedUser = user;
      const availability = parsedUser.availability || [];
        console.log("User:", parsedUser, "Availability:", availability, "Tutor:", tutor);
      // Appel au service de matching
      const listOfStudentsUnmatched =
        await matchingHelper.checkAffectability(
          parsedUser,
          availability,
          tutor,
          false
        );
      const response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Available matches found for this user :  ${parsedUser?.userId}`,
        listOfStudentsUnmatched,
        200
      );
      return res.status(200).send(response);
    } catch (error) {
      console.log("error", error)
      const resp = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to check availability",
        500
      );
      return res.status(500).send(resp);
    }
  }
);
module.exports = router;
