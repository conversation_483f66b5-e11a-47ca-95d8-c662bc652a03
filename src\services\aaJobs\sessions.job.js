const apiLoggerS = require("../../services/logger/LoggerService.js");
const sendGrid = require("@sendgrid/mail");
const schedule = require("node-schedule");
const logger = new apiLoggerS("monitoringWebhooks.Job.js");
const sessionHelper = require("../../controllers/sessions/sessions.helper.js");

const emailHelper = require("../../middleware/mailServiceSendGrid/EmailHelper");
const userHelper = require("../../controllers/user/User.Helper.js");
const moment = require("moment-timezone");
moment.tz.setDefault("Europe/Paris");
const studentPreferenceModel = require("../../models/Student.Preferences.Model.js");
const zupDecoDateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");
const sendGridConstants = require("../../utils/constants/sendgrid.constants.js");
const cron = require("node-cron");
const jobStatusModel = require("../../models/JobStatus.Model.js");
const UserModels = require("../../models/User.Models.js");
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");
const ruleResumeGenerated = new schedule.RecurrenceRule();

ruleResumeGenerated.dayOfWeek = [1, 2, 3, 4, 5];
ruleResumeGenerated.hour = [
  8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21,
];

ruleResumeGenerated.minute = [0, 30];

ruleResumeGenerated.tz = "Europe/Paris";

const sendReminderForSession = schedule.scheduleJob(
  ruleResumeGenerated,
  async function () {
    try {
      const jobStatus = await jobStatusModel.findOneAndUpdate(
        { jobName: "sendReminderForSessionLock", isJobRunning: false },
        { $set: { isJobRunning: true, lastRun: new Date() } },
        { new: true }
      );
      if (!jobStatus) return;
      const listOfSessions = await sessionHelper.listOfAllSessionsInAnHour();
      if (listOfSessions.status && listOfSessions.data.length > 0) {
        await Promise.allSettled(
          listOfSessions.data.map(async (session) => {
            await sendNotificationForSession(session);
          })
        );
      }
    } catch (error) {
      console.error(error);
      logger
        .newLog(logger.getLoggerTypeConstants().sessionsJob)
        .alert(`Error in sendNotificationToTutors: ${error}`);
    } finally {
      try {
        await jobStatusModel.updateOne(
          { jobName: "sendReminderForSessionLock" },
          { $set: { isJobRunning: false, lastRun: new Date() } }
        );
      } catch (error) {
        console.error(error);
        logger
          .newLog(logger.getLoggerTypeConstants().sessionsJob)
          .alert(`Error in sendNotificationToTutors: ${error}`);
      }
    }
  }
);
const sendEndOfSessionReminderToTutor = async ({ email, data, templateId }) => {
  const setupInvitation = {
    // to: email,
    to: ["<EMAIL>"],
    dynamic_template_data: data,
    from: sendGridConstants.ZupDecoEmails.EMAIL_TEAM_ZUPDECO,
    templateId,
  };
  try {
    await sendGrid.send(setupInvitation);
  } catch (error) {
    logger
      .newLog(logger.getLoggerTypeConstants().sessionsJob)
      .alert(`Error in sendNotificationToTutors: ${error}`);
  }
};
const sendReportSummaryReminderToTutors = async () => {
  try {
    const listOfSessions =
      await sessionHelper.listOfAllEndingSessionsInAnHour();
    if (listOfSessions.status && listOfSessions.data.length > 0) {
      for (const session of listOfSessions.data) {
        const { tutors, students } = session;
        const tutor = await userHelper.getUserDetailsByUserId(tutors[0].userId);
        if (tutor) {
          const sessionReportLink = `${process.env.APP_BASE_URL}/tutor/reports`;
          await sendEndOfSessionReminderToTutor({
            // email: tutor.contactDetails.email,
            email: "<EMAIL>",
            data: {
              tutorFirstName: tutor.contactDetails.firstName,
              studentName: students[0].fullName,
              sessionReportLink,
            },
            templateId:
              sendGridConstants.EmailTemplates.TEMPLATE_END_OF_SESSION_REPORT,
          });
        }
      }
    }
  } catch (error) {
    console.error(error);
    logger
      .newLog(logger.getLoggerTypeConstants().sessionsJob)
      .alert(`Error in sendNotificationToTutors: ${error}`);
  }
};

/**
 * Send notifications to a list of users.
 *
 * @param {Array} listOfUsers - The list of users to notify.
 * @param {string} sessionLink - The link to the session.
 * @param {Object} sessionDate - The date of the session.
 * @param {string} subject - The subject of the email.
 */
const sendNotifications = async (
  target,
  date,
  time,
  listOfUsers,
  invitedUsers,
  createdBy,
  sessionLink,
  sessionDate,
  subject
) => {
  for (const user of listOfUsers) {
    let setupInvitation;
    try {
      const userDetails = await userHelper.getUserDetailsByUserId(user.userId);
      if (!userDetails) {
        logger
          .newLog(logger.getLoggerTypeConstants().sessionsJob)
          .info(`No user details found for userId: ${user.userId}`);
        continue;
      }

      let sendTo = new Set(); // Utilisation d'un Set pour éviter les doublons
      const { firstName, lastName, email } = userDetails.contactDetails || {};
      const fullName = firstName + " " + lastName;
      if (email) sendTo.add(email?.toLowerCase());
      if (userDetails.userRole == userRolesConstants.ROLE_STUDENT) {
        // Récupérer les préférences de l'étudiant
        const studentPref = await studentPreferenceModel.findOne({
          userId: user.userId,
        });
        // Récupérer les détails du parent si parentUserId est défini
        let parentEmail;
        if (studentPref && studentPref?.parentUserId) {
          const parentDetails = await UserModels.findOne({
            userId: studentPref.parentUserId,
          });
          parentEmail = parentDetails?.contactDetails?.email;
        }
        // Ajouter les e-mails uniques à la liste d'envoi
        if (
          parentEmail !== null &&
          parentEmail?.toLowerCase() !== email?.toLowerCase()
        )
          sendTo.add(parentEmail.toLowerCase());
      }

      if (createdBy?.userId) {
        setupInvitation = emailHelper.sendSessionNotification(
          [...sendTo],
          target,
          date,
          time,
          invitedUsers[0]?.fullName,
          createdBy?.email,
          subject,
          fullName,
          sessionDate,
          sessionLink
        );
      } else {
        setupInvitation = emailHelper.sendSessionNotification(
          [...sendTo],
          target,
          date,
          time,
          invitedUsers[0]?.fullName,
          null,
          subject,
          fullName,
          sessionDate,
          sessionLink
        );
      }
      await sendGrid.send(setupInvitation);
      console.log(`Notification sent to:`, sendTo);
    } catch (error) {
      logger
        .newLog(logger.getLoggerTypeConstants().sessionsJob)
        .alert(`Error in sendNotificationToTutors: ${error}`);
    }
  }
};

/**
 * Send notifications for a session to tutors and students.
 *
 * @param {Object} sessionDocument - The session document.
 */

const sendNotificationForSession = async (sessionDocument) => {
  console.log("sendNotificationForSession", sessionDocument);
  const { tutors, students, sessionLink, sessionDate, createdBy } =
    sessionDocument;
  const subject = `Votre prochaine session avec ZUPdeCO`;

  const date = moment(sessionDate.startDate)
    .locale("fr")
    .format("DD MMMM YYYY");
  const time = moment(sessionDate.startDate).format("HH:mm");

  const jobStatus = await jobStatusModel.findOne({
    jobName: "sendReminderForSessionLock",
  });
  if (jobStatus && jobStatus.isJobRunning) return; // Évite les exécutions multiples si déjà en cours

  try {
    await sendNotifications(
      (target = "tutor"),
      date,
      time,
      tutors,
      students,
      createdBy,
      sessionLink,
      sessionDate,
      subject
    );
    await sendNotifications(
      (target = "student"),
      date,
      time,
      students,
      tutors,
      createdBy,
      sessionLink,
      sessionDate,
      subject
    );
  } catch (error) {
    console.log("Error in sendNotificationForSession: ${error}");
    logger
      .newLog(logger.getLoggerTypeConstants().sessionsJob)
      .alert(`Error in sendNotificationForSession: ${error}`);
  }
};

const sendNotificationForPresentationSession = async (
  sessionDocument,
  origin
) => {
  try {
    const { tutors, students, sessionLink, sessionDate, createdBy } =
      sessionDocument;
    const studentsDetails = await Promise.all(
      students.map(
        async (student) =>
          await userHelper.getUserDetailsByUserId(student.userId)
      )
    );
    const tutorsDetails = await Promise.all(
      tutors.map(
        async (tutor) => await userHelper.getUserDetailsByUserId(tutor.userId)
      )
    );
    let setupInvitation = {};
    for (const student of studentsDetails) {
      if (student) {
        const subject = `${student.contactDetails.firstName} ${student.contactDetails.lastName}`;
        const date = moment(sessionDate.startDate)
          .locale("fr")
          .format("DD MMMM YYYY");
        const time = moment(sessionDate.startDate).format("HH:mm");
        let tutors = tutorsDetails.map((tutor) => ({
          email: tutor.contactDetails.email,
          name: `${tutor.contactDetails.firstName} ${tutor.contactDetails.lastName}`,
        }));
        const timeStartSession = moment(sessionDate.startDate).format("HH:mm");
        const timeEndSession = moment(sessionDate.endDate).format("HH:mm");

        let parent = {};
        const studentPreferences = await studentPreferenceModel
          .findOne({ userId: student.userId })
          .exec();
        if (studentPreferences) {
          const { parentUserId } = studentPreferences;
          parent = await userHelper.getUserDetailsByUserId(parentUserId);
        }
        let sendTo = [tutors[0].email];
        const studentEmail = student?.contactDetails?.email;
        const parentEmail = parent?.contactDetails?.email;
        if (studentEmail) {
          sendTo.push(studentEmail);
        }
        if (
          parentEmail &&
          studentEmail?.toLowerCase() !== parentEmail?.toLowerCase()
        ) {
          sendTo.push(parentEmail);
        }
        // const sessionLink = `${process.env.APP_BASE_URL}/choose`;

        setupInvitation = {
          to: sendTo,
          ...(createdBy ? { cc: createdBy.email } : {}),
          ...(createdBy ? { replyTo: createdBy.email } : {}),
          dynamic_template_data: {
            name: subject,
            date: date,
            dateTime: time,
            tutorName: tutors[0]?.name,
            studentName: students[0]?.fullName,
            hours: `${timeStartSession} - ${timeEndSession}`,
            sessionLink: sessionLink,
          },
          from: sendGridConstants.ZupDecoEmails.EMAIL_HOME_ZUPDECO,
          templateId: sendGridConstants.SESSION_TEMPLATES_MAPPINGS[origin],
        };

        await sendGrid.send(setupInvitation);
      }
    }
  } catch (error) {
    console.log("error", error);
  }
};

const sendNotificationForCancelledSession = async (sessionDocument) => {
  const { tutors, students, sessionLink, sessionDate, createdBy } =
    sessionDocument;

  const studentsDetails = await Promise.all(
    students.map(
      async (student) => await userHelper.getUserDetailsByUserId(student.userId)
    )
  );
  const tutorsDetails = await Promise.all(
    tutors.map(
      async (tutor) => await userHelper.getUserDetailsByUserId(tutor.userId)
    )
  );

  for (const student of studentsDetails) {
    if (student) {
      const subject = `${student.contactDetails.firstName} ${student.contactDetails.lastName}`;
      const date = moment(sessionDate.startDate).format("DD MMMM YYYY");
      const time = moment(sessionDate.startDate).format("HH:mm");
      let tutors = tutorsDetails.map((tutor) => ({
        email: tutor.contactDetails.email,
        name: `${tutor.contactDetails.firstName} ${tutor.contactDetails.lastName}`,
      }));
      let parent = {};
      const studentPreferences = await studentPreferenceModel
        .findOne({ userId: student.userId })
        .exec();
      if (studentPreferences) {
        const { parentUserId } = studentPreferences;
        parent = await userHelper.getUserDetailsByUserId(parentUserId);
      }

      const setupInvitation = {
        to: [
          tutors[0].email,
          student.contactDetails.email ? student.contactDetails.email : "",
          parent?.contactDetails.email,
        ],
        ...(createdBy ? { cc: createdBy.email } : {}),
        dynamic_template_data: {
          name: subject,
          sessionDay: date,
          sessionHour: time,
          sessionLink: sessionLink,
        },
        from: sendGridConstants.ZupDecoEmails.EMAIL_HOME_ZUPDECO,
        templateId:
          sendGridConstants.EmailTemplates.TEMPLATE_SEND_SESSION_0_CANCELATION,
      };

      try {
        await sendGrid.send(setupInvitation);
      } catch (error) {
        logger
          .newLog(logger.getLoggerTypeConstants().sessionsJob)
          .alert(`Error in sendNotificationToTutors: ${error}`);
      }
    }
  }
};

const sendReportForTodaySessions = async () => {
  try {
    moment.locale("fr");
    const subject = `Rapport des sessions du jour`;
    const reportData = [];
    const listOfSessions = await sessionHelper.listOfAllSessionsToday();
    let totNb = 0;
    let doneSession = 0;
    let doneNotSession = 0;
    let reportNb = 0;

    if (listOfSessions?.status && listOfSessions?.data?.length > 0) {
      for (const session of listOfSessions.data) {
        const sessionData = sessionHelper.createDynamicTemplateData(session);
        reportData.push(sessionData);
      }
      totNb = reportData.length;
      reportData.forEach((session) => {
        if (session.reported == "Saisi") {
          reportNb += 1;
        }
        if (
          (session.tutorAttendance === "Présent" &&
            session.studentCount === 1 &&
            session.studentAttendance == "Présent") ||
          (session.studentCount.length > 1 &&
            session.attendanceCount == session.studentCount.length)
        ) {
          doneSession += 1;
        } else {
          doneNotSession += 1;
        }
      });
    }

    const result = await sessionHelper.getRecieversMail();
    // Initialisation des tableaux pour les emails
    let recieversEmails = [];
    let recieversCCEmails = [];
    // Parcours du tableau de données
    recieversEmails = result?.data?.filter(
      (mail) => mail.principalReciever == true
    );
    recieversCCEmails = result?.data?.filter(
      (mail) => mail.principalReciever == false
    );
    // Obtenir la date actuelle
    const today = moment();
    // Formater la date selon le format souhaité
    const formatDate = today.format("dddd D MMMM YYYY");

    const setupInvitation = {
      to: recieversEmails,
      cc: recieversCCEmails,
      dynamic_template_data: {
        name: subject,
        report: reportData,
        check: reportData?.length > 0 ? true : false,
        date: formatDate,
        totalsessionNb: totNb,
        doneSessionNb: doneSession,
        notDoneSession: doneNotSession,
        reportedSesssionNb: reportNb,
      },
      from: sendGridConstants.ZupDecoEmails.EMAIL_HOME_ZUPDECO,
      templateId: sendGridConstants.EmailTemplates.TEMPLATE_SESSION_REPORT,
    };
    try {
      await sendGrid.send(setupInvitation);
    } catch (error) {
      logger
        .newLog(logger.getLoggerTypeConstants().sessionsJob)
        .alert(`Error in sendNotificationToTutors: ${error}`);
    }
    // }
  } catch (error) {
    console.error(error);
    logger
      .newLog(logger.getLoggerTypeConstants().sessionsJob)
      .alert(`Error in sendNotificationToTutors: ${error}`);
  }
};

// every day at midnight

// let isRunning = false;

cron.schedule(
  "0 20 * * *",
  async () => {
    const jobStatus = await jobStatusModel.findOne({
      jobName: "dailySessionReportLock",
    });
    if (jobStatus && jobStatus.isJobRunning) return; // Évite les exécutions multiples si déjà en cours
    try {
      await jobStatusModel.updateOne(
        { jobName: "dailySessionReportLock" },
        { $set: { isJobRunning: true, lastRun: new Date() } },
        { upsert: true }
      );
      console.log("Sending Report....");
      await sendReportForTodaySessions();
      console.log("Report Sent....");
    } catch (error) {
      console.error(error);
      logger
        .newLog(logger.getLoggerTypeConstants().sessionsJob)
        .alert(`Error in sendNotificationToTutors: ${error}`);
    } finally {
      await jobStatusModel.updateOne(
        { jobName: "dailySessionReportLock" },
        { $set: { isJobRunning: false } }
      );
    }
  },
  {
    scheduled: true,
    timezone: "Europe/Paris",
  }
);

// from Monday to Friday starting from 8 am to 21 pm each hour and 15 minutes
// cron.schedule(
//   "0 15 8-21  * * 1-5",
//   async () => {
//     try {
//       console.log("Sending Reminder....");
//       await sendReportSummaryReminderToTutors();
//       console.log("Reminder sent....");
//     } catch (error) {
//       console.error(error);
//       logger
//         .newLog(logger.getLoggerTypeConstants().sessionsJob)
//         .alert(`Error in sendNotificationToTutors: ${error}`);
//     }
//   },
//   {
//     scheduled: true,
//     timezone: "Europe/Paris",
//   }
// );
module.exports = {
  sendNotificationForSession,
  sendReminderForSession,
  sendNotificationForPresentationSession,
  sendNotificationForCancelledSession,
  sendReportForTodaySessions,
};
