//import luxon library
const { DateTime } = require("luxon");
const moment = require('moment');
//import  user model
const User = require("../../models/User.Models");

//import  child model
const StudentsPreferences = require("../../models/Student.Preferences.Model");

//import userHelper
const userHelper = require("../../controllers/user/User.Helper.js");

const userModel = require("../../models/User.Models.js");
const crscreportsModel = require("../../models/Cr.Report.Model.js");

//import userConstants
const userConstants = require("../../utils/constants/userRolesConstants");

const { v4: uuidv4 } = require("uuid");
const { student } = require("../../services/logger/loggerType");
const { join, filter, find } = require("lodash");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const sessionModel = require("../../models/Sessions.Model");

const establishmentHelper = require("../establishments/establishments.helper.js");

const firebaseHelper = require("../firebase/firebase.helper.js");

const sessionStudentHelper = require("../sessions/sessions.student.helper.js");

const userRoleStatusConstants = require("../../utils/constants/user.role.status.constants.js");
const StudentPreferencesModel = require("../../models/Student.Preferences.Model");
const { userStatus } = require("../../utils/constants/student.constants");
const UserModels = require("../../models/User.Models");

const { getStudentsCountFromPreferences } = require("./student.pipelines.js");
const filterHelper = require("../../utils/filter.js");
const studentConstants = require("../../utils/constants/student.constants.js");
const sessionConstants = require("../../utils/constants/sessions.constants.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const ParentPreferencesModel = require("../../models/Parent.Preferences.Model.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

//getActiveStudents
exports.getActiveStudentsBaseOnTutor = async (tutorId) => {
  try {
    //get list of students base on tutor id
    let studentsPreferences = await StudentsPreferences.find({
      tutors: {
        $elemMatch: {
          tutorUserId: tutorId,
        },
      },
    });
    if (studentsPreferences.length == 0) {
      return {
        status: true,
        message: "Active students fetched successfully",
        data: [],
      };
    }

    let activeStudents = [];

    for (let i = 0; i < studentsPreferences.length; i++) {
      let studentUserId = studentsPreferences[i].userId;
      let userData = await userHelper.getUserDetailsByUserId(studentUserId);
      activeStudents.push(userData);
    }

    //get full name of student and add it to activeStudents array
    let activeStudentsWithFullNameAndNextSessions = [];
    for (let i = 0; i < activeStudents.length; i++) {
      let student = activeStudents[i];
      let fullName =
        student.contactDetails.firstName +
        " " +
        student.contactDetails.lastName;
      let profilePicture = student.profilePic ? student.profilePic : "";

      //get student preferences data from studentsPreferences model base on student id
      let studentPreferencesData = studentsPreferences.find(
        (studentPreference) => studentPreference.userId == student.userId
      );
      let nextSessions = await this.getNextSessionsPerStudent(
        studentPreferencesData
      );

      activeStudentsWithFullNameAndNextSessions.push({
        userId: student.userId,
        fullName: fullName,
        profilePic: profilePicture,
        nextSessions: nextSessions,
      });
    }
    if (activeStudentsWithFullNameAndNextSessions.length == 0) {
      //order activeStudentsWithFullNameAndNextSessions by  dayOfTheWeek and start time
      activeStudentsWithFullNameAndNextSessions.sort(
        (a, b) =>
          (a.nextSessions[0].dayOfTheWeek > b.nextSessions[0].dayOfTheWeek
            ? 1
            : -1) ||
          (a.nextSessions[0].startTime > b.nextSessions[0].startTime ? 1 : -1)
      );

      return {
        status: true,
        message: "Active students fetched successfully",
        data: activeStudentsWithFullNameAndNextSessions,
      };
    } else {
      return {
        status: true,
        message: "Active students fetched successfully",
        data: activeStudentsWithFullNameAndNextSessions,
      };
    }
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting active students ${error}`,
    };
    console.log(response);
    return response;
  }
};

//get list of students base on tutorId, inactive students or students who manage in the past
exports.getInactiveStudentsBaseOnTutor = async (tutorId) => {
  try {
    //get list of students base on tutor id
    let studentsPreferences = await StudentsPreferences.find({
      tutors: {
        $elemMatch: {
          tutorUserId: tutorId,
        },
      },
    });

    let inactiveStudents = [];

    for (let i = 0; i < studentsPreferences.length; i++) {
      let studentUserId = studentsPreferences[i].userId;
      let userData = await userHelper.getUserDetailsByUserId(studentUserId);
      inactiveStudents.push(userData);
    }

    //get full name of student and add it to inactiveStudents array
    let inactiveStudentsWithFullName = [];
    for (let i = 0; i < inactiveStudents.length; i++) {
      let student = inactiveStudents[i];
      let fullName =
        student.contactDetails.firstName +
        " " +
        student.contactDetails.lastName;
      let profilePicture = student.profilePic ? student.profilePic : "";

      inactiveStudentsWithFullName.push({
        userId: student.userId,
        fullName: fullName,
        profilePic: profilePicture,
      });
    }

    return {
      status: true,
      message: "Inactive students fetched successfully",
      data: inactiveStudentsWithFullName,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting inactive students ${error}`,
    };
    console.log(response);
    return response;
  }
};

//get all active students per tutorUserId
exports.getActiveStudentsNumberBaseOnTutor = async (tutorUserId) => {
  try {
    //get list of students base on tutor id
    let studentsPreferences = await StudentsPreferences.find({
      tutors: {
        $elemMatch: {
          tutorUserId: tutorUserId,
        },
      },
    });

    let activeNumberOfStudents = studentsPreferences.length;

    return {
      status: true,
      message: "Active students fetched successfully",
      data: activeNumberOfStudents,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting active students ${error}`,
    };
    console.log(response);
    return response;
  }
};

//get net sessions for student by student id base on student availability
exports.getNextSessionsPerStudent = async (studentPreferencesData) => {
  try {
    //get student preferences
    let studentPreferences = studentPreferencesData;

    //get student availability
    let studentAvailability = studentPreferences.availability;

    //get next sessions
    let nextSessions = [];

    //order studentAvailability by day of the week
    studentAvailability.sort((a, b) =>
      a.dayOfTheWeek > b.dayOfTheWeek ? 1 : -1
    );

    //loop through student availability
    for (const element of studentAvailability) {
      let availability = element;

      //current date time object luxon
      let currentDateTime = DateTime.now();

      //convert to dateTime object availability
      let availabilityStartDateTime = currentDateTime.set({
        weekday: availability.dayOfTheWeek,
        hour: availability.startTime.split(":")[0],
        minute: availability.startTime.split(":")[1],
      });

      let availabilityEndDateTime = currentDateTime.set({
        weekday: availability.dayOfTheWeek,
        hour: availability.endTime.split(":")[0],
        minute: availability.endTime.split(":")[1],
      });

      //check if current date time is between availability start and end date time
      if (currentDateTime < availabilityStartDateTime) {
        //get next sessions
        let nextSessionData = {
          dayOfTheWeek: availability.dayOfTheWeek,
          startTime: availability.startTime,
          endTime: availability.endTime,
        };
        nextSessions.push(nextSessionData);
        //break loop
        break;
      }
    }

    return nextSessions;
  } catch (error) {
    console.log(error);
    let response = {
      status: false,
      message: `Error in getting next sessions ${error}`,
    };
    return response;
  }
};

//Get all Tutor's sessions for student for tutorId, return array of all sessions base on date range
exports.getAllSessionsPerTutorIdCalendarView = async (tutorId) => {
  try {
    let studentPreferencePerTutor = await StudentsPreferences.find({
      tutors: {
        $elemMatch: {
          tutorUserId: tutorId,
        },
      },
    });

    let listOfStudentsDataAndSession = [];

    for (let i = 0; i < studentPreferencePerTutor.length; i++) {
      let studentPreference = studentPreferencePerTutor[i];
      //get student user data
      let studentUserData = await userHelper.getUserDetailsByUserId(
        studentPreference.userId
      );

      //get student sessions
      let studentSessions = studentPreference.availability;

      let studentAndSessionData = {
        fullName:
          studentUserData.contactDetails.firstName +
          " " +
          studentUserData.contactDetails.lastName,
        availability: studentSessions,
      };

      listOfStudentsDataAndSession.push(studentAndSessionData);
    }

    return {
      status: true,
      message: "All sessions fetched successfully",
      data: listOfStudentsDataAndSession,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting all sessions ${error}`,
    };
    console.log(response);
    return response;
  }
};

exports.getAllSessionsPerParentIdCalendarView = async (parentUserId) => {
  try {
    let studentPreferencePerTutor = await StudentsPreferences.find({
      parentUserId: parentUserId,
    });

    let listOfStudentsDataAndSession = [];

    for (let i = 0; i < studentPreferencePerTutor.length; i++) {
      let studentPreference = studentPreferencePerTutor[i];
      //get student user data
      let studentUserData = await userHelper.getUserDetailsByUserId(
        studentPreference.userId
      );

      //get student sessions
      let studentSessions = studentPreference.availability;

      let studentAndSessionData = {
        fullName:
          studentUserData.contactDetails.firstName +
          " " +
          studentUserData.contactDetails.lastName,
        availability: studentSessions,
      };

      listOfStudentsDataAndSession.push(studentAndSessionData);
    }

    return {
      status: true,
      message: "All sessions fetched successfully",
      data: listOfStudentsDataAndSession,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting all sessions ${error}`,
    };
    console.log(response);
    return response;
  }
};

//Get next sessions for student for tutorId, return array of 3 next sessions
exports.getNextTopThreeSessionsForStudent = async (tutorId) => {
  try {
    // activeStudentsWithFullNameAndNextSessions.push({
    //      userId: student.userId,
    //      fullName: fullName,
    //      nextSessions: nextSessions,
    // });

    //order activeStudentsWithFullNameAndNextSessions by  dayOfTheWeek and start time
    // activeStudentsWithFullNameAndNextSessions.sort(
    //      (a, b) => (a.nextSessions[0].dayOfTheWeek > b.nextSessions[0].dayOfTheWeek ? 1 : -1) || (a.nextSessions[0].startTime > b.nextSessions[0].startTime ? 1 : -1)
    // );

    // //get next top three sessions
    // let nextTopThreeSessions = activeStudentsWithFullNameAndNextSessions.slice(0, 3);

    return {
      status: true,
      message: "Next top three sessions for student fetched successfully",
      data: nextTopThreeSessions,
    };
  } catch (error) {
    console.log("Error in getting next top three sessions for student", error);
    return {
      status: false,
      message: `Error in getting next top three sessions for student ${error}`,
    };
  }
};

//Get next sessions for student for parentUserId, return array of 3 next sessions
exports.getNextTopThreeSessionsForStudentBaseOnParentId = async (
  parentUserId,
  threeItems
) => {
  try {
    //get student preferences
    //get list of students base on tutor id
    let studentsPreferences = await StudentsPreferences.find({
      parentUserId: parentUserId,
    });

    let activeStudents = [];

    for (let i = 0; i < studentsPreferences.length; i++) {
      let studentUserId = studentsPreferences[i].userId;
      let userData = await userHelper.getUserDetailsByUserId(studentUserId);
      activeStudents.push(userData);
    }

    //get full name of student and add it to activeStudents array
    let activeStudentsWithFullNameAndNextSessions = [];
    for (let i = 0; i < activeStudents.length; i++) {
      let student = activeStudents[i];
      let fullName =
        student.contactDetails.firstName +
        " " +
        student.contactDetails.lastName;

      //get student preferences data from studentsPreferences model base on student id
      let studentPreferencesData = studentsPreferences.find(
        (studentPreference) => studentPreference.userId == student.userId
      );
      let nextSessions = await this.getNextSessionsPerStudent(
        studentPreferencesData
      );

      activeStudentsWithFullNameAndNextSessions.push({
        userId: student.userId,
        fullName: fullName,
        nextSessions: nextSessions,
      });
    }

    //order activeStudentsWithFullNameAndNextSessions by  dayOfTheWeek and start time
    activeStudentsWithFullNameAndNextSessions.sort(
      (a, b) =>
        (a.nextSessions[0].dayOfTheWeek > b.nextSessions[0].dayOfTheWeek
          ? 1
          : -1) ||
        (a.nextSessions[0].startTime > b.nextSessions[0].startTime ? 1 : -1)
    );

    //get next top three sessions
    let nextTopThreeSessions;
    if (threeItems) {
      nextTopThreeSessions = activeStudentsWithFullNameAndNextSessions.slice(
        0,
        3
      );
    } else {
      nextTopThreeSessions = activeStudentsWithFullNameAndNextSessions;
    }

    return {
      status: true,
      message: "Next top three sessions for student fetched successfully",
      data: nextTopThreeSessions,
    };
  } catch (error) {
    console.log("Error in getting next top three sessions for student", error);
    return {
      status: false,
      message: `Error in getting next top three sessions for student ${error}`,
    };
  }
};

exports.getStudentsProfilesFromUserId = async (listOfUserIds) => {
  try {
    let listOfStudentsProfiles = [];
    for (let i = 0; i < listOfUserIds.length; i++) {
      let userId = listOfUserIds[i];
      let studentProfile = await userHelper.getStudentProfileAndLevel(userId);
      let parentData = await userHelper.getStudentParentDetails(studentProfile.data.parentUserId);
      let studentData = {
        ...studentProfile.data,
        parentData: parentData.data,
      };
      if (studentProfile) {
        listOfStudentsProfiles.push(studentData);
      }
    }
    return {
      status: true,
      message: "Students profiles fetched successfully",
      data: listOfStudentsProfiles,
    };
  } catch (error) {
    console.log("Error in getting students profiles", error);
    return {
      status: false,
      message: `Error in getting students profiles ${error}`,
    };
  }
};

// add  student in user model in database
exports.addStudent = async (parentUserId, student) => {
  try {
    //create new user in database
    const userId = userHelper.generateNewUserId();
    const contactDetails = {
      firstName: student.firstName,
      lastName: student.lastName,
      email: student.email ? student.email : "",
      phoneNumber: student.phoneNumber ? student.phoneNumber : "",
    };
    let user = await User.create({
      userId: userId,
      contactDetails: contactDetails,
      dateOfBirth: student.dateOfBirth,
      gender: student.gender,
      userRole: userConstants.ROLE_STUDENT,
    });
    let response = await user.save();

    // add student in Student Preferences Model
    const levelObject = [
      {
        levelName: "CM1",
        levelId: "640bacd8ccd55123460565d8",
      },
    ];
    let studentsAssignment = {
      //  subjectToStudy: [doesYourChildNeedHelpWith],
      establishments: student.establishment,
      level: levelObject,
    };
    let studentPreferences = await StudentsPreferences.create({
      userId: userId,
      parentUserId: parentUserId,
      institutionName: student.institutionName,
      availability: student.availability ? student.availability : [],
      comments: student.comments ? student.comments : "",
      // suiviCoordo: student.suiviCoordo ? student.suiviCoordo : "",
      // doesYourChildNeedHelpWith: student.doesYourChildNeedHelpWith,
      assignment: studentsAssignment,
    });

    let newStudentPreferences = await studentPreferences.save();

    //combine student preferences and user data merged
    let studentData = { ...response._doc, ...newStudentPreferences._doc };

    return {
      status: true,
      message: "Student added successfully",
      data: studentData,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in adding student ${error}`,
    };
    console.log(response);
    return response;
  }
};

//get student preferences
exports.getStudentPreferences = async (studentId) => {
  try {
    let studentPreferences = await StudentsPreferences.findOne({
      userId: studentId,
    });
    if (!studentPreferences) {
      return { status: false, message: "Student preferences not found" };
    }
    //get establishment name from establishmentId
    let establishmentsList = studentPreferences?.assignment?.establishments
      ? studentPreferences.assignment.establishments
      : [];
    for (let i = 0; i < establishmentsList.length; i++) {
      let establishment = establishmentsList[i];
      let establishmentId = establishment?.establishmentId;

      const establishmentName =
        (await establishmentHelper.getEstablishmentName(establishmentId)) ||
        establishment?.establishmentName;

      //update establishmentsList with establishmentName
      if (
        establishmentsList[i] !== null &&
        establishmentsList[i] !== undefined
      ) {
        establishmentsList[i].establishmentName = establishmentName;
      }
    }

    //update studentPreferences with establishmentsList
    if (establishmentsList.length > 0) {
      studentPreferences.assignment.establishments = establishmentsList;
    }

    const parentUserId = studentPreferences.parentUserId
      ? studentPreferences.parentUserId
      : "";

    let parentFullName;
    let parentOnBoarding;
    let parentAddress;
    if (parentUserId) {
      const parentUser = await User.findOne({
        userId: parentUserId,
        userRole: userConstants.ROLE_PARENT,
      });
      const parentPref = await ParentPreferencesModel.findOne({
        userId: parentUserId,
      });
      if (parentUser && parentPref) {
        parentFullName =
          parentUser.contactDetails.firstName +
          " " +
          parentUser.contactDetails.lastName;
        parentAddress = parentUser?.address;
      }
      parentOnBoarding = {
        onBoardingStep: parentPref?.onBoardingStep,
        quizScore: parentPref?.quizScore,
      };
    }
    const parentData = {
      parentFullName: parentFullName,
      parentUserId: parentUserId,
      parentOnBoarding,
      parentAddress,
    };

    const combineData = { ...studentPreferences._doc, parentData };
    return {
      status: true,
      message: "Student preferences fetched successfully",
      data: combineData,
    };
  } catch (error) {
    console.log(error);
    let response = {
      status: false,
      message: `Error in getting student preferences ${error}`,
    };
    return response;
  }
};

//update student preferences
exports.updateStudentPreferences = async (studentId, studentPreferences) => {
  try {
    let oldData = await this.getStudentPreferences(studentId);

    let studentPreferencesData = await StudentsPreferences.findOne({
      userId: studentId,
    });

    if (studentPreferences.level && studentPreferences.level.length > 0) {
      studentPreferencesData.assignment.level = studentPreferences.level;
    }
    studentPreferencesData.institutionName = studentPreferences.institutionName
      ? studentPreferences.institutionName
      : oldData.data.institutionName;
    studentPreferencesData.availability = studentPreferences.availability
      ? studentPreferences.availability
      : oldData.data.availability;

    if (
      studentPreferences.establishment &&
      studentPreferences.establishment.length > 0
    ) {
      studentPreferencesData.assignment.establishments =
        studentPreferences.establishment;
    }

    studentPreferencesData.comments = studentPreferences.comments
      ? studentPreferences.comments
      : oldData.data.comments;
    studentPreferencesData.suiviCoordo = studentPreferences.suiviCoordo
      ? studentPreferences.suiviCoordo
      : oldData.data.suiviCoordo;
    if (
      studentPreferences.subjectToStudy &&
      studentPreferences.subjectToStudy.length > 0
    ) {
      studentPreferencesData.assignment.subjectToStudy =
        studentPreferences.subjectToStudy;
    }
    // if (studentPreferences.doesYourChildNeedHelpWith) {
    //      studentPreferencesData.doesYourChildNeedHelpWith = studentPreferences.doesYourChildNeedHelpWith;
    //      studentPreferencesData.assignment.subjectToStudy = [studentPreferences.doesYourChildNeedHelpWith];
    // }

    await studentPreferencesData.save();

    return {
      status: true,
      message: "Student preferences updated successfully",
      data: studentPreferencesData,
    };
  } catch (error) {
    console.log(error);
    let response = {
      status: false,
      message: `Error in updating student preferences ${error}`,
    };
    return response;
  }
};

//delete  student from user model in database and Student Preferences Model
exports.deleteStudent = async (studentId) => {
  try {
    //find  user
    const userDocument = await User.findOne({ userId: studentId });

    if (!userDocument) {
      return { status: false, message: "Student not found" };
    }

    const firebaseIds = userDocument.firebaseIds;

    //delete user from database
    await userDocument.remove();

    //delete student from Student Preferences Model
    const studentPreference = await StudentsPreferences.findOne({
      userId: studentId,
    });
    if (!studentPreference) {
      //delete studentPreferences from database
      await studentPreference.remove();
    }

    //delete student from firebase
    for (let i = 0; i < firebaseIds.length; i++) {
      const firebaseId = firebaseIds[i];
      await firebaseHelper.deleteUserFromAuth(firebaseId.identifier);
    }
    return { status: true, message: "Student deleted successfully" };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in deleting student ${error.message}`,
    };
    return response;
  }
};

//generate random invitation code from email or phone number and add 10 random characters
exports.generateInvitationCode = (firebaseIdentifier) => {
  let invitationCode = firebaseIdentifier + "=" + uuidv4();
  return invitationCode;
};

//save invitation code in database for studentPreferences
exports.saveInvitationCodeInDb = async (invitationCode, studentUserId) => {
  try {
    let studentPreferences = await StudentsPreferences.findOne({
      userId: studentUserId,
    });

    studentPreferences.invitation.invitationCode = invitationCode;
    studentPreferences.invitation.invitationAccepted = false;
    studentPreferences.invitation.invitationDate = new Date();
    await studentPreferences.save();

    return { status: true, message: "Invitation code saved successfully" };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in saving invitation code ${error.message}`,
    };
    return response;
  }
};

//check if invitation code is valid
exports.checkInvitationCode = async (invitationCode, userId) => {
  try {
    let studentPreferences = await StudentsPreferences.findOne({
      "invitation.invitationCode": invitationCode,
      userId: userId,
    });

    if (studentPreferences) {
      return { status: true, message: "Invitation code is valid" };
    } else {
      return { status: false, message: "Invitation code is not valid" };
    }
  } catch (error) {
    let response = {
      status: false,
      message: `Error in checking invitation code ${error.message}`,
    };
    return response;
  }
};

//extract invitation code in two parts email or phone number and random characters
exports.extractInvitationCode = (invitationCode) => {
  try {
    let invitationCodeArray = invitationCode.split("=");
    let firebaseIdentifier = invitationCodeArray[0];
    let randomCharacters = invitationCodeArray[1];

    return {
      firebaseIdentifier: firebaseIdentifier,
      randomCharacters: randomCharacters,
    };
  } catch (error) {
    return { firebaseIdentifier: null, randomCharacters: null };
  }
};

//accept invitation code, update invitationAccepted to true
exports.acceptInvitationCode = async (invitationCode, userId) => {
  try {
    let studentPreferences = await StudentsPreferences.findOne({
      "invitation.invitationCode": invitationCode,
    });
    studentPreferences.invitation.invitationAccepted = true;
    studentPreferences.invitation.invitationAcceptedDate = new Date();
    await studentPreferences.save();

    console.log("Data is saved in database");

    return { status: true, message: "Invitation code accepted successfully" };
  } catch (error) {
    console.log("studentHelper.acceptInvitationCode", error);
    let response = {
      status: false,
      message: `Error in accepting invitation code ${error.message}`,
    };
    return response;
  }
};

//get getAllChildrenOfParent
exports.getAllChildrenOfParent = async (parentUserId) => {
  try {
    let students = await StudentsPreferences.find({
      parentUserId: parentUserId,
    });

    let studentsDataList = [];
    //get user data of students
    for (let i = 0; i < students.length; i++) {
      let student = students[i];
      const userId = student.userId;

      const matching = student.matching;

      let tutorProfile;
      let referentProfile;

      let studentUserData = await userHelper.getUserDetailsByUserId(userId);

      const pastSessions = await sessionStudentHelper.getPastSessions(userId);

      const nextSession = await sessionStudentHelper.getNextSessionsPerStudent(
        userId
      );

      if (nextSession.status) {
        tutorProfile = nextSession.data.tutorProfile;
        referentProfile = nextSession.data.vsc[0];
      } else {
        tutorProfile = null;
        referentProfile = null;
      }

      let studentDataContact = {
        email:
          studentUserData && studentUserData.contactDetails.email
            ? studentUserData.contactDetails.email
            : "",
        phoneNumber:
          studentUserData && studentUserData.contactDetails.phoneNumber
            ? studentUserData.contactDetails.phoneNumber
            : "",
        firstName:
          studentUserData && studentUserData.contactDetails.firstName
            ? studentUserData.contactDetails.firstName
            : "",
        lastName:
          studentUserData && studentUserData.contactDetails.lastName
            ? studentUserData.contactDetails.lastName
            : "",
        userId: userId,
        firebaseIds:
          studentUserData && studentUserData.firebaseIds
            ? studentUserData.firebaseIds
            : [],
      };
      //combine student data and student preferences
      let studentData = {
        ...student._doc,
        ...studentDataContact,
        pastSessions: pastSessions.data,
        nextSession: nextSession.data,
        tutorProfile: tutorProfile,
        referentProfile: referentProfile,
      };
      studentsDataList.push(studentData);
    }

    return {
      status: true,
      message: "Students fetched successfully",
      data: studentsDataList,
    };
  } catch (error) {
    console.log("studentHelper.getAllChildrenOfParent", error);
    let response = {
      status: false,
      message: `Error in getting students ${error.message}`,
    };
    return response;
  }
};

//get listOfStudents base on parentUserId
exports.studentsListPerParent = async (parentUserId) => {
  try {
    let listOfStudents = await StudentsPreferences.find({
      parentUserId: parentUserId,
    });

    if (!listOfStudents) {
      return { status: false, message: `No students found` };
    }
    let studentsDataList = [];
    for (let i = 0; i < listOfStudents.length; i++) {
      let student = listOfStudents[i];
      const userId = student.userId;
      let studentUserDocument = await userHelper.getUserDetailsByUserId(userId);

      let studentCombinedData = {
        ...studentUserDocument?._doc,
        ...student?._doc,
      };
      studentsDataList.push(studentCombinedData);
    }

    return {
      status: true,
      message: "Students fetched successfully",
      data: studentsDataList,
    };
  } catch (error) {
    console.log("studentHelper.allStudentsBaseOnParentUserId", error);
    return {
      status: false,
      message: `Error in getting students ${error.message}`,
    };
  }
};

//get all children details base  on userId
exports.studentDetails = async (userId) => {
  try {
    //get userDetails
    let userDocument = await User.findOne({ userId: userId });

    //get student preferences
    let studentPreferences = await StudentsPreferences.findOne({
      userId: userId,
    });

    let studentData = {
      userId: userDocument.userId,
      firstName: userDocument.contactDetails.firstName,
      lastName: userDocument.contactDetails.lastName,
      email: userDocument.contactDetails.email
        ? userDocument.contactDetails.email
        : "",
      phoneNumber: userDocument.contactDetails.phoneNumber
        ? userDocument.contactDetails.phoneNumber
        : "",
      gender: userDocument.gender ? userDocument.gender : "",
      dateOfBirth: userDocument.dateOfBirth,
      level: studentPreferences.level,
      institutionName: studentPreferences.institutionName,
      availability: studentPreferences.availability,
      establishment: studentPreferences.establishment,
      comments: studentPreferences.comments ? studentPreferences.comments : "",
      suiviCoordo: studentPreferences.suiviCoordo
        ? studentPreferences.suiviCoordo
        : "",
      doesYourChildNeedHelpWith: studentPreferences.doesYourChildNeedHelpWith
        ? studentPreferences.doesYourChildNeedHelpWith
        : "",
    };

    return {
      status: true,
      message: "Student details fetched successfully",
      data: studentData,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting student details ${error.message}`,
    };
    return response;
  }
};

//Get getRightReportsPerStudent
exports.getRightReportsPerStudent = async (studentId) => {
  try {
    let combinedData = {
      sessions: 40,
      absence: 10,
      referent: "Cody Fisher",
    };

    return {
      status: true,
      message: "Right reports fetched successfully",
      data: combinedData,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting right reports ${error.message}`,
    };
    return response;
  }
};

//Get getStudentAvailability
exports.getStudentAvailability = async (studentId) => {
  try {
    let studentAvailability = await StudentsPreferences.findOne({
      userId: studentId,
    });

    return {
      status: true,
      message: "Student availability fetched successfully",
      data: studentAvailability.availability,
    };
  } catch (error) {
    let response = {
      status: false,
      message: `Error in getting student availability ${error.message}`,
    };
    return response;
  }
};

//#region ADMINISTRATOR FUNCTIONS

//check if exists student with the same first name and last name and userRole student
exports.checkIfStudentExists = async (firstName, lastName, userId) => {
  //if userId is not null, then check if student exists with the same first name and last name and userId is not equal to userId
};

//create student
exports.createStudentIS = async (studentData) => {
  try {
    //get student preferences from object
    const studentPreference = {
      ...studentData.studentPreferences,
      program: this.addPriorityToProgram(
        studentData?.studentPreferences?.program?.programId
      ),
    };

    //delete student preference from student data
    delete studentData.studentPreference;

    //generate new userId
    const userId = userHelper.generateNewUserId();
    //create user
    const user = await User.create({
      userId: userId,
      userRole: userConstants.ROLE_STUDENT,
      hasChangedPassword: true,
      ...studentData,
    });

    //check if user is created
    if (!user) {
      return {
        status: false,
        message: `Error in creating student`,
        data: null,
      };
    }

    //create student preferences
    const studentPreferenceData = await StudentsPreferences.create({
      userId: userId,
      contactDetails: studentData.contactDetails,
      gender: studentData.gender,
      ...studentPreference,
    });
    //return response combined
    const studentDataResponse = {
      ...user._doc,
      studentPreferences: studentPreferenceData._doc,
    };

    return {
      status: true,
      message: "Student created successfully",
      data: studentDataResponse,
    };
  } catch (error) {
    return {
      status: false,
      message: `Error in creating student ${error.message}`,
    };
  }
};

exports.createStudentPreferences = async (studentData) => {
  try {
    //create student preferences
    return await StudentsPreferences.create({
      userId: studentData.userId,
      contactDetails: studentData.contactDetails,
    });
  } catch (error) {
    return {
      status: false,
      message: `Error in creating student preferences ${error.message}`,
    };
  }
};

//update student
exports.updateStudentIS = async (studentData) => {
  try {
    const studentPreference = studentData.studentPreferences;

    //delete student preference from student data
    delete studentData.studentPreference;

    const userId = studentData.userId;

    const user = await User.findOne({ userId: userId }).exec();
    const userEmail = user.contactDetails.email;
    // check if student status is STOP or AWAITING_TUTOR, then cancel all sessions
    if (
      studentData?.studentPreferences?.situation ===
        studentConstants.studentStatus.STOP ||
      studentData?.studentPreferences?.situation ==
        studentConstants.studentStatus.AWAITING_TUTOR ||
        studentData?.userStatus ==
        studentConstants.userStatus.DEACTIVATED 
    ) {
      try {
        const dateNow = dateTimeHelper.getCurrentDateInParisZone();
        const sessionToCancel = await sessionModel.find({
          "students.userId": userId,
          "sessionDate.startDate": { $gte: dateNow },
        });
        // Extraire les sessionIds
        const sessionIds = sessionToCancel.map((session) => session.sessionId);
        const updateSessionsPromise = sessionModel.updateMany(
          { sessionId: { $in: sessionIds } },
          { status: sessionConstants.sessionsStatus.CANCELED }
        );

        const unmatchStudentPromise = StudentPreferencesModel.findOneAndUpdate(
          { userId, "matching.sessionId": { $in: sessionIds } },
          {
            $set: {
              "matching.$[elem].status":
                sessionConstants.sessionsStatus.CANCELED, // Mise à jour du statut de chaque session correspondante
              matchedTutors: [], // Réinitialisation de matchedTutors
            },
          },
          {
            arrayFilters: [
              // Filtres pour cibler uniquement les éléments du tableau matching
              { "elem.sessionId": { $in: sessionIds } },
            ],
            new: true,
          }
        );

        const unmatchTutorPromise = TutorPreferencesModel.findOneAndUpdate(
          { "matching.sessionId": { $in: sessionIds } }, // Critère pour trouver les sessions à mettre à jour
          {
            $pull: { matchedStudents: { userId: userId } }, // Suppression des étudiants correspondant
            $set: {
              "matching.$[elem].status":
                sessionConstants.sessionsStatus.CANCELED,
            }, // Mise à jour du statut
          },
          {
            arrayFilters: [
              // Filtres pour cibler uniquement les éléments du tableau matching
              { "elem.sessionId": { $in: sessionIds } },
            ],
            new: true,
          }
        );

        await Promise.all([
          updateSessionsPromise,
          unmatchStudentPromise,
          unmatchTutorPromise,
        ]);
      } catch (error) {
        console.error(`Failed to update records for userId ${userId}:`, error);
        // Additional error handling logic could go here, like logging to a monitoring service
      }
    }

    //update tutor in User in firebase
    let userDocument;
    if (!userEmail) {
      userDocument = await User.findOneAndUpdate(
        { userId: userId },
        { ...studentData, status: studentData.userStatus },
        { new: true }
      ).exec();
    } else {
      const responseFirebase = await firebaseHelper.resetEmail(
        userEmail,
        studentData.contactDetails.email
      );

      if (!responseFirebase) {
        return {
          status: false,
          message: "Email studeent not found in firebase",
          data: null,
        };
      }

      if (responseFirebase.status !== 404) {
        userDocument = await User.findOneAndUpdate(
          { userId: userId },
          {
            $set: {
              ...studentData,
              status: studentData.userStatus,
              "firebaseIds.0.identifier": studentData.contactDetails.email,
            },
          },
          { new: true }
        ).exec();
      } else {
        userDocument = await User.findOneAndUpdate(
          { userId: userId },
          { ...studentData, status: studentData.userStatus },
          { new: true }
        ).exec();
      }
    }

    //check if user is created
    if (!userDocument) {
      return {
        status: false,
        message: `Error in updating student`,
        data: null,
      };
    }

    //update student preferences
    studentPreference.contactDetails = userDocument.contactDetails;
    studentPreference.gender = userDocument.gender;

    const studentPreferenceData = await StudentsPreferences.findOneAndUpdate(
      { userId: userId },
      {
        ...studentPreference,
        userStatus: studentData.userStatus,
        program: this.addPriorityToProgram(
          studentPreference?.program?.programId
        ),
      },
      { new: true }
    ).exec();
    //return response combined
    const studentDataResponse = {
      ...userDocument._doc,
      studentPreferences: studentPreferenceData._doc,
    };

    return {
      status: true,
      message: "Student updated successfully",
      data: studentDataResponse,
    };
  } catch (error) {
    console.log("studentHelper.updateStudentIS", error);
    return {
      status: false,
      message: `Error in updating student ${error.message}`,
    };
  }
};

exports.getISStudentDashboard = async (payload) => {
  let { page, pageSize, filter, sortByObject, userId, userRole, strictProgramFilter } = payload;
  const fiteredData = payload.filter ? JSON.parse(filter) : "";
  
  // Vérifier si le filtre vide est présent
  if (fiteredData && fiteredData.vide === "true") {
    return {
      status: true,
      message: "Aucun étudiant trouvé",
      data: [],
      page: 1,
      pageSize: 0,
      totalNumberOfStudents: 0
    };
  }
  
  pageSize = buildPageSize(pageSize);
  page = buildPage(page);
  let filterData = buildFilter(filter);
  if (!filterData.status) {
    // return { status: false, message: filterData.message };
    filterData = {
      status: true,
      data: {
        student: {},
        preference: {},
      },
    };
  }
  const { student, preference } = filterData.data;

  // Si strictProgramFilter est vrai et qu'il y a un filtre de programme,
  // utiliser directement le filtre de programme fourni sans appliquer la logique de rôle
  if (strictProgramFilter && fiteredData && fiteredData.program) {
    // Remplacer le filtre de programme par défaut par le filtre strict
    preference["program.programId"] = fiteredData.program;
  }
  // Sinon, appliquer la logique de filtrage par rôle normale
  else if (
    [userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(userRole)
  ) {
    // get the admin preferences model of the user
    const userAdminDetails = await UserAdministrationModel.findOne({ userId });

    // get the program preferences of the user
    const pref = userAdminDetails?.administrationPreferences;

    // check if the user has program preferences
    if (pref && pref.program?.length) {
      // add the program preferences to the filter to match the student preferences with the programId got from admin preferences model
      preference["program.programId"] = {
        $in:
          pref.program[0].programId === "az-per-2ca0z9s" ||
          pref.program[0].programId === "az-rem-4ca0f8d"
            ? ["az-per-2ca0z9s", "az-rem-4ca0f8d"]
            : pref.program.map((p) => p.programId),
      };
      // get the useradmin program from the program preferences
      const program = pref.program[0];
      // check if the program is (DEVOIRS_FAITS or ZUPDEFOOT) Or Home Classe
      if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
        //Tutorat solidaire or ZupdeFoot userAdmin

        // Step 1
        // get the establishmentIds from the admin preferences model
        const establishmentIds = pref.establishment.map(
          (est) => est.establishmentId
        );
        // Step 2
        //  check if the user has establishmentIds
        if (!establishmentIds?.length) {
          // in case of coordinator/vsc has no establishmentIds in his admin preferences model
          preference["_id"] = null;
        } else {
          // Step 3
          // in case of coordinator/vsc has multiple establishmentIds in his admin preferences model
          // add establishmentIds to the filter to match the student preferences with the programId && establishementIds got from admin preferences model
          // Example : this id "64e38c176c4b64e21ce131a2" is an example of establishmentId that has students in tutorat solidaire program
          // Example Explanation : For Test (You should have a TS userAdmin coord/vsc that assigned to this establishment:"64e38c176c4b64e21ce131a2")
          preference["assignment.establishments.establishmentId"] = {
            $in: establishmentIds,
          };
        }
      } else if (
        program.programId === HOME_CLASSES.programId ||
        program.programId === CLASSSES_HOME.programId ||
        program.programId === ZUPDEFOOT.programId
      ) {
        // Home Classe userAdmin
        // Step 1
        // get the departementIds from the admin preferences model
        const departmentIds = pref.department.map((dep) => dep.departmentId);
        // Step 2
        // check if the user has departementIds
        if (!departmentIds?.length) {
          // in case of coordinator/vsc has no departementIds in his admin preferences model
          preference["_id"] = null;
        } else {
          // in case of coordinator/vsc has adress zipcode in his admin preferences model
          if (preference["assignment.department.departmentId"]) {
            // Si un filtre de département existe déjà, on fusionne avec $and
            preference["$and"] = [
              { "assignment.department.departmentId": { $in: departmentIds } },
              { "assignment.department.departmentId": preference["assignment.department.departmentId"] }
            ];
            delete preference["assignment.department.departmentId"];
          } else {
            preference["assignment.department.departmentId"] = {
              $in: departmentIds,
            };
          }
          preference["program.programId"] = program.programId;
        }
      }
    }
  }
  let sortBy = buildSortBy(sortByObject);

  const result = await StudentsPreferences.countDocuments({
    ...preference,
    ...student,
  });
  let totalNumberOfStudents = result;

  let studentPreference = await StudentsPreferences.find({
    ...preference,
    ...student,
  })
    .sort(sortBy)
    .skip((page - 1) * pageSize)
    .limit(pageSize)
    .exec();
  const onBoardingStepValue = Number(fiteredData.onBoardingStep);

  if (onBoardingStepValue) {
    // Fetch all parent userIds for the given onBoardingStep in a single query
    const parentIdsSet = new Set(
      await ParentPreferencesModel.find({
        onBoardingStep: onBoardingStepValue,
      }).distinct("userId")
    );

    // Filter studentPreference based on parentIds
    studentPreference = studentPreference.filter((student) =>
      parentIdsSet.has(student.parentUserId)
    );
  }

  // Collect all unique parentUserIds from the remaining studentPreference
  const parentUserIds = [
    ...new Set(studentPreference.map((student) => student.parentUserId)),
  ];

  // Fetch all parents in one query
  const parents = await ParentPreferencesModel.find({
    userId: { $in: parentUserIds },
  });
  // Create a Map for parent data for faster lookup
  const parentMap = new Map(parents.map((parent) => [parent.userId, parent]));
  // Map student data
  const studentListData = await Promise.all(
    studentPreference.map(async (student) => {
      const {
        userId,
        contactDetails = {},
        userStatus,
        situation,
        program = "",
        typeOfStudent = "",
        userRole,
        assignment = {},
        isPartner,
        matchedTutors = [],
        partner,
        createdAt,
        iAcceptToBeContactedForParticipatingInAStudy,
        availability = [],
        gender = [],
      } = student;
  
      const parent = parentMap.get(student.parentUserId);
  
      const establishmentNames = (assignment.establishments || [])
        .map((e) => e?.establishmentName)
        .filter(Boolean)
        .join(", ");
  
      const affectedStatus =
        matchedTutors.length > 0 ? "affected" : "not_affected";
  
      const folderStatus =
        userStatus === "active" &&
        createdAt &&
        availability.length > 0 &&
        assignment.level?.length > 0 &&
        assignment.subjectToStudy?.length > 0 &&
        gender.length > 0 &&
        (contactDetails.phoneNumber || contactDetails.email);
  
      // 🔄 Opérations async
      const sessionPassesNbre = await sessionModel.countDocuments({
        status: "session-0-to-be-scheduled",
        "students.userId": userId,
        "sessionDate.startDate": { $lte: new Date() },
        parentSessionId : {$ne : null}
      });
  
      const nbrCr = await crscreportsModel.countDocuments({
        status: { $ne: "to-be-entered" },
        "students.userId": userId,
      });
  
      const aggregationResult = await crscreportsModel.aggregate([
        {
          $match: {
            status: { $ne: "to-be-entered" },
            "students.userId": userId,
          },
        },
        {
          $group: {
            _id: null,
            sumGrade: { $sum: "$grade" },
            count: { $sum: 1 },
          },
        },
      ]);
  
      const SumGradeOptimized =
        aggregationResult.length > 0 ? aggregationResult[0].sumGrade : 0;
  
      return {
        userId,
        firstName: contactDetails.firstName || "",
        lastName: contactDetails.lastName || "",
        email: contactDetails.email || "",
        userStatus,
        situation,
        program,
        typeOfStudent,
        userRole,
        establishment: establishmentNames || "",
        department: assignment.department?.departmentName
          ? `${extractCityName(assignment.department.departmentName)} (${assignment.department.departmentId})`
          : "",
        isPartner,
        matchedTutors,
        partner,
        createdAt,
        iAcceptToBeContactedForParticipatingInAStudy,
        folderStatus,
        affectedStatus,
        onBoardingStepValue: parent?.onBoardingStep || null,
        nbrCr: nbrCr,
        note:  nbrCr > 0 ? Math.floor(SumGradeOptimized / nbrCr) : 0,
        pourcentageCr : ((nbrCr/sessionPassesNbre) *100) || 0,
        nbrSession: sessionPassesNbre,
        gender: gender || [], // Ajout du champ gender
      };
    })
  );

  return {
    status: true,
    message: "Students",
    data: studentListData,
    totalNumberOfStudents: totalNumberOfStudents,
    page: page,
    pageSize: pageSize,
  };
};
function extractCityName(input) {
  // Utilise une expression régulière pour extraire le nom de la ville, en éliminant les codes éventuels
  const match = input.match(/^(.+?)(?:\s*\(\d{2}\))?$/);

  if (match) {
    // Extrait et retourne le nom de la ville sans les codes
    return match[1];
  }

  // Retourne l'entrée d'origine si aucune correspondance n'est trouvée
  return input;
}

//delete student
exports.deleteStudentIS = async (studentId) => {
  try {
    //find  user
    const userDocument = await User.findOne({ userId: studentId });
    if (!userDocument) {
      return { status: false, message: "Student not found" };
    }

    const firebaseIds = userDocument.firebaseIds;

    //delete user from database
    await userDocument.delete();

    //delete student from Student Preferences Model
    const studentPreference = await StudentsPreferences.findOne({
      userId: studentId,
    });
    if (studentPreference) {
      //delete studentPreferences from database
      await studentPreference.delete();
    }

    if (userDocument && studentPreference) {
      //delete student from firebase
      for (let i = 0; i < firebaseIds.length; i++) {
        const firebaseId = firebaseIds[i];
        await firebaseHelper.deleteUserFromAuth(firebaseId.identifier);
      }
      return { status: true, message: "Student deleted successfully" };
    } else {
      return {
        status: false,
        message: "Error in deleting student",
        data: null,
      };
    }
  } catch (error) {
    console.log("studentHelper.deleteStudentIS", error);
    let response = {
      status: false,
      message: `Error in deleting student ${error.message}`,
    };
    return response;
  }
};
function buildFilter(filter) {
  if (!filter) return { status: false, message: "Filter is not provided" };

  if (!isJSON(filter)) {
    return { status: false, message: "Filter is not a valid JSON" };
  }

  const parsedFilter = JSON.parse(filter);
  let studentFilter = {};
  const preferencesFilter = {};

  // Helper function to create regular expression object for a given key and value
  const createRegExpObject = (key, value) => ({
    [`contactDetails.${key}`]: { $regex: value, $options: "i" },
  });

  for (const [key, value] of Object.entries(parsedFilter)) {
    console.log("key", key);
    if (key === "globalSearch" && value) {
      studentFilter = {
        ...studentFilter,
        $or: [
          { "contactDetails.firstName": { $regex: value, $options: "i" } },
          { "contactDetails.lastName": { $regex: value, $options: "i" } },
          { "contactDetails.email": { $regex: value, $options: "i" } },
          { "userId": { $regex: value, $options: "i" } }
        ]
      };
      continue; // Passer à l'itération suivante
    }
    switch (key) {
      case "lastName":
      case "firstName":
        Object.assign(studentFilter, createRegExpObject(key, value));
        break;
      case "email":
        studentFilter["contactDetails.email"] =
          filterHelper.createEmailRegex(value);
        break;
      case "userStatus":
        studentFilter.userStatus = { $regex: value, $options: "i" };
        break;
      case "program":
        preferencesFilter["program.programId"] = {
          $regex: value,
          $options: "i",
        };
        break;
      case "department":
        console.log("department", value);
        preferencesFilter["assignment.department.departmentId"] = {
          $eq: value.toString(),
        };
        break;
      case "establishment":
        preferencesFilter["assignment.establishments"] = {
          $elemMatch: {
            establishmentId: value,
          },
        };
        break;
      case "partner":
        preferencesFilter.partner = { $regex: value, $options: "i" };
        break;
      case "situation":
        preferencesFilter.situation = { $regex: value, $options: "i" };
        break;
      case "study":
        preferencesFilter.iAcceptToBeContactedForParticipatingInAStudy = value;
        break;
      case "folderStatus":
        if (value === "complete") {
          Object.assign(preferencesFilter, {
            userStatus: "active",
            availability: { $exists: true, $ne: [] },
            "assignment.level": { $exists: true, $ne: [] },
            "assignment.subjectToStudy": { $exists: true, $nin: ["", null] },
            gender: { $exists: true, $nin: ["", null] },
            $or: [
              {
                "contactDetails.phoneNumber": {
                  $exists: true,
                  $nin: ["", null],
                },
              },
              { "contactDetails.email": { $exists: true, $nin: ["", null] } },
            ],
          });
        } else if (value === "incomplete") {
          Object.assign(preferencesFilter, {
            $or: [
              { availability: { $exists: true, $eq: [] } },
              { "assignment.level": { $exists: true, $eq: [] } },
              {
                $or: [
                  { "assignment.subjectToStudy": { $exists: false } },
                  { "assignment.subjectToStudy": { $exists: true, $eq: "" } },
                ],
              },
              {
                $or: [
                  { gender: { $exists: true, $eq: "" } },
                  { gender: { $exists: false } },
                ],
              },
              {
                $and: [
                  { "contactDetails.phoneNumber": { $exists: true, $eq: "" } },
                  { "contactDetails.email": { $exists: true, $eq: "" } },
                ],
              },
            ],
          });
        }
        break;
      case "parentUserId":
        studentFilter.parentUserId = { $eq: value };
        break;
      case "parent-email":
        studentFilter["parent-email"] = value;
        break;
      case "unmatched":
        studentFilter.matchedTutors = { $eq : []};
        break;
      case "matched":
        studentFilter.matchedTutors = { $ne : []};
        break;
      case "thirtyDaysAgo":
        studentFilter.createdAt  = { $gte: new Date().setDate(new Date().getDate() - 30) }
        break;
      default:
        break;
    }
  }

  const finalFilter = {
    student: studentFilter,
    preference: preferencesFilter,
    userRole: userRoleConstants.ROLE_STUDENT,
  };

  return { status: true, data: finalFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);

    //check if sortBy is empty or not
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key === "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      } else if (key == "firstName") {
        key = "contactDetails.firstName";
      } else if (key == "email") {
        key = "contactDetails.email";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["createdAt", -1]];
  }

  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}
function buildPageSize(pageSize) {
  if (pageSize != undefined || pageSize != null) {
    pageSize = parseInt(pageSize);
  } else {
    pageSize = 10;
  }
  return pageSize;
}

function buildPage(page) {
  if (page != undefined || page != null) {
    page = parseInt(page);
    //check if nextPage is = 0 , if yes set it to 1
    if (page == 0) {
      page = 1;
    }
  } else {
    page = 1;
  }

  return page;
}
//#endregion

//Get number of students per sector
exports.getNumberOfStudentsPerSector = async (sectorId) => {
  try {
    //convert sectorId from ObjectId to string
    sectorId = sectorId.toString();
    //count total number of students  per sector
    const numberOfStudents = await StudentsPreferences.countDocuments({
      "assignment.sectors.sectorId": sectorId,
    }).exec();
    return {
      status: true,
      message: "Number of students per sector",
      data: numberOfStudents.toString(),
    };
  } catch (error) {
    console.log("studentHelper.getNumberOfStudentsPerSector", error);
    return {
      status: false,
      message: `Error in getting number of students per sector ${error.message}`,
      data: 0,
    };
  }
};
//Get number of students per department
exports.getNumberOfStudentsPerDepartment = async (departmentId) => {
  try {
    //convert departmentId from ObjectId to string
    departmentId = departmentId.toString();
    //count total number of students  per department
    const numberOfStudents = await StudentsPreferences.countDocuments({
      "assignment.department.departmentId": department,
    }).exec();
    return {
      status: true,
      message: "Number of students per department",
      data: numberOfStudents.toString(),
    };
  } catch (error) {
    console.log("studentHelper.getNumberOfStudentsPerDepartment", error);
    return {
      status: false,
      message: `Error in getting number of students per department ${error.message}`,
      data: 0,
    };
  }
};

//get student full name
exports.getStudentFullName = async (studentId) => {
  const studentDocument = await userHelper.getUserDetailsByUserId(studentId);
  if (!studentDocument) {
    return { status: false, message: "Student not found", data: "" };
  }
  const firstName = studentDocument.contactDetails.firstName;
  const lastName = studentDocument.contactDetails.lastName;
  let fullName = firstName + " " + lastName;
  return { status: true, message: "Student full name", data: fullName };
};

exports.getListOfStudentIdsBySectorId = async (sectorId) => {
  try {
    //convert sectorId from ObjectId to string
    sectorId = sectorId.toString();
    //get list of student ids by sector id
    const listOfStudentIds = await StudentsPreferences.find({
      "assignment.sectors.sectorId": sectorId,
      userStatus: userRoleStatusConstants.userAdminStatus.ACTIVE,
      createdAt: { $exists: true },
    })
      .distinct("userId")
      .exec();
    return {
      status: true,
      message: "List of student ids by sector id",
      data: listOfStudentIds,
    };
  } catch (error) {
    console.log("studentHelper.getListOfStudentIdsBySectorId", error);
    return {
      status: false,
      message: `Error in getting list of student ids by sector id ${error.message}`,
      data: [],
    };
  }
};

exports.searchChildrenHasParent = async (parentId) => {
  try {
    const student = await User.find({ userId: parentId }).exec();
    if (student.length > 0) {
      return { status: true, message: "Child has parent", data: true };
    } else {
      return {
        status: true,
        message: "Child does not have child",
        data: false,
      };
    }
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in searching parent has child",
      data: null,
    };
  }
};
exports.addPriorityToProgram = (programId) => {
  const programPriority = {
    ///HomeClasses - remote, Devoirs Faits - Face-to-face
    "az-rem-4ca0f8d": {
      programName: "HomeClasse",
      programId: "az-rem-4ca0f8d",
      priority: 3,
    },
    "az-fac-5f87232": {
      programName: "Tutorat solidaire",
      programId: "az-fac-5f87232",
      priority: 4,
    },
    "az-zup-6g98343": {
      programName: "ZUPdeFOOT",
      programId: "az-zup-6g98343",
      priority: 1,
    },
    "az-per-2ca0z9s": {
      programName: "ClassHome",
      programId: "az-per-2ca0z9s",
      priority: 2,
    },
  };
  return programPriority[programId];
};
exports.getStudentStatistics = async (payload) => {
  try {
    const { userId, userRole } = payload;
    
    // Préparer le filtre de base (logique similaire à getISStudentDashboard)
    const baseFilter = {};
    
    // Ajouter des filtres basés sur le rôle utilisateur si nécessaire
    if ([userRoleConstants.COORDINATOR, userRoleConstants.VSC].includes(userRole)) {
      const userAdminDetails = await UserAdministrationModel.findOne({ userId });
      const pref = userAdminDetails?.administrationPreferences;
      
      if (pref && pref.program?.length) {
        // Appliquer les filtres de programme
        baseFilter["program.programId"] = {
          $in: pref.program[0].programId === "az-per-2ca0z9s" || pref.program[0].programId === "az-rem-4ca0f8d"
            ? ["az-per-2ca0z9s", "az-rem-4ca0f8d"]
            : pref.program.map((p) => p.programId)
        };
        
        const program = pref.program[0];
        
        // Filtres spécifiques selon le type de programme
        if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
          const establishmentIds = pref.establishment.map(est => est.establishmentId);
          
          if (!establishmentIds?.length) {
            baseFilter["_id"] = null;
          } else {
            baseFilter["assignment.establishments.establishmentId"] = { $in: establishmentIds };
          }
        } else if (
          program.programId === HOME_CLASSES.programId ||
          program.programId === CLASSSES_HOME.programId ||
          program.programId === ZUPDEFOOT.programId
        ) {
          const departmentIds = pref.department.map(dep => dep.departmentId);
          
          if (!departmentIds?.length) {
            baseFilter["_id"] = null;
          } else {
            baseFilter["assignment.department.departmentId"] = { $in: departmentIds };
            baseFilter["program.programId"] = program.programId;
          }
        }
      }
    }

    // Date pour filtrer les étudiants créés dans les 30 derniers jours
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    // Requêtes individuelles pour obtenir des statistiques précises
    const [
      // Nombre total d'étudiants
      totalCount,
      
      // Nombre d'étudiants actifs
      activeCount,
      
      // Nombre d'étudiants nouveaux (situation: "new" et status: "active")
      newSituationCount,
      
      // Nombre d'étudiants non appariés
      unmatchedCount,
      
      // Nombre d'étudiants appariés
      matchedCount,
      
      // Nombre d'étudiants créés récemment
      recentlyCreatedCount,

      // Nombre d'étudiants avec dossier incomplet
      incompleteCount
    ] = await Promise.all([
      // 1. Total
      StudentPreferencesModel.countDocuments(baseFilter),
      
      // 2. Actifs
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
         userStatus: "active"
      }),
      
      // 3. Nouveaux (situation: "new", status: "active")
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
        situation: "new",
      }),
      
      // 4. Non appariés
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
        $or: [
          { matchedTutors: { $exists: false } },
          { matchedTutors: { $size: 0 } }
        ]
      }),
      
      // 5. Appariés
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
        matchedTutors: { $exists: true },
        $expr: { $gt: [{ $size: "$matchedTutors" }, 0] }
      }),
      
      // 6. Créés récemment
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
        createdAt: { $gte: thirtyDaysAgo }
      }),
      
      // 7. Dossier incomplet
      StudentPreferencesModel.countDocuments({
        ...baseFilter,
        $or: [
          { availability: { $exists: true, $eq: [] } },
          { "assignment.level": { $exists: true, $eq: [] } },
          {
            $or: [
              { "assignment.subjectToStudy": { $exists: false } },
              { "assignment.subjectToStudy": { $exists: true, $eq: "" } },
            ],
          },
          {
            $or: [
              { gender: { $exists: true, $eq: "" } },
              { gender: { $exists: false } },
            ],
          },
          {
            $and: [
              { "contactDetails.phoneNumber": { $exists: true, $eq: "" } },
              { "contactDetails.email": { $exists: true, $eq: "" } },
            ],
          },
        ]
      })
    ]);
    
    return {
      status: true,
      message: "Statistiques des students",
      data: {
        totalNumberOfstudents: totalCount,
        numberOfActifsStudents: activeCount,
        numberOfNewSituationStudents: newSituationCount,
        numberOfUnMatchedStudents: unmatchedCount,
        numberOfMatchedStudents: matchedCount,
        numberOfStudentsCreated: recentlyCreatedCount,
        numberOfStudentsIncompletFolder: incompleteCount
      },
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Erreur lors de l'obtention des statistiques des students",
      data: null,
    };
  }
};
