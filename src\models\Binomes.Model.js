const mongoose = require("mongoose");
const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const binomesSchema = new mongoose.Schema(
  {
    idBinome: {
      type: String,
    //   required: true,
    //   index: {
    //     unique: true,
    //     sparse: false,
    //   },
    },
    binomeConcat: {
      type: String,
    //   required: true,
    },
    tutorId: {
      type: String,
    //   required: true,
    },
    tutor: {
      type: String,
    //   required: true,
    },
    tutorStatus: {
      type: String,
    },
    tutorSituation: {
      type: String,
    },
    studentId: {
      type: String,
    //   required: true,
    },
    student: {
      type: String,
    //   required: true,
    },
    studentStatus: {
      type: String,
    },
    studentSituation: {
      type: String,
    },
    program: {
      type: String,
    //   required: true,
    },
    createdBy: {
      type: String,
    },
    createdAt: {
      type: Date,
    },
    CreatedAtYear: {
      type: Number,
    },
    CreatedAtMonth: {
      type: Number,
    },
    CreatedAtWeek: {
      type: Number,
    },
    CreatedAtDay: {
      type: Number,
    },
    CreatedAtHour: {
      type: Number,
    },
    // Additional fields if needed
    lastUpdatedAt: {
      type: Date,
      default: dateTimeHelper.getCurrentDateTimeInParisZone(),
    },
    lastUpdatedBy: {
      userId: String,
      fullName: String,
      email: String,
      userRole: String,
    },
    // Metadata fields
    metadata: {
      type: Object,
    },
  },
  {
    timestamps: { createdAt: "createdAt", updatedAt: "lastUpdatedAt" },
  },
  {
    versionKey: false,
  }
);

// Add indexes for frequently queried fields
// binomesSchema.index({ tutorId: 1 });
// binomesSchema.index({ studentId: 1 });
// binomesSchema.index({ program: 1 });
// binomesSchema.index({ tutorSituation: 1 });
// binomesSchema.index({ studentSituation: 1 });
// binomesSchema.index({ CreatedAtYear: 1, CreatedAtMonth: 1 });

module.exports = mongoose.model("binomes", binomesSchema);