const crReportsModel = require("../../models/Cr.Report.Model.js");
const mongoose = require("mongoose");
const sessionHelper = require("../sessions/sessions.helper.js");
const { report } = require("../../routes/bigbluebutton.routes.js");
const { parse } = require("dotenv");
const moment = require("moment");
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");
const UserAdminsModel = require("../../models/User.Administration.Model.js");
const studentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const {
  DEVOIRS_FAITS,
  HOME_CLASSES,
  ZUPDEFOOT,
  CLASSSES_HOME,
} = require("../../utils/constants/program.constants.js");
const { student } = require("../../services/logger/loggerType.js");

const SessionsModel = require("../../models/Sessions.Model.js");
const onligneClass = [
  HOME_CLASSES.programId,
  CLASSSES_HOME.programId,
  ZUPDEFOOT.programId,
];
//This is a Helper for Reports CR and SC
//create new SC Report
exports.createScReport = async (userId, scReportsObjet) => {
  try {
    const newScReport = new crReportsModel(scReportsObjet);
    const newReport = await newScReport.save();
    if (newReport) {
      const sessionId = newReport.sessionId;

      await sessionHelper.updateSessionReport(userId, sessionId, newReport);
      return {
        status: true,
        message: "SC Report created successfully",
        data: newReport,
      };
    } else {
      return { status: false, message: "SC Report not created" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get SC and CR Reports by _id
exports.getScCrReportById = async (_id) => {
  try {
    //convert the _id to ObjectId
    _id = mongoose.Types.ObjectId(_id);

    const report = await crReportsModel.findById(_id);

    if (report) {
      const sessionId = report.sessionId;

      const sessionDocument = await sessionHelper.getSessionById(sessionId);
      let newItem = {
        program: sessionDocument.data.program,
        ...report.toObject(),
      };
      return { status: true, message: "Report found", data: newItem };
    } else {
      return { status: false, message: "Report not found" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
exports.getScCrReportBySessionId = async (_id) => {
  try {
    const report = await crReportsModel.findOne({ sessionId: _id });

    if (report) {
      const sessionId = report.sessionId;

      const sessionDocument = await sessionHelper.getSessionById(sessionId);
      let newItem = {
        program: sessionDocument.data?.program,
        ...report.toObject(),
      };
      return { status: true, message: "Report found", data: newItem };
    } else {
      return { status: false, message: "Report not found" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//delete SC and CR Reports by _id
exports.deleteScCrReportById = async (_id) => {
  try {
    //convert the _id to ObjectId
    _id = mongoose.Types.ObjectId(_id);

    const deletedReport = await crReportsModel.findByIdAndDelete(_id);

    if (deletedReport) {
      await sessionHelper.deleteReportFromSession(deletedReport.sessionId);
      return {
        status: true,
        message: "Report deleted successfully",
        data: deletedReport,
      };
    } else {
      return { status: false, message: "Report not deleted" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//delete SC and CR Reports  from sessionId
exports.deleteScCrReportBySessionId = async (sessionId) => {
  try {
    const deletedReport = await crReportsModel.findOneAndDelete({
      sessionId: sessionId,
    });

    if (deletedReport) {
      await sessionHelper.deleteReportFromSession(deletedReport.sessionId);
      return {
        status: true,
        message: "Report deleted successfully",
        data: deletedReport,
      };
    } else {
      return { status: false, message: "Report not deleted" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//update SC and CR Reports
exports.updateScCrReport = async (userId, reportObject) => {
  try {
    const session = await SessionsModel.findOne({
      sessionId: reportObject.reportId,
    });
    //reportObject.vsc = session?.vsc;
    const updatedReport = await crReportsModel.findOneAndUpdate(
      { sessionId: reportObject.reportId },
      reportObject,
      { new: true }
    );
    if (updatedReport) {
      const sessionId = updatedReport.sessionId;

      await sessionHelper.updateSessionReport(userId, sessionId, updatedReport);

      return {
        status: true,
        message: "Report updated successfully",
        data: updatedReport,
      };
    } else {
      return { status: false, message: "Report not updated" };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

/**
 * Update student appreciation in a crReport.
 * @async
 * @param {string} userId - The ID of the user updating the report.
 * @param {appreciation: string, rating: number} reportObject - The updated report object.
 * @returns {Promise<object>} The result of the operation.
 */
exports.updateStudentAppreciation = async (userId, reportObject) => {
  try {
    const { _id, appreciation, note, rating } = reportObject;
    // check if appreciation or _id is empty
    if (!_id || !appreciation)
      throw new Error("Appreciation && Report Id are required");

    // find the report by _id
    const updatedReport = await crReportsModel.findById(_id);
    // check if the report is not found
    if (!updatedReport) throw new Error("Report not found");
    const student = updatedReport.students.find(
      (student) => student.userId === userId
    );
    // check if the student is not found
    if (!student) throw new Error("Student not found");
    student.appreciation = appreciation;
    if (note) student.note = note;
    if (rating >= 0) student.rating = rating;
    await updatedReport.save();

    return {
      status: true,
      message: "Report updated successfully",
      data: updatedReport,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};
/**
 * Update tutor appreciation in a crReport.
 * @async
 * @param {string} userId - The ID of the user updating the report.
 * @param {appreciation: string, rating: number} reportObject - The updated report object.
 * @returns {Promise<object>} The result of the operation.
 */
exports.updateTutorAppreciation = async (userId, reportObject) => {
  try {
    const { _id, appreciation, rating } = reportObject;
    // check if appreciation or _id is empty
    if (!_id || !appreciation)
      throw new Error("Appreciation && Report Id are required");

    // find the report by _id
    const updatedReport = await crReportsModel.findById(_id);
    // check if the report is not found
    if (!updatedReport) throw new Error("Report not found");
    const tutor = updatedReport.tutors.find((tutor) => tutor.userId === userId);
    // check if the student is not found
    if (!tutor) throw new Error("Student not found");
    tutor.appreciation = appreciation;
    if (rating >= 0) tutor.rating = rating;
    await updatedReport.save();

    return {
      status: true,
      message: "Report updated successfully",
      data: updatedReport,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//report dashboard based on the typeOfReport
exports.reportDashboard = async (data) => {
  try {
    const { typeOfReport, page, pageSize, filter, sortByObject } = data;
    let applyFilterData = buildFilter(filter);

    let applyFilter = applyFilterData.data;
    if (!applyFilterData.status) {
      return { status: false, message: applyFilterData.message };
    }

    let sortBy = buildSortBy(sortByObject);

    let skip = (page - 1) * pageSize;

    let reports = await crReportsModel
      .find({ typeOfReport: typeOfReport, ...applyFilter })
      .populate({
        path: "sessionId",
        match: { _id: { $ne: null } },
        select: "sessionType",
      })
      .sort(sortBy)
      .skip(skip)
      .limit(pageSize)
      .exec();

    const totalOfReports = crReportsModel.countDocuments({
      typeOfReport: typeOfReport,
      ...applyFilter,
    });
    if (!totalOfReports) return { status: false, message: "No reports found" };

    return {
      status: true,
      message: `Reports Dashboard `,
      data: reports,
      total: totalOfReports,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//report dashboard based on the typeOfReport
exports.crReportDashboard = async (data) => {
  try {
    const { userId, userRole, page, pageSize, filter, sortByObject } = data;

    let applyFilterData = buildCrFilter(filter);
    if (!applyFilterData.status) {
      return { status: false, message: applyFilterData.message };
    }

    const applyFilter = {
      ...applyFilterData.data,
    };

    const sortBy = buildSortBy(sortByObject);

    const skip = (page - 1) * pageSize;
    let query = { ...applyFilter };
    if (userRole === userRolesConstants.VSC) {
      query = { "vsc.userId": userId };
    }
    // Check if the user is an admin and his role in [coordinator or vsc]
    if (userRolesConstants.COORDINATOR === userRole) {
      // get the admin preferences model of the user
      const userAdminDetails = await UserAdminsModel.findOne({ userId });
      // get the program preferences of the user
      const pref = userAdminDetails?.administrationPreferences;
      // check if the user has program preferences
      let listStudents = [];
      if (pref && pref.program?.length) {
        const program = pref.program[0];
        if ([DEVOIRS_FAITS.programId].includes(program.programId)) {
          const establishmentIds = pref.establishment.map(
            (est) => est.establishmentId
          );
          listStudents = await studentPreferencesModel.find({
            "program.programId": program.programId,
            "assignment.establishments.establishmentId": {
              $in: establishmentIds,
            },
          });
        } else if (onligneClass.includes(program.programId)) {
          // get the departementIds from the admin preferences model
          const departmentIds = pref.department.map((dep) => dep.departmentId);
          // Trouver les coordinateurs avec les programmes et départements correspondants
          listStudents = await studentPreferencesModel.find({
            "program.programId": program.programId,
            "assignment.department.departmentId": { $in: departmentIds },
          });
        }
        const studentIds = listStudents.map((student) => student.userId);
        if( ! applyFilterData?.data["students.userId"]) {
          query["students.userId"] = { $in: studentIds };
        }
      }
    }
    const listOfReports = await crReportsModel.aggregate([
      {
        $lookup: {
          from: "sessions", // Le nom de la collection "sessions"
          localField: "sessionId", // Le champ dans "crReports" qui référence la session
          foreignField: "sessionId", // Le champ dans "sessions" qui correspond à "sessionId"
          as: "sessionData" // Le nom du champ où les données de la session seront stockées
        }
      },
      {
        $match: {
          "sessionData.0": { $exists: true },
          ...query // Filtre pour ne garder que les comptes-rendus avec une session correspondante
        }
      },
      {
        $facet: {
          // Sous-pipeline pour les documents paginés
          paginatedResults: [
            { $sort: sortBy }, // Appliquer le tri
            { $skip: skip }, // Appliquer le saut
            { $limit: pageSize }, // Appliquer la limite
          ],
          // Sous-pipeline pour le nombre total de documents
          totalCount: [
            { $count: "count" } // Compter le nombre total de documents
          ]
        }
      }
    ]);
    return {
      status: true,
      message: "Reports Dashboard",
      data: listOfReports[0]?.paginatedResults,
      total: listOfReports[0]?.totalCount[0]?.count || 0,
      page: page,
      pageSize: pageSize,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

function buildFilter(filter) {
  let applyFilter = {};

  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (key == "tutorName") {
      applyFilter["tutors"] = {
        $elemMatch: {
          fullName: {
            $regex: value,
            $options: "i",
          },
        },
      };
      delete applyFilter.fullName;
    } else if (key == "studentName") {
      applyFilter["students"] = {
        $elemMatch: {
          fullName: {
            $regex: value,
            $options: "i",
          },
        },
      };
      delete applyFilter.fullName;
    } else if (key == "studentsAttendance") {
      applyFilter["students"] = {
        $elemMatch: {
          absence: value,
        },
      };
      delete applyFilter.studentsAttendance;
    } else if (key == "byDate") {
      const byDate = value;

      applyFilter["session_date"] = {
        $gte: byDate.startDate,
        $lte: byDate.endDate,
      };
    } else if (key == "coordinatorName") {
      applyFilter["coordinator.coordinatorFullName"] = value;
      delete applyFilter.coordinatorName;
    } else if (key == "vscName") {
      applyFilter["vsc.vscFullName"] = value;
      delete applyFilter.coordinatorName;
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (typeof value == "string") {
      let regex = new RegExp(value, "i");
      applyFilter[key] = regex;
    }
  }
  return { status: true, data: applyFilter };
}

function buildCrFilter(filter) {
  let applyFilter = {};

  try {
    applyFilter = JSON.parse(filter || "{}");
  } catch (error) {
    return { status: false, message: "Filter is not a valid JSON" };
  }

  const filterMappings = {
    tutorFullName: { target: "tutors", field: "fullName" },
    vscFullName: { target: "vsc", field: "fullName" },
    studentFullName: { target: "students", field: "fullName" },
    studentsAttendance: { target: "students", field: "absence" },
    tutorsAttendance: { target: "tutors", field: "absence" },
    byDate: { target: "session_date", field: null },
    studentUserId: { target: "studentUserId", field: "students.userId" },
  };

  for (const [key, value] of Object.entries(applyFilter)) {
    const mapping = filterMappings[key];

    if (mapping) {
      const { target, field } = mapping;

      if (applyFilter[target] === field) {
        applyFilter[target] = {
          $elemMatch: { [field]: { $regex: value, $options: "i" } },
        };
        delete applyFilter[key];
      } else if (
        ["tutorFullName", "studentFullName", "vscFullName"].includes(key)
      ) {
        applyFilter[target] = {
          $elemMatch: { [field]: { $regex: value, $options: "i" } },
        };
        delete applyFilter[key];
      } else if (target === "session_date") {
        applyFilter[target] = {
          $gte: new Date (value?.startDate),
          $lte: new Date (value?.endDate),
        };
        delete applyFilter[key];
      } else if (target === "students") {
        applyFilter[target] = {
          $elemMatch: { absence: value === "true" ? false : true },
        };

        delete applyFilter[key];
      } else if (target === "studentUserId") {
        applyFilter[field] =  value ;
        delete applyFilter[target];
      } else if (target === "tutors") {
        applyFilter[target] = {
          $elemMatch: { absence: value === "true" ? false : true },
        };

        delete applyFilter[key];
      }
    }
  }
  return { status: true, data: applyFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = {"session_date": -1};
  }
  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}

exports.getStudentAttendance = async (req, res) => {
  const { userId } = req.params;

  if (!userId || userId === "undefined") {
    return res.status(400).send("userId query parameter is required");
  }

  try {
    const matchCriteria = {
      "students.userId": userId,
      "students.absence": false,
    };

    const hours = await CrScReport.countDocuments(matchCriteria);

    await StudentPreferencesModel.findByIdAndUpdate(
      userId,
      { totalSessionsHours: hours },
      { new: true }
    ).exec();

    res.json({ totalHours: hours });
  } catch (err) {
    console.error("Error retrieving attendance:", err);
    res.status(500).send("Internal server error");
  }
};

exports.getTutorAttendance = async (req, res) => {
  const { userId } = req.params;

  if (!userId || userId === "undefined") {
    return res.status(400).send("userId query parameter is required");
  }

  try {
    const matchCriteria = {
      "tutors.userId": userId,
      "tutors.absence": false,
    };

    const hours = await CrScReport.countDocuments(matchCriteria);

    await TutorPreferencesModel.findByIdAndUpdate(
      userId,
      { totalSessionsHours: hours },
      { new: true }
    ).exec();

    res.json({ totalHours: hours });
  } catch (err) {
    console.error("Error retrieving attendance:", err);
    res.status(500).send("Internal server error");
  }
};
