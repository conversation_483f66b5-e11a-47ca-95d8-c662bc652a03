//setup express
const express = require("express");
const router = express.Router();
const User = require("../models/User.Models.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const firebaseHelper = require("../controllers/firebase/firebase.helper.js");

//import api response helper from controllers
const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

//import formidable
const formidable = require("formidable");

//import userHelper
const userHelper = require("../controllers/user/User.Helper.js");

//import userRole  constants

//import  logger
const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("parent.routes.js");

//import student helper
const studentHelper = require("../controllers/students/student.helpers.js");

//import parent helper
const parentHelper = require("../controllers/parent/parent.helper.js");
//import parentPreferences model
const ParentPreferences = require("../models/Parent.Preferences.Model.js");

//import sendGridHelper
const sendGridHelper = require("../middleware/mailServiceSendGrid/EmailHelper.js");

//import app constants
const appConstants = require("../utils/constants/app.constants.js");

const sendGrid = require("@sendgrid/mail");
const sendGridConstants = require("../utils/constants/sendgrid.constants.js");
//import twilio init
const twilioInit = require("../middleware/smsTwilio/init.twilio.js");

const sessionsParentHelper = require("../controllers/sessions/sessions.parent.helper.js");

const dotenv = require("dotenv").config();

const {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
} = require("../middleware/apiAuth/verifyBearer.js");

const {
  ROLE_PARENT,
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
} = require("../utils/constants/userRolesConstants.js");
const userRolesConstants = require("../utils/constants/userRolesConstants.js");
const ParentPreferencesModel = require("../models/Parent.Preferences.Model.js");

//Signup Parent
/**
 * @swagger
 * /api/v1/user/parent:
 *   post:
 *     summary: Signup Parent
 *     description: Creates a new parent user account
 *     tags: [Parent]
 *     parameters:
 *       - name: firebaseUserId
 *         description: The Firebase user ID of the parent
 *         in: formData
 *         required: true
 *         type: string
 *       - name: provider
 *         description: The provider of the authentication service (e.g., Google, Facebook)
 *         in: formData
 *         required: true
 *         type: string
 *       - name: firebaseIdentifier
 *         description: The Firebase identifier of the parent
 *         in: formData
 *         required: true
 *         type: string
 *       - name: firstName
 *         description: The first name of the parent
 *         in: formData
 *         required: true
 *         type: string
 *       - name: lastName
 *         description: The last name of the parent
 *         in: formData
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Parent created successfully
 *       400:
 *         description: Bad request
 */
router.post(
  "/user/parent",
  [verifyApiKey, checkIfAuthenticated],
  async (req, res) => {
    try {
      const {
        firebaseUserId,
        provider,
        firebaseIdentifier,
        firstName,
        lastName,
        email,
        phoneNumber,
        gender,
        howDidYouHearAboutUs,
        department,
        socioProfessionalCategory,
        club,
        program,
        iAcceptTermsAndPolicyOfConfidentiality,
        iAgreeToReceiveInformationAndUpdates,
        iAcceptToBeContactedForParticipatingInAStudy,
        address,
        mentor,
      } = req.body;

      // userId is generated by  uuidv4() and is unique for each user
      const userId = userHelper.generateNewUserId();
      //check if firebaseIdentifier is provided in the request
      if (
        firebaseIdentifier === undefined ||
        firebaseIdentifier === null ||
        firebaseIdentifier === ""
      ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Firebase Identifier is required`,
          `Please provide firebase identifier, it is required to create a parent account`
        );
        res.status(200).json(response);
      }

      //check if firebaseUserId is provided in the request
      if (
        firebaseUserId === undefined ||
        firebaseUserId === null ||
        firebaseUserId === ""
      ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Firebase User Id is required`,
          `Please send firebase user id, it is required to create a parent account`
        );

        res.status(200).json(response);
      }

      //check if provider is provided in the request
      if (provider === undefined || provider === null || provider === "") {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Provider is required`,
          `Please send provider, it is required to create a parent account`
        );

        res.status(200).json(response);
      }

      //check if user already exists based on firebaseIdentifier
      let userDocument = await userHelper.getUserDetailsFromFirebaseIdentifier(
        firebaseIdentifier
      );

      //if user not found, create new user document in database otherwise return user exists
      if (userDocument) {
        let response = apiResponse.responseWithStatusCode(
          false,
          `User already exists with this identifier ${firebaseIdentifier}`,
          { userId: userDocument.userId },
          apiResponse.apiConstants.USER_ALREADY_EXISTS
        );
        res.status(200).json(response);
      } else {
        const contactDetails = await userHelper.getContactDetailsObject(
          firebaseIdentifier,
          null
        );
        contactDetails.firstName = firstName;
        contactDetails.lastName = lastName;
        contactDetails.email = email;
        contactDetails.phoneNumber = phoneNumber;

        //create new user document
        let user = new User({
          userId: userId,
          firebaseIds: {
            firebaseUserId: firebaseUserId,
            provider: provider,
            identifier: firebaseIdentifier,
          },
          userRole: userRolesConstants.ROLE_PARENT,
          contactDetails: contactDetails,
          gender: gender || null,
          hasChangedPassword: true,
          iAcceptTermsAndPolicyOfConfidentiality:
            iAcceptTermsAndPolicyOfConfidentiality,
          iAcceptToBeContactedForParticipatingInAStudy:
            iAcceptToBeContactedForParticipatingInAStudy,
          iAgreeToReceiveInformationAndUpdates:
            iAgreeToReceiveInformationAndUpdates,
          address: {
            addressLine1: address.addressLine1,
            zipCode: address.zipCode,
          },
        });

        user.save(async (err, user) => {
          if (err) {
            let response = apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_FAILED,
              "Error while saving user document",
              `There was an error while saving user document. Error: ${err}`
            );
            res.status(400).json(response);
          } else {
            await parentHelper.createParentPreferences(
              user.userId,
              howDidYouHearAboutUs,
              department,
              socioProfessionalCategory,
              club,
              program,
              mentor
            );
            const token = generateAccessToken(user.userId);
            const refreshToken = generateRefreshToken(user.userId);

            let response = apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_SUCCESS,
              "User created successfully",
              { ...user._doc, token },
              apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
            );
            res.cookie("refreshToken", refreshToken, {
              httpOnly: true,
              secure: process.env.NODE_ENV === "production",
              path: "/",
            });
            res.status(200).json(response);
          }
        });
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while creating a parent account`,
        500
      );
      res.status(500).json(response);
    }
  }
);

//update parent profile firstName and lastName
/**
 * @swagger
 * /api/v1/user/parent/last-step:
 *   put:
 *     summary: Update Parent Profile (Last Step)
 *     description: Updates the profile of a parent user with additional details in the last step of the signup process.
 *     tags: [Parent]
 *     parameters:
 *       - name: userId
 *         in: formData
 *         description: The unique ID of the parent user.
 *         required: true
 *         type: string
 *       - name: firstName
 *         in: formData
 *         description: The first name of the parent user.
 *         required: false
 *         type: string
 *       - name: email
 *         in: formData
 *         description: The email address of the parent user.
 *         required: false
 *         type: string
 *       - name: lastName
 *         in: formData
 *         description: The last name of the parent user.
 *         required: false
 *         type: string
 *       - name: gender
 *         in: formData
 *         description: The gender of the parent user.
 *         required: false
 *         type: string
 *       - name: howDidYouHearAboutUs
 *         in: formData
 *         description: Information on how the parent user heard about the service.
 *         required: false
 *         type: string
 *       - name: iAcceptTermsAndPolicyOfConfidentiality
 *         in: formData
 *         description: Indicates whether the parent user accepts the terms and policy of confidentiality.
 *         required: false
 *         type: boolean
 *       - name: iAgreeToReceiveInformationAndUpdates
 *         in: formData
 *         description: Indicates whether the parent user agrees to receive information and updates.
 *         required: false
 *         type: boolean
 *       - name: iAcceptToBeContactedForParticipatingInAStudy
 *         in: formData
 *         description: Indicates whether the parent user accepts to be contacted for participating in a study.
 *         required: false
 *         type: boolean
 *       - name: firebaseUserId
 *         in: formData
 *         description: The Firebase user ID of the parent user.
 *         required: false
 *         type: string
 *       - name: firebaseIdentifier
 *         in: formData
 *         description: The Firebase identifier of the parent user.
 *         required: false
 *         type: string
 *       - name: provider
 *         in: formData
 *         description: The provider of the authentication service (e.g., Google, Facebook).
 *         required: false
 *         type: string
 *     responses:
 *       200:
 *         description: Parent profile updated successfully.
 *       400:
 *         description: Bad request. The provided userId is required.
 *       404:
 *         description: User not found. No user exists with the provided userId.
 *       500:
 *         description: Error occurred while updating parent profile.
 */
router.put(
  "/user/parent/last-step",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      const {
        userId,
        firstName,
        email,
        lastName,
        gender,
        howDidYouHearAboutUs,
        department,
        socioProfessionalCategory,
        club,
        program,
        iAcceptTermsAndPolicyOfConfidentiality,
        iAgreeToReceiveInformationAndUpdates,
        iAcceptToBeContactedForParticipatingInAStudy,
        firebaseUserId,
        firebaseIdentifier,
        provider,
        address,
      } = req.body;
      //check if userId is provided in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to update parent profile`
        );
        return res.status(200).json(response);
      } else {
        //check if user already exists based on userId
        let userDocument = await userHelper.getUserDetailsByUserId(userId);

        //if user not found, return user not found
        if (!userDocument) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "User not found",
            `User not found with userId: ${userId}`,
            apiResponse.apiConstants.USER_NOT_FOUND
          );
          return res.status(200).json(response);
        }
        if (
          howDidYouHearAboutUs ||
          department ||
          socioProfessionalCategory ||
          club ||
          program
        ) {
          let parentPreferences = await ParentPreferences.findOne({
            userId: userId,
          });
          if (parentPreferences) {
            parentPreferences.howDidYouHearAboutUs = howDidYouHearAboutUs;
            parentPreferences.department = department;
            parentPreferences.socioProfessionalCategory =
              socioProfessionalCategory;
            parentPreferences.club = club;
            await parentPreferences.save();
          } else {
            let parentPreferences = new ParentPreferences({
              userId: userId,
              howDidYouHearAboutUs: howDidYouHearAboutUs,
              department: department,
              socioProfessionalCategory: socioProfessionalCategory,
              club: club,
              currentVideo: 1,
              program: program,
            });
            await parentPreferences.save();
          }
        }
        let firebaseIds = userDocument.firebaseIds;
        if (firebaseUserId && provider && firebaseIdentifier) {
          let newFirebaseIds = {
            identifier: firebaseIdentifier,
            firebaseUserId: firebaseUserId,
            provider: provider,
          };
          firebaseIds.push(newFirebaseIds);

          let newContactDetails = await userHelper.getContactDetailsObject(
            firebaseIdentifier,
            userDocument.contactDetails
          );
          userDocument.contactDetails.email = newContactDetails.email;
          userDocument.contactDetails.phoneNumber =
            newContactDetails.phoneNumber;
        }
        //update user document
        userDocument.firebaseIds = firebaseIds;
        userDocument.contactDetails.firstName = firstName
          ? firstName
          : userDocument.contactDetails.firstName;
        userDocument.contactDetails.lastName = lastName
          ? lastName
          : userDocument.contactDetails.lastName;
        userDocument.gender = gender || userDocument.gender;
        userDocument.iAcceptTermsAndPolicyOfConfidentiality =
          iAcceptTermsAndPolicyOfConfidentiality
            ? iAcceptTermsAndPolicyOfConfidentiality
            : userDocument.iAcceptTermsAndPolicyOfConfidentiality;
        userDocument.iAcceptToBeContactedForParticipatingInAStudy =
          iAcceptToBeContactedForParticipatingInAStudy ||
          userDocument.iAcceptToBeContactedForParticipatingInAStudy;
        userDocument.iAgreeToReceiveInformationAndUpdates =
          iAgreeToReceiveInformationAndUpdates
            ? iAgreeToReceiveInformationAndUpdates
            : userDocument.iAgreeToReceiveInformationAndUpdates;
        userDocument.address = { addressLine1: "", zipCode: "" };
        userDocument.address.addressLine1 = address.addressLine1;
        userDocument.address.zipCode = address.zipCode;
        let userUpdated = await userDocument.save();

        if (userUpdated) {
          //send welcoming email to parent
          const subject = `Bienvenue dans votre espace Homeclasse par ZUPdeCO !`;
          const setupInvitation = {
            to: [email ? email : userDocument.contactDetails.email],
            dynamic_template_data: {
              name: subject,
              firstName: firstName,
            },
            from: sendGridConstants.ZupDecoEmails.EMAIL_ZUPDECO_ORG,
            templateId:
              sendGridConstants.EmailTemplates.TEMPLATE_PARENT_WELCOMING,
          };
          await sendGrid.send(setupInvitation);

          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Parent profile updated successfully`,
            { userId: userUpdated.userId, isFirebaseIdentifierExists: false },
            apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
          );
          return res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            "Error while updating user document",
            `There was an error while updating user document. Error: ${err}`
          );
          return res.status(400).json(response);
        }
      }
    } catch (error) {
      console.log("ERROR", error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while updating parent profile`,
        apiResponse.apiConstants.USER_DATA_FAILED_TO_UPDATE
      );
      res.status(500).json(response);
    }
  }
);

//add child to parent account
/**
 * @swagger
 * /api/v1/parent/child:
 *   post:
 *     summary: Add Child to Parent Account
 *     description: Adds a child to a parent's account.
 *     tags: [Parent]
 *     parameters:
 *       - in: formData
 *         name: parentUserId
 *         description: The unique ID of the parent user.
 *         required: true
 *         type: string
 *       - in: formData
 *         name: student
 *         description: JSON object containing the details of the student to be added.
 *         required: true
 *         type: string
 *     responses:
 *       200:
 *         description: Child added successfully.
 *       400:
 *         description: Bad request. Either parentUserId or student is missing from the request.
 *       500:
 *         description: Error occurred while adding child to parent account.
 */
router.post(
  "/parent/child",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const parentUserId = fields.parentUserId;
        const student = fields.student;

        //check if parentUserId is provided in the request
        if (!parentUserId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Parent User Id is required`,
            `Please provide parentUserId, it is required to add child to parent account`
          );
          return res.status(200).json(response);
        }

        //check if listOfStudents is provided in the request and is not empty
        if (!student) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `List of students is required`,
            `Please provide listOfStudents, it is required to add child to parent account`
          );
          return res.status(200).json(response);
        }

        let newStudent = await studentHelper.addStudent(
          parentUserId,
          JSON.parse(student)
        );

        //check if student is added successfully
        if (newStudent.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Child added successfully`,
            newStudent.data,
            apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
          );
          res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Error while adding child`,
            newStudent.message
          );
          return res.status(400).json(response);
        }
      } catch (error) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while adding child to parent account`,
          500
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
        return res.status(500).json(response);
      }
    });
  }
);

//Update parent data, my space
/**
 * @swagger
 * /api/v1/parent/myspace:
 *   put:
 *     summary: Update Parent's Personal Information
 *     description: Updates the personal information of a parent user.
 *     tags: [Parent]
 *     parameters:
 *       - in: formData
 *         name: userId
 *         description: The unique ID of the parent user.
 *         required: true
 *         type: string
 *       - in: formData
 *         name: firstName
 *         description: The first name of the parent user.
 *         required: false
 *         type: string
 *       - in: formData
 *         name: email
 *         description: The email address of the parent user.
 *         required: false
 *         type: string
 *       - in: formData
 *         name: lastName
 *         description: The last name of the parent user.
 *         required: false
 *         type: string
 *       - in: formData
 *         name: dateOfBirth
 *         description: The date of birth of the parent user.
 *         required: false
 *         type: string
 *         format: date
 *       - in: formData
 *         name: gender
 *         description: The gender of the parent user.
 *         required: false
 *         type: string
 *       - in: formData
 *         name: phoneNumber
 *         description: The phone number of the parent user.
 *         required: false
 *         type: string
 *       - in: formData
 *         name: address
 *         description: The address of the parent user.
 *         required: false
 *         type: string
 *     responses:
 *       200:
 *         description: Parent's personal information updated successfully.
 *       400:
 *         description: Bad request. The provided userId is required.
 *       404:
 *         description: User not found. No user exists with the provided userId.
 *       500:
 *         description: Error occurred while updating parent data.
 */
router.put(
  "/parent/myspace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const userId = fields.userId;
        const firstName = fields.firstName;
        const email = fields.email;
        const lastName = fields.lastName;
        const dateOfBirth = fields.dateOfBirth;
        const gender = fields.gender;
        const telephone = fields.phoneNumber;
        const address = fields.address;
        const zipCode = fields.zipCode;
        const socioProfessionalCategory = fields.socioProfessionalCategory;
        //check if userId is provided in the request
        if (!userId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User Id is required`,
            `Please provide userId, it is required to update parent data`
          );
          return res.status(200).json(response);
        }

        //Get user data from database
        let userDocument = await userHelper.getUserDetailsByUserId(userId);
        //check if user is found
        if (!userDocument) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User not found`,
            `User not found, please provide valid userId`
          );
          res.status(200).json(response);
        }
        //update user email address in firebase
        let oldEMail = userDocument.contactDetails.email;
        let newEmail = email;
        await parentHelper.updateParentEmail(oldEMail, newEmail);
        //update firebase identifier in user document firebaseIds
        userDocument.firebaseIds.forEach((firebaseId) => {
          if (firebaseId.identifier === oldEMail) {
            firebaseId.identifier = newEmail;
          }
        });

        //update User details
        userDocument.contactDetails.firstName = firstName
          ? firstName
          : userDocument.contactDetails.firstName;
        userDocument.contactDetails.lastName = lastName
          ? lastName
          : userDocument.contactDetails.lastName;
        userDocument.contactDetails.email = email
          ? email
          : userDocument.contactDetails.email;
        userDocument.contactDetails.phoneNumber = telephone
          ? telephone
          : userDocument.contactDetails.phoneNumber;
        userDocument.dateOfBirth = dateOfBirth
          ? dateOfBirth
          : userDocument.dateOfBirth;
        userDocument.gender = gender ? gender : userDocument.gender;

        //update address
        userDocument.address.addressLine1 = address
          ? address
          : userDocument.address.addressLine1;

        userDocument.address.zipCode = zipCode
          ? zipCode
          : userDocument.address.zipCode;

        let userUpdated = await userDocument.save();
        let parentPref = await ParentPreferencesModel.findOneAndUpdate(
          { userId: userId },
          { $set: { socioProfessionalCategory: socioProfessionalCategory } },
          { new: true }
        );
        if (userUpdated && parentPref) {
          let parentUser = {};
          const socioProfessionalCategory =
            parentPref.socioProfessionalCategory;
          parentUser = { userUpdated, socioProfessionalCategory };
          userUpdated.firstName = parentPref.socioProfessionalCategory;
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Parent data updated successfully`,
            parentUser,
            apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
          );
          res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `Error while updating parent data`,
            `There was an error while updating parent data. Error: ${err}`
          );
          return res.status(400).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while updating parent data`,
          `There was an error while updating parent data. Error: ${err}`
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
        return res.status(500).json(response);
      }
    });
  }
);

//Get my space data
/**
 * @swagger
 * /api/v1/parent/myspace:
 *   get:
 *     summary: Get Parent's Personal Information
 *     description: Retrieves the personal information of a parent user.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The unique ID of the parent user.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Parent's personal information retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   description: The unique ID of the parent user.
 *                 email:
 *                   type: string
 *                   description: The email address of the parent user.
 *                 firstName:
 *                   type: string
 *                   description: The first name of the parent user.
 *                 lastName:
 *                   type: string
 *                   description: The last name of the parent user.
 *                 phoneNumber:
 *                   type: string
 *                   description: The phone number of the parent user.
 *                 address:
 *                   type: string
 *                   description: The address of the parent user.
 *                 dateOfBirth:
 *                   type: string
 *                   format: date
 *                   description: The date of birth of the parent user.
 *                 gender:
 *                   type: string
 *                   description: The gender of the parent user.
 *       400:
 *         description: Bad request. The provided userId is required.
 *       404:
 *         description: User not found. No user exists with the provided userId.
 *       500:
 *         description: Error occurred while getting parent data.
 */
router.get(
  "/parent/myspace",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is provided in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to get parent data`
        );
        return res.status(200).json(response);
      }

      //Get user data from database
      let userDocument = await userHelper.getUserDetailsByUserId(userId);

      //check if user is found
      if (!userDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found`,
          `User not found, please provide valid userId`
        );
        return res.status(200).json(response);
      }
      let socioProfessionalCategory = "";
      const parentPref = await ParentPreferencesModel.find({
        userId: userId,
      }).lean();
      socioProfessionalCategory = parentPref[0].socioProfessionalCategory;
      //get firstName, lastName, telephone, address, dateOfBirth, gender
      let parentData = {
        userId: userDocument.userId,
        email: userDocument.contactDetails.email,
        firstName: userDocument.contactDetails.firstName,
        lastName: userDocument.contactDetails.lastName,
        phoneNumber: userDocument.contactDetails.phoneNumber,
        address: userDocument.address.addressLine1,
        zipCode: userDocument.address.zipCode,
        dateOfBirth: userDocument.dateOfBirth,
        gender: userDocument.gender,
        socioProfessionalCategory: socioProfessionalCategory,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Parent my space data`,
        parentData
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting parent data`,
        500
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//get all children of a parent account by parentUserId
/**
 * @swagger
 * /api/v1/parent/child:
 *   get:
 *     summary: Get Children of Parent Account
 *     description: Retrieves the children associated with a parent account.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: parentUserId
 *         description: The unique ID of the parent user.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Children of the parent account retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   // Define properties of children object here
 *       400:
 *         description: Bad request. The provided parentUserId is required.
 *       500:
 *         description: Error occurred while getting children of a parent account.
 */
router.get(
  "/parent/child",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      let parentUserId = req.query.parentUserId;
      let children = await studentHelper.getAllChildrenOfParent(parentUserId);
      if (children.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `List of children of a parent account`,
          children.data
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while getting children of a parent account`,
          children.message
        );
        return res.status(400).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting children of a parent account`,
        500
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//get child details by childUserId
/**
 * @swagger
 * /api/v1/parent/child/details:
 *   get:
 *     summary: Get Child Details
 *     description: Retrieves the details of a child associated with a parent account.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The unique ID of the child user.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Child details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 // Define properties of child details object here
 *       400:
 *         description: Bad request. The provided userId is required.
 *       500:
 *         description: Error occurred while getting child details.
 */
router.get(
  "/parent/child/details",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      //check if userId is provided in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to get child details`
        );
        return res.status(200).json(response);
      }

      const studentDocument = await studentHelper.studentDetails(userId);

      if (studentDocument.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Child details`,
          studentDocument.data
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while getting child details`,
          studentDocument.message
        );
        return res.status(400).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting child details`,
        500
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//update child details
/**
 * @swagger
 * /api/v1/parent/child:
 *   put:
 *     summary: Update Child Details
 *     description: Updates the details of a child associated with a parent account.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               student:
 *                 type: string
 *                 description: JSON string containing the updated details of the child.
 *     responses:
 *       200:
 *         description: Child details updated successfully.
 *       400:
 *         description: Bad request. The provided student data or childUserId is missing.
 *       500:
 *         description: Error occurred while updating child details.
 */
router.put(
  "/parent/child",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const studentData = fields.student;
        console.log("studentData", studentData);
        //check if student is provided in the request
        if (!studentData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Student data is required`,
            `Please provide student data, it is required to update child details`
          );
          return res.status(200).json(response);
        }

        const student = JSON.parse(studentData);
        const childUserId = student.userId;

        //check if childUserId is provided in the request
        if (!childUserId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Child User Id is required`,
            `Please provide childUserId, it is required to update child details`
          );
          return res.status(200).json(response);
        }

        //update email address and telephone number in user document
        let userDocument = await userHelper.getUserDetailsByUserId(childUserId);
        console.log("email", student.email);
        const contactDetails = {
          firstName: student.firstName
            ? student.firstName
            : userDocument.contactDetails.firstName,
          lastName: student.lastName
            ? student.lastName
            : userDocument.contactDetails.lastName,
          email: student.email ? student.email : "",
          phoneNumber: student.phoneNumber ? student.phoneNumber : "",
        };

        userDocument.contactDetails = contactDetails;
        userDocument.dateOfBirth = student.dateOfBirth
          ? student.dateOfBirth
          : userDocument.dateOfBirth;
        userDocument.gender = student.gender
          ? student.gender
          : userDocument.gender;
        //update data in mongoDB
        await userDocument.save();

        let updatedStudent = await studentHelper.updateStudentPreferences(
          childUserId,
          student
        );

        //check if student is updated successfully
        if (updatedStudent.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Child details updated successfully`,
            updatedStudent.data,
            apiResponse.apiConstants.DATA_SAVED_SUCCESSFULLY
          );
          res.status(200).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Error while updating child details`,
            updatedStudent.message
          );
          return res.status(400).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while updating child details`,
          500
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
        return res.status(500).json(response);
      }
    });
  }
);

//delete child from parent account
/**
 * @swagger
 * /api/v1/parent/child:
 *   delete:
 *     summary: Delete Child from Parent Account
 *     description: Deletes a child from a parent's account.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: childUserId
 *         description: The unique ID of the child user.
 *         required: true
 *         schema:
 *           type: string
 *     responses:
 *       200:
 *         description: Child deleted successfully.
 *       400:
 *         description: Bad request. The provided childUserId is required.
 *       500:
 *         description: Error occurred while deleting child from parent account.
 */
router.delete(
  "/parent/child",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      const childUserId = req.query.childUserId;

      //check if childUserId is provided in the request
      if (!childUserId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Child User Id is required`,
          `Please provide childUserId, it is required to delete child from parent account`
        );
        res.status(200).json(response);
      }

      let childDeleted = await studentHelper.deleteStudent(childUserId);

      //check if child is deleted successfully
      if (childDeleted.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Child deleted successfully`,
          childDeleted.data,
          apiResponse.apiConstants.USER_SAVED_SUCCESSFULLY
        );
        res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while deleting child`,
          childDeleted.message
        );
        return res.status(400).json(response);
      }
    } catch (error) {}
  }
);

/**
 * @swagger
 * /api/v1/parent/change-password:
 *   patch:
 *     summary: Change Parent Account Password
 *     description: Changes the password of a parent account.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         description: The unique ID of the parent user.
 *         required: true
 *         schema:
 *           type: string
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               password:
 *                 type: string
 *                 description: The new password for the parent account.
 *     responses:
 *       200:
 *         description: Parent account password successfully updated.
 *       400:
 *         description: Bad request. The provided userId or new password is missing.
 *       500:
 *         description: Error occurred while updating parent account password.
 */
router.patch(
  "/parent/change-password",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      const userId = req.query.userId;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "UserId is required, please provide a valid userId",
          400
        );
        return res.status(400).json(response);
      }
      const user = await userHelper.getUserDetailsByUserId(userId);
      if (!user || !user.contactDetails.email) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User not found: ${userId}, please provide a valid userId`,
          400
        );
        return res.status(400).json(response);
      }
      const { password } = req.body;
      if (!password) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New password is empty`,
          400
        );
        return res.status(400).json(response);
      }

      const userRecord = await firebaseHelper.changePassword(
        user.contactDetails.email,
        password
      );

      if (!userRecord) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error while updating password`,
          400
        );
        return res.status(400).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Account password successfully updated`
      );
      return res.status(200).json(response);
    } catch (error) {
      let errorMessage = `Error while updating password: ${error}`;
      console.log(errorMessage);
      let response = apiResponse.response(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Error while getting tutor preferences",
        errorMessage
      );
      return res.status(500).json(response);
    }
  }
);

//send invitation to  child to join platform, this will send invitation to child email or phone number
/**
 * @swagger
 * /api/v1/parent/send-invitation:
 *   post:
 *     summary: Send Invitation to Child
 *     description: Sends an invitation to a child to join the platform.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               childUserId:
 *                 type: string
 *                 description: The unique ID of the child user.
 *               firebaseIdentifier:
 *                 type: string
 *                 description: The Firebase identifier (email or phone number) of the child.
 *     responses:
 *       200:
 *         description: Invitation sent successfully.
 *       400:
 *         description: Bad request. The provided childUserId or firebaseIdentifier is missing.
 *       500:
 *         description: Error occurred while sending invitation to child.
 */
router.post(
  "/parent/send-invitation",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([ROLE_PARENT, SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      console.log("fields", fields);
      try {
        const childUserId = fields.childUserId;
        const firebaseIdentifier = fields.firebaseIdentifier;
        const userRole = fields.userRole;
        //check if childUserId is provided in the request
        if (!childUserId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Child User Id is required`,
            `Please provide childUserId, it is required to send invitation to child`,
            apiResponse.apiConstants.PARENT_SEND_INVITATION_FAILED
          );
          res.status(200).json(response);
        }

        //check if firebaseIdentifier is provided in the request
        if (!firebaseIdentifier) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Firebase Identifier is required`,
            `Please provide firebaseIdentifier, it is required to send invitation to child`,
            apiResponse.apiConstants.PARENT_SEND_INVITATION_FAILED
          );
          res.status(200).json(response);
        }

        //get child details from User collection
        let childDetails = await userHelper.getUserDetailsByUserId(childUserId);
        console.log("childDetails", childDetails);
        if (!childDetails) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Child details not found`,
            `Please provide valid childUserId, it is required to send invitation to child`,
            apiResponse.apiConstants.PARENT_SEND_INVITATION_FAILED
          );
          res.status(200).json(response);
        }

        const childName = childDetails.contactDetails.firstName;

        //Generate invitation code
        const invitationCode =
          studentHelper.generateInvitationCode(firebaseIdentifier);

        await studentHelper.saveInvitationCodeInDb(invitationCode, childUserId);

        // check if it is an email or phone number
        let isEmail = userHelper.checkIdentifierIsEmail(firebaseIdentifier);
        //check if invitation code is saved successfully

        const invitationLink = `${appConstants.APP_BASE_URL}/${userRole}/register/?accept=${invitationCode}&firebaseIdentifier=${firebaseIdentifier}&userId=${childUserId}`;
        if (isEmail) {
          let emailSubject = `Vous êtes invités sur la plateforme Homeclasse`;
          let setupInvitation = sendGridHelper.invitationToStudent(
            firebaseIdentifier,
            emailSubject,
            invitationLink,
            childName
          );

          await sendGrid
            .send(setupInvitation)
            .then(() => {
              let response = apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_SUCCESS,
                `Invitation sent successfully`,
                `Invitation sent successfully to ${firebaseIdentifier}, please notify child to check email or phone. Invitation code: ${invitationCode}`,
                apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESS
              );
              res.status(200).json(response);
            })
            .catch((error) => {
              console.error(error);
              let apiData = apiResponse.response(
                false,
                "Invitation sent failed.",
                error.message
              );
              return res.status(200).json(apiData);
            });
        } else {
          //send sms to child phone number

          let smsMessage = `Bonjour ${childName}, Nous avons le plaisir de vous inviter à rejoindre la plateforme ZUPdeCO ! Pour accéder à votre espace personnel, veuillez cliquer sur le lien suivant : ${invitationLink}
  À bientôt,
  L’Équipe ZUPdeCO`;

          twilioInit
            .sendSms(firebaseIdentifier, smsMessage)
            .then((data) => {
              let response = apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_SUCCESS,
                `Invitation sent successfully`,
                `Invitation sent successfully to ${firebaseIdentifier}, please notify child to check email or phone`,
                apiResponse.apiConstants.PARENT_SEND_INVITATION_SUCCESSFULLY
              );
              res.status(200).json(response);
            })
            .catch((error) => {
              console.log(error);
            });
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while sending invitation to child`,
          JSON.stringify(error),
          apiResponse.apiConstants.PARENT_SEND_INVITATION_FAILED
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);

        return res.status(500).json(response);
      }
    });
  }
);

//#region DASHBOARD & PROFILE

//Parent Dashboard
/**
 * @swagger
 * /api/v1/parent/dashboard:
 *   get:
 *     summary: Get Parent Dashboard
 *     description: Retrieves the dashboard data for a parent user.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the parent user.
 *     responses:
 *       200:
 *         description: Parent dashboard data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 nextSession:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       studentId:
 *                         type: string
 *                       nextSessionDate:
 *                         type: string
 *                       subject:
 *                         type: string
 *                 nameAndMaxiCours:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       studentId:
 *                         type: string
 *                       studentName:
 *                         type: string
 *                       maxiCoursLink:
 *                         type: string
 *                 numberOfSession:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       studentId:
 *                         type: string
 *                       totalSessions:
 *                         type: integer
 *                       completedSessions:
 *                         type: integer
 *                 absencePerStudent:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       studentId:
 *                         type: string
 *                       totalAbsences:
 *                         type: integer
 *                 referent:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       studentId:
 *                         type: string
 *                       coordinators:
 *                         type: array
 *                         items:
 *                           type: string
 *       400:
 *         description: Bad request. The provided userId is missing.
 *       500:
 *         description: Error occurred while getting parent dashboard.
 */
router.get(
  "/parent/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, ROLE_PARENT]),
  ],
  async (req, res) => {
    try {
      let userId = req.query.userId;

      //check if userId is provided in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to get parent dashboard`,
          apiResponse.apiConstants.PARENT_DASHBOARD_FAILED
        );
        res.status(200).json(response);
      }

      const studentsList = await studentHelper.studentsListPerParent(userId);

      const nextSession = await sessionsParentHelper.getNextSessionsPerStudent(
        studentsList
      );

      const nameAndMaxiCours =
        await sessionsParentHelper.getStudentsNameAndMaxicoursLink(
          studentsList
        );

      const numberOfSession =
        await sessionsParentHelper.getNumberOfSessionsPerStudent(studentsList);

      const absencePerStudent =
        await sessionsParentHelper.getAbsencesPerStudent(studentsList);

      const listOfCoordinatorsPerStudent =
        await sessionsParentHelper.getCoordinatorsPerStudent(studentsList);

      const combineData = {
        nextSession: nextSession.data,
        nameAndMaxiCours: nameAndMaxiCours.data,
        numberOfSession: numberOfSession.data,
        absencePerStudent: absencePerStudent.data,
        referent: listOfCoordinatorsPerStudent.data,
      };

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Parent dashboard`,
        combineData
      );
      res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting parent dashboard`,
        JSON.stringify(error)
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//get all sessions of a child
/**
 * @swagger
 * /api/v1/parent/child/sessions:
 *   get:
 *     summary: Get Child Sessions
 *     description: Retrieves all sessions for children associated with a parent user.
 *     tags: [Parent]
 *     parameters:
 *       - in: query
 *         name: userId
 *         schema:
 *           type: string
 *         required: true
 *         description: The unique ID of the parent user.
 *     responses:
 *       200:
 *         description: Child sessions retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 type: object
 *                 properties:
 *                   sessionId:
 *                     type: string
 *                   studentId:
 *                     type: string
 *                   sessionDate:
 *                     type: string
 *                   subject:
 *                     type: string
 *                   status:
 *                     type: string
 *                   duration:
 *                     type: integer
 *                   rating:
 *                     type: number
 *                   feedback:
 *                     type: string
 *       400:
 *         description: Bad request. The provided userId is missing.
 *       500:
 *         description: Error occurred while getting child sessions.
 */
router.get(
  "/parent/child/sessions",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([ROLE_PARENT])],
  async (req, res) => {
    try {
      let userId = req.query.userId;

      //check if userId is provided in the request
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to get child sessions`
        );
        res.status(200).json(response);
      }
      const studentsList = await studentHelper.studentsListPerParent(userId);
      console.log("studentsList", studentsList);
      let childSessions = await sessionsParentHelper.getAllSessionsPerStudent(
        studentsList
      );
      console.log("childSessions", childSessions);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `All sessions of child`,
        childSessions.data
      );
      res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting child sessions`,
        JSON.stringify(error)
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);

//test my hardware
/**
 * @swagger
 * /api/v1/parent/testHardware:
 *   put:
 *     summary: Test Hardware
 *     description: Test hardware devices (video and audio) for a parent user.
 *     tags: [Parent]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               userId:
 *                 type: string
 *                 description: The unique ID of the parent user.
 *               isWorkingVideoDevice:
 *                 type: boolean
 *                 description: Indicates whether the video device is working or not.
 *               isWorkingAudioDevice:
 *                 type: boolean
 *                 description: Indicates whether the audio device is working or not.
 *     responses:
 *       200:
 *         description: Hardware test successful.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 userId:
 *                   type: string
 *                   description: The unique ID of the parent user.
 *                 testHardware:
 *                   type: object
 *                   properties:
 *                     isWorkingVideoDevice:
 *                       type: boolean
 *                       description: Indicates whether the video device is working or not.
 *                     isWorkingAudioDevice:
 *                       type: boolean
 *                       description: Indicates whether the audio device is working or not.
 *       400:
 *         description: Bad request. The provided userId is missing.
 *       500:
 *         description: Error occurred while testing hardware.
 */
router.put(
  "/parent/testHardware",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        let userId = fields.userId;
        let isWorkingVideoDevice = fields.isWorkingVideoDevice;
        let isWorkingAudioDevice = fields.isWorkingAudioDevice;

        //check if userId is provided in the request
        if (!userId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `User Id is required`,
            `Please provide userId, it is required to test hardware`
          );
          res.status(200).json(response);
        }

        //Get parent Preference
        let parentPreference = await ParentPreferences.findOne({
          userId: userId,
        });

        //check if parentPreference is null or empty, if yes then create new parentPreference
        if (parentPreference) {
          parentPreference.testHardware.isWorkingVideoDevice =
            isWorkingVideoDevice;
          parentPreference.testHardware.isWorkingAudioDevice =
            isWorkingAudioDevice;
          await parentPreference.save();
        } else {
          let newParentPreference = new ParentPreferences({
            userId: userId,
            testHardware: {
              isWorkingVideoDevice: isWorkingVideoDevice,
              isWorkingAudioDevice: isWorkingAudioDevice,
            },
          });
          await newParentPreference.save();
        }

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Hardware tested successfully`,
          parentPreference
        );
        res.status(200).json(response);
      } catch (error) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error occurred while testing hardware`,
          JSON.stringify(error)
        );
        logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
        return res.status(500).json(response);
      }
    });
  }
);
//get parent pref by userId
router.get(
  "/parent/preferences",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      let userId = req.query.userId;
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User Id is required`,
          `Please provide userId, it is required to get parent preferences`
        );
        res.status(200).json(response);
      }
      const parentStatus = await parentHelper.getParentStatus(userId);
      console.log("parentStatus", parentStatus?.data?.status);
      let parentPreference = await parentHelper.getParentPreferences(userId);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Parent preferences`,
        { ...parentPreference.data._doc, status: parentStatus?.data?.status },
        apiResponse.apiConstants.API_REQUEST_SUCCESS
      );
      res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while getting parent preferences`,
        JSON.stringify(error)
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);
router.put(
  "/parent/onBoarding",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const parentId = req.query.userId;
      const parentObBoardingData = req.body;

      const updatedParent = await parentHelper.updateParentPreferences(
        parentId,
        parentObBoardingData
      );

      if (
        (parentObBoardingData.onBoardingStep === "4" ||
          parentObBoardingData.onBoardingStep === "7") &&
        parentObBoardingData.quizScore >= 12
      ) {
        const updatedParentStatus =
          await parentHelper.updateParentAndChildStatus(parentId, "active");

        if (updatedParentStatus.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Parent onboarding data updated successfully`,
            updatedParentStatus.data
          );
          return res.status(200).json(response);
        }
      }

      if (updatedParent?.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Parent onboarding data updated successfully`,
          updatedParent?.data
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Error while updating parent onboarding data`
        );
        return res.status(400).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error occurred while updating parent onboarding data`
      );
      logger.newLog(logger.getLoggerTypeConstants().parent).error(response);
      return res.status(500).json(response);
    }
  }
);
//#endregion

module.exports = router;
