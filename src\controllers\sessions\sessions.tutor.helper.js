const sessionModel = require("../../models/Sessions.Model.js");
const crSessionsModel = require("../../models/Cr.Report.Model.js");

const sessionsConstants = require("../../utils/constants/sessions.constants.js");

const userHelper = require("../user/User.Helper.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const dateTimeHelper = require("../../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper");

const studentHelper = require("../students/student.helpers.js");

const parentHelper = require("../parent/parent.helper.js");

const sectorHelper = require("../sectors/sectors.helper.js");

const reportConstants = require("../../utils/constants/reports.constants.js");

//import tutorPreferences
const tutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const crscreportsModel = require("../../models/Cr.Report.Model.js");
const moment = require("moment");
exports.getTop3Sessions = async (tutorId) => {
  console.log("tutorId", tutorId);
  try {
    //get current timestamp in paris zone
    const currentTimestamp = dateTimeHelper.getCurrentDateInParisZone();

    //get top 3 sessions for tutor, top 3 sessions are sessions that are scheduled for today or in the future  but not before the current time
    const top3Sessions = await sessionModel
      .find({
        tutors: {
          $elemMatch: {
            userId: tutorId,
            // A verifier
            // , absence: true
          },
        },
        "sessionDate.startDate": { $gt: currentTimestamp },
        parentSessionId: { $ne: null },
        status: { $ne: "canceled" },
      })
      .sort({
        "sessionDate.startDate": 1, // sort by month in ascending order
      })
      .limit(3)
      .exec();
    console.log("top3Sessions", top3Sessions);
    //if there is no top 3 sessions
    let nextSessionList = [];
    if (top3Sessions.length === 0) {
      return { status: false, message: "No top 3 sessions" };
    } else {
      //return these fields only : sessionId, sessionType, program, status, tutors, vsc, school, placeOrRoom, sessionDate

      for (let i = 0; i < top3Sessions.length; i++) {
        const session = top3Sessions[i];
        const sessionStudents = session.students;

        const parentProfile = await this.getParentProfilePerStudent(
          session.students[0].userId
        );

        for (let j = 0; j < sessionStudents.length; j++) {
          const student = sessionStudents[j];
          const studentId = student.userId;
          //get student profile with data from Student collection
          const studentProfile = await userHelper.getStudentProfileAndLevel(
            studentId
          );

          // const coordinatorProfile = await this.getCoordinatorProfilePerStudent(
          //   studentId
          // );

          if (studentProfile.status && studentProfile.data) {
            const nextSession = {
              sessionId: session.sessionId,
              sessionType: session.sessionType,
              program: session.program,
              status: session.status,
              vsc: session.vsc,
              studentProfile: studentProfile.data,
              school: session.school,
              report: session.report,
              referent: parentProfile.data ? [parentProfile.data] : [],
              sessionLink: session.sessionLink,
              placeOrRoom: session.placeOrRoom,
              sessionDate: session.sessionDate,
            };

            nextSessionList.push(nextSession);
          }
        }
      }

      return {
        status: true,
        message: "Top 3 sessions for tutor",
        data: nextSessionList,
      };
    }
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//get getActiveStudentsBaseOnTutor  for tutor dashboard
exports.getActiveStudentsBaseOnTutor = async (tutorId) => {
  try {
    const currentTimestamp = dateTimeHelper.getCurrentDateTimeInParisZone();
    //getActiveStudents for tutor dashboard based on tutor id. The condition is that the tutor is in the session,
    //object name is tutors and the absence is false and session status is Scheduled
    const listOfSessionsWithStudents = await sessionModel
      .find({
        tutors: {
          $elemMatch: {
            userId: tutorId,
            // absence: false,
          },
        },
        "sessionDate.startDate": { $gt: currentTimestamp },
        parentSessionId: { $ne: null },
        status: {
          $in: [
            sessionsConstants.sessionsStatus.SCHEDULED,
            sessionsConstants.sessionsStatus.CONFIRMED,
            sessionsConstants.sessionsStatus.SESSIONS_0_SUGGESTED,
            sessionsConstants.sessionsStatus.SESSIONS_0_TO_BE_SCHEDULED,
            sessionsConstants.sessionsStatus.SESSIONS_0_CONFIRMED,
          ],
        },
      })
      .sort({
        "sessionDate.startDate": 1, // sort by month in ascending order
      });
    let listOfStudents = [];

    //check if there any active students
    if (listOfSessionsWithStudents.length > 0) {
      listOfSessionsWithStudents.forEach((session) => {
        session.students.forEach((student) => {
          listOfStudents.push(student.userId);
        });
      });
    }
    //remove duplicates
    const listOfStudentsWithoutDuplicates = [...new Set(listOfStudents)];

    //get students profiles
    const studentsProfiles = await studentHelper.getStudentsProfilesFromUserId(
      listOfStudentsWithoutDuplicates
    );
    //get next session for each student
    let studentsProfilesWithNextSession = [];

    // Importer le helper Firebase pour accéder aux informations de dernière connexion
    const firebaseHelper = require("../firebase/firebase.helper.js");
    const userHelper = require("../user/User.Helper.js");

    for (let i = 0; i < studentsProfiles.data.length; i++) {
      const studentProfile = studentsProfiles.data[i];
      const studentId = studentProfile?.userId;
    
      // Récupérer l'utilisateur complet pour obtenir les firebaseIds
      const userDetails = await userHelper.getUserDetailsByUserId(studentId);
      
      // Ajouter la date de dernière connexion s'il y a des firebaseIds
      if (userDetails && userDetails.firebaseIds && userDetails.firebaseIds.length > 0) {
        const firebaseIdentifier = userDetails.firebaseIds[0].identifier;
        const lastLoginResponse = await firebaseHelper.getUserByEmailOrPhone(firebaseIdentifier);
        
        if (lastLoginResponse && lastLoginResponse.status && lastLoginResponse.data && 
            lastLoginResponse.data.metadata && lastLoginResponse.data.metadata.lastSignInTime) {
          const lastLoginDate = new Date(lastLoginResponse.data.metadata.lastSignInTime);
          // Formatage de la date au format DD/MM/YYYY
          const day = String(lastLoginDate.getDate()).padStart(2, '0');
          const month = String(lastLoginDate.getMonth() + 1).padStart(2, '0');
          const year = lastLoginDate.getFullYear();
          studentProfile.lastLoginDate = `${day}/${month}/${year}`;
        } else {
          studentProfile.lastLoginDate = null;
        }
      } else {
        studentProfile.lastLoginDate = null;
      }
      
      const sessionPassesNbre = await sessionModel.countDocuments({
        status: "session-0-to-be-scheduled",
        "tutors.userId": tutorId,
        parentSessionId : {$ne : null},
            // absence: false,
        "students":  {
          $elemMatch: {
            userId: studentsProfiles.data[i].userId,
          },
        },
        "sessionDate.startDate": { $lte: new Date() }
      });
      studentProfile.sessionNbr = sessionPassesNbre;
      const nbrCr =  await crscreportsModel.countDocuments({
        status: { $ne: "to-be-entered" },
        "tutors.userId": tutorId,
        "students":  {
          $elemMatch: {
            userId: studentsProfiles.data[i].userId,
          },
        },
      });
      const aggregationResult = await crscreportsModel.aggregate([
        {
          $match: {
            status: { $ne: "to-be-entered" },
            "tutors.userId": tutorId,
            "students":  {
              $elemMatch: {
                userId: studentsProfiles.data[i].userId,
              },
            },
          }
        },
        {
          $group: {
            _id: null,
            sumGrade: { $sum: "$grade" },
            count: { $sum: 1 }
          }
        }
      ]);
      
      const SumGradeOptimized = aggregationResult.length > 0 ? aggregationResult[0].sumGrade : 0;
      studentProfile.note =  nbrCr > 0 ? Math.floor(SumGradeOptimized / nbrCr) : 0;
      // //get current timestamp in paris zone
      const currentTimestamp = dateTimeHelper.getCurrentDateTimeInParisZone();
      if (studentId) {
        const nextSession = await sessionModel
          .findOne({
            tutors: { $elemMatch: { userId: tutorId } },
            students: { $elemMatch: { userId: studentId } },
            "sessionDate.startDate": { $gt: currentTimestamp },
            status: {$ne : sessionsConstants.sessionsStatus.CANCELED},
            parentSessionId: { $ne: null },
          })
          .sort({
            "sessionDate.startDate": 1, // sort by month in ascending order
          })
          .exec();
        const lastSession = await sessionModel
        .findOne({
          tutors: { $elemMatch: { userId: tutorId } },
          students: { $elemMatch: { userId: studentId } },
          "sessionDate.startDate": { $lte: new Date() },
          status: {$ne : sessionsConstants.sessionsStatus.CANCELED},
          parentSessionId: { $ne: null },
        }).sort({"sessionDate.endDate" : -1});
        studentProfile.lastSession = lastSession;
        const coordinatorProfile = await studentHelper.getStudentPreferences(
          studentId
        );
        //if there is session for student then build student profile with next session

        const nextSessionData = {
          sessionId: nextSession ? nextSession.sessionId : "",
          sessionType: nextSession ? nextSession.sessionType : "",
          program: nextSession ? nextSession.program : "",
          status: nextSession ? nextSession.status : "",
          vsc: nextSession ? nextSession.vsc : "",
          report: nextSession ? nextSession.report : "",
          studentProfile: studentProfile,
          school: nextSession ? nextSession.school : "",
          referent: coordinatorProfile.data
            ? [coordinatorProfile.data.parentData]
            : [],
          sessionLink: nextSession ? nextSession.sessionLink : "",
          placeOrRoom: nextSession ? nextSession.placeOrRoom : "",
          sessionDate: nextSession ? nextSession.sessionDate : "",
        };

        studentsProfilesWithNextSession.push(nextSessionData);
      }
    }

    return {
      status: true,
      message: "Active students for tutor",
      data: studentsProfilesWithNextSession,
    };

    //get students profiles
  } catch (error) {
    console.log("error in getActiveStudentsBaseOnTutor : ", error.message);
    return { status: false, message: error.message };
  }
};

//get getTutorRightReport for tutor dashboard with All sessions for tutor and Total number of active students
exports.getAllSessionForTutorAsNumber = async (tutorId) => {
  try {
    const allSessionForTutor = await sessionModel.countDocuments({
      tutors: { $elemMatch: { userId: tutorId } },
      parentSessionId: { $ne: null },
      status: { $ne: "canceled" },
    });
    return {
      status: true,
      message: "All sessions for tutor",
      data: allSessionForTutor,
    };
  } catch (error) {
    console.log("error in getAllSessionForTutorAsNumber : ", error.message);
    return { status: false, message: error.message };
  }
};

//get report data for tutor dashboard
exports.reportDataForTutorDashboard = async (tutorId) => {
  try {
    const currentDateTime = dateTimeHelper.getCurrentDateTimeInParisZone();
    //get all past session
    const allPastSessions = await sessionModel
      .find({
        tutors: { $elemMatch: { userId: tutorId } },
        "sessionDate.startDate": { $lt: currentDateTime },
        parentSessionId: { $ne: null },
        status: { $ne: "canceled" },
      })
      .exec();

    //count number of reports base on report.status : to-be-entered is when status is empty or null, entered and confirmed
    let numberOfReportsToBeEntered = 0;
    let numberOfReportsEntered = 0;
    let numberOfReportsConfirmed = 0;

    const reports = allPastSessions.flatMap((session) =>
      session.tutors?.map((tutor) => tutor?.report)
    );
    reports.forEach((report) => {
      if (
        report.status ===
        reportConstants.crScReportsStatus.entered.crScReportsStatusId
      ) {
        numberOfReportsEntered++;
      } else if (
        report.status ===
        reportConstants.crScReportsStatus.confirmed.crScReportsStatusId
      ) {
        numberOfReportsConfirmed++;
      } else {
        numberOfReportsToBeEntered++;
      }
    });

    const reportData = {
      numberOfReportsToBeEntered: numberOfReportsToBeEntered,
      numberOfReportsEntered: numberOfReportsEntered,
      numberOfReportsConfirmed: numberOfReportsConfirmed,
    };

    return {
      status: true,
      message: "Report data for tutor dashboard",
      data: reportData,
    };
  } catch (error) {
    console.log("error in reportDataForTutorDashboard : ", error.message);
    return { status: false, message: error.message, data: null };
  }
};

//get coordinator profile based  on sectorId on tutor profile
exports.getCoordinatorProfilePerStudent = async (userId) => {
  try {
    const studentProfile = await studentHelper.getStudentPreferences(userId);
    if (!studentProfile.status) {
      return { status: false, message: studentProfile.message };
    }
    // workhere
    const sectors = studentProfile.data.assignment.sectors;

    const sectorIds = sectors.map((sector) => sector.sectorId);

    const coordinatorProfile = await sectorHelper.getCoordinatorsPerSector(
      sectorIds
    );
    return { status: true, data: coordinatorProfile.data };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: [] };
  }
};

//get parent profile based  on sectorId on student profile
exports.getParentProfilePerStudent = async (userId) => {
  try {
    const studentProfile = await studentHelper.getStudentPreferences(userId);
    const parentProfile = await parentHelper.getParentIS(
      studentProfile.data.parentUserId
    );

    return { status: true, data: parentProfile.data };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get All sessions for Tutor
exports.getAllSession = async (tutorId, startDate, endDate, status) => {
  try {
    //get current timestamp in paris zone
    const currentTimestamp = dateTimeHelper.getCurrentDateTimeInParisZone();

    const parsedStartDate = moment(startDate, "YYYY-MM-DDTHH:mm:ssZ").toDate(); // Convert to Date object
    const parsedEndDate = moment(endDate, "YYYY-MM-DDTHH:mm:ssZ").toDate();

    // Construire la requête de base
    let query = {
      tutors: {
        $elemMatch: {
          userId: tutorId,
        },
      },
      parentSessionId: { $ne: null },
      status: { $ne: "canceled" },
      "sessionDate.startDate": { $gte: parsedStartDate },
      "sessionDate.endDate": { $lte: parsedEndDate },
    };

    // Ajouter le filtre de statut si spécifié
    if (status) {
      query.status = status;
    }

    const sessionList = await sessionModel
      .find(query)
      .sort({
        "sessionDate.startDate": 1, // sort by month in ascending order
      })
      .exec();

    //if there is no top 3 sessions
    if (sessionList.length === 0) {
      return { status: false, message: "No sessions found" };
    } else {
      //return these fields only : sessionId, sessionType, program, status, tutors, vsc, school, placeOrRoom, sessionDate
      let nextSessionList = [];
      for (let i = 0; i < sessionList.length; i++) {
        const session = sessionList[i];
        const sessionStudents = session.students;

        for (let j = 0; j < sessionStudents.length; j++) {
          const student = sessionStudents[j];
          const studentId = student.userId;
          //get student profile with data from Student collection
          const studentProfile = await userHelper.getStudentProfileAndLevel(
            studentId
          );

          const parentProfile = await this.getParentProfilePerStudent(
            studentId
          );
          
          //if studentProfile is not empty
          if (studentProfile.data) {
            const nextSession = {
              sessionId: session.sessionId,
              sessionType: session.sessionType,
              program: session.program,
              status: session.status,
              statusOfReport: session.tutors.filter(
                (tutor) => String(tutor.userId) === String(tutorId)
              )[0].report.status,
              vsc: session.vsc,
              studentProfile: studentProfile.data,
              school: session.school,
              report: session.report,
              referent: parentProfile.data ? [parentProfile.data] : [],
              sessionLink: session.sessionLink,
              placeOrRoom: session.placeOrRoom,
              sessionDate: session.sessionDate,
              sessionRecurrence: session.recurrence,
            };
            nextSessionList.push(nextSession);
          }
        }
      }

      return {
        status: true,
        message: "All sessions for tutor",
        data: nextSessionList,
      };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get all past sessions for tutor
exports.getAllPastSessions = async (
  tutorId,
  page,
  pageSize,
  filter,
  sortByObject
) => {
  try {
    let applyFilterData = buildFilter(filter);

    let applyFilter = applyFilterData.data;
    if (!applyFilterData.status) {
      return { status: false, message: applyFilterData.message };
    }

    let sortBy = buildSortBy(sortByObject);

    let skip = (page - 1) * pageSize;

    const result = await sessionModel.aggregate([
      // Étape 1 : Filtrer les sessions passées
      {
        $match: {
          tutors: { $elemMatch: { userId: tutorId } }, // Sessions où le tuteur est présent
          "sessionDate.startDate": { $lt: new Date() }, // Sessions passées
          parentSessionId: { $ne: null }, // Sessions avec un parentSessionId non nul
          status: { $ne: "canceled" }, // Sessions non annulées
          "tutors.report": { $exists: false }, // Sessions sans rapport
        },
      },
      // Étape 2 : Trier par date de début
      {
        $sort: { "sessionDate.startDate": 1 },
      },
      // Étape 3 : Jointure avec la collection `crSessionsModel` pour vérifier les rapports existants
      {
        $lookup: {
          from: "crSessions", // Nom de la collection `crSessionsModel`
          localField: "sessionId", // Champ dans `sessionModel`
          foreignField: "sessionId", // Champ dans `crSessionsModel`
          as: "reportData", // Stocke les rapports correspondants
        },
      },
      // Étape 4 : Filtrer les sessions sans rapport
      {
        $match: {
          reportData: { $size: 0 }, // Ne garder que les sessions sans rapport
        },
      },
      // Étape 5 : Pagination (optionnelle, si vous voulez paginer)
      // {
      //   $skip: skip,
      // },
      // {
      //   $limit: pageSize,
      // },
      // Étape 6 : Compter le nombre total de sessions
      {
        $facet: {
          paginatedResults: [
            // { $skip: skip }, // Appliquer le saut
            // { $limit: pageSize }, // Appliquer la limite
          ],
          totalCount: [
            { $count: "count" }, // Compter le nombre total de sessions
          ],
        },
      },
    ]);
    // Extraire les résultats
    const paginatedResults = result[0]?.paginatedResults; // Sessions paginées
    const totalCount = result[0]?.totalCount[0]?.count || 0; // Nombre total de sessions

    return {
      status: true,
      message: "All past sessions for tutor",
      data: paginatedResults,
      total: totalCount,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

function buildFilter(filter) {
  let applyFilter = {};

  //check if the filter is json or not
  if (filter != undefined && filter != null && filter != "") {
    if (!isJSON(filter)) {
      return { status: false, message: "Filter is not a valid Json " };
    } else {
      applyFilter = JSON.parse(filter);
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    console.log(`Filter data:  ${key}: ${value}`);
    if (key == "establishment") {
      //update email key to contactDetails.email
      applyFilter["assignment.establishments"] = {
        $elemMatch: {
          establishmentId: value,
        },
      };
      delete applyFilter.establishment;
    }
  }

  //filter with value string RegExp
  for (const [key, value] of Object.entries(applyFilter)) {
    if (typeof value == "string") {
      let regex = new RegExp(value, "i");
      applyFilter[key] = regex;
    }
  }
  return { status: true, data: applyFilter };
}

function buildSortBy(sortByObject) {
  let sortBy = [];
  if (sortByObject != undefined && sortByObject != null && sortByObject != "") {
    sortByObject = JSON.parse(sortByObject);
    for (let [key, value] of Object.entries(sortByObject)) {
      if (key == "lastName") {
        //update email key to contactDetails.email
        key = "contactDetails.lastName";
      }
      let item = [key, value];
      sortBy.push(item);
    }
  } else {
    sortBy = [["sessionDate.startDate", -1]];
  }
  return sortBy;
}

function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
}