const express = require("express");
const client = require("prom-client");
const responseTime = require("response-time");
const cookieParser = require("cookie-parser");
//creating a register for the metrics
const register = new client.Registry();
// Set some default metrics for the process
client.collectDefaultMetrics({ register });
const {
  connectToMongoDB,
} = require("./src/middleware/mongodb/Init.MongoDb.js");

const app = express();
connectToMongoDB();
//Reading req.body in post requests
app.use(express.json());
app.use(cookieParser());

const bodyParser = require("body-parser");

const helmet = require("helmet");
const morgan = require("morgan");
app.use(helmet());

require("dotenv").config();

const apiConstants = require("./src/utils/constants/apiConstants.js");

//#region CORS ORIGIN
var cors = require("cors");

const Sentry = require("@sentry/node");

const swaggerUi = require("swagger-ui-express");
const swaggerJsdoc = require("swagger-jsdoc");
const swaggerOptions = {
  swaggerDefinition: {
    openapi: "3.0.0",
    info: {
      title: "Zupdeco API",
      version: "1.0.0",
      description: "Zupdeco API swagger doc",
      contact: {
        name: "Zupdeco",
      },
      servers: ["http://localhost:3000/api/v1"],
    },
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
        },
      },
    },

    securityDefinitions: {
      ApiKeyAuth: {
        type: "apiKey",
        in: "header",
        name: "api-key",
      },
    },
    security: [{ ApiKeyAuth: [], bearerAuth: [] }],
  },
  apis: ["./src/routes/*.js"],
};
const specs = swaggerJsdoc(swaggerOptions);

// Save the Swagger JSON to a file
const fs = require("fs");
fs.writeFileSync("./swagger.json", JSON.stringify(specs, null, 2));

app.use("/api/v1/docs", swaggerUi.serve, swaggerUi.setup(specs));
// Histogram to track latency (response time) for each endpoint
const httpRequestDuration = new client.Histogram({
  name: "http_request_duration_seconds",
  help: "Histogram of HTTP request durations in seconds",
  labelNames: ["method", "route", "status"], // Labels for filtering
  buckets: [0.1, 0.3, 0.5, 0.7, 1, 2, 5], // Buckets for response times
});
register.registerMetric(httpRequestDuration);

// Middleware to measure response time and track latency
app.use(
  responseTime((req, res, time) => {
    const route = req.route?.path || req.path; // Use the route path if available
    httpRequestDuration
      .labels(req.method, route, res.statusCode)
      .observe(time / 1000); // Convert ms to seconds
  })
);

const allowlist = [
  "https:/localhost/400",
  "http://localhost:3000",
  "http://localhost:3001",
  "http://localhost:5173",
  "https://zupdeco-development-jh3pg.ondigitalocean.app",
  "https://zupdeco-preprod-5qvv9.ondigitalocean.app",
  "https://zupdeco-prod-5lriu.ondigitalocean.app",
  "https://plateforme.zupdeco.org",
];

const corsOptionsDelegate = (req, callback) => {
  let corsOptions = {
    credentials: true,
  };

  //get env variable is dev or prod
  const isDev = process.env.NODE_ENV === "development";

  // Get the origin from the request
  const headerOrigin = req.header("Origin");
  const origin = req.headers.origin;
  // Check if the origin matches any entry in the allowlist
  const isDomainAllowed = allowlist.some((entry) => {
    const pattern = new RegExp(`^${entry}`);
    return pattern.test(origin);
  });

  if (isDev) {
    corsOptions = { ...corsOptions, origin: true };
  } else {
    corsOptions = { ...corsOptions, origin: true };
    // if (isDomainAllowed) {
    //   // Enable CORS for this request
    //   corsOptions = { origin: true };
    // } else {
    //   // Disable CORS for this request
    //   corsOptions = { origin: false };
    //   // Return a 500 error response
    //   return callback(new Error("CORS: Domain not allowed"), corsOptions);
    // }
  }

  callback(null, corsOptions);
};

// app.use(function (req, res, next) {
//      if (req.url == "/api/v1/bigBlueButton/webhooks") {
//           next();
//      }
// });

app.use(cors(corsOptionsDelegate));
//#endregion

//#endregion

//#region INIT FIREBASE
const initFirebase = require("./src/middleware/firebase/Init.Firebase.js");
initFirebase.initFirebase = new initFirebase.initFirebase();
//#endregion

Sentry.init({
  dsn: process.env.SENTRY_DNS,
  environment: process.env.NODE_ENV,
  enabled: process.env.NODE_ENV === "production",
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Express({
      app,
    }),
  ],
  tracesSampleRate: process.env.NODE_ENV === "production" ? 0.5 : 1.0,
});

app.use(Sentry.Handlers.requestHandler());
app.use(Sentry.Handlers.tracingHandler());
app.get("/metrics", async (req, res) => {
  res.set("Content-Type", register.contentType);
  res.end(await register.metrics());
});
//#region ROUTES
//import routes and add to the array of routes
const apiLogger = require("./src/routes/apiLogger.routes.js");
const user = require("./src/routes/user.routes.js");
const tutor = require("./src/routes/tutor.routes.js");
const parent = require("./src/routes/parent.routes.js");
const student = require("./src/routes/student.routes.js");
const bigbluebutton = require("./src/routes/bigbluebutton.routes.js");
const testHardware = require("./src/routes/hardware.routes.js");
const scholarYear = require("./src/routes/scholaryear.routes.js");
const schoolZone = require("./src/routes/schoolZone.routes.js");
const zupdecoEmails = require("./src/routes/zupdecoEmails.routes.js");
const publicHolidays = require("./src/routes/publicHolidays.routes.js");
const sectors = require("./src/routes/sectors.routes.js");
const establishment = require("./src/routes/establishment.routes.js");
const userRoleRoutes = require("./src/routes/userRole.routes.js");
const availability = require("./src/routes/availability.routes.js");
const userAdministration = require("./src/routes/user.administration.routes.js");
const sessionsRoute = require("./src/routes/sessions.routes.js");
const externalUsersAdministration = require("./src/routes/externalUsers.administration.routes.js");
const levelsRoutes = require("./src/routes/levels.routes.js");
const specialtyRoutes = require("./src/routes/specialty.routes.js");
const matchingRoutes = require("./src/routes/matching.routes.js");
const crReportsRoutes = require("./src/routes/cr.reports.routes.js");
const scReportsRoutes = require("./src/routes/sc.reports.routes.js");
const myMaxicours = require("./src/routes/maxicour.routes.js");
const studentAdmin = require("./src/routes/students.administartion.routes.js")
const bbbWebhooks = require("./src/routes/bbb.webhooks.routes.js");
const sessionTraking = require("./src/routes/sessionLiveTraking.routes.js")

//Jobs from services
const monitoringWebhooksJob = require("./src/services/aaJobs/monitoringWebhooks.Job.js");
monitoringWebhooksJob.webhookHealthCheck;

//Job for SC Reports
const scReportsJob = require("./src/services/aaJobs/scReports.job.js");
// scReportsJob.scReportsCreatedWeekly;

// Job for session reminders
const sessionsJob = require("./src/services/aaJobs/sessions.job.js");
sessionsJob.sendReminderForSession;

const tutorJob = require("./src/services/aaJobs/tutor.job.js");
tutorJob.sendReminderToTutor;

//Tutor Job
// const tutorJob = require("./src/services/aaJobs/tutor.job.js");
// tutorJob.checkUserLoginExpirationTime;

//add routes to the array
const routes = [
  user,
  bigbluebutton,
  tutor,
  parent,
  student,
  testHardware,
  zupdecoEmails,
  myMaxicours,
  scholarYear,
  schoolZone,
  publicHolidays,
  sectors,
  studentAdmin,
  establishment,
  userRoleRoutes,
  bbbWebhooks,
  availability,
  userAdministration,
  sessionsRoute,
  externalUsersAdministration,
  levelsRoutes,
  specialtyRoutes,
  matchingRoutes,
  crReportsRoutes,
  scReportsRoutes,
  sessionTraking
];

app.use(apiConstants.API_VERSION + "/bigBlueButton/webhooks", bbbWebhooks);

//add routes to the app
app.use(apiConstants.API_VERSION, routes);

//Api Config route
const apiConfig = require("./src/routes/api.config.routes.js");
const { credential } = require("firebase-admin");

//Config default route handler
app.use((req, res, next) => {
  apiConfig.defaultRoute(req, res, next);
});

app.use(Sentry.Handlers.errorHandler());

//Config default error handler
app.use((err, req, res, next) => {
  apiConfig.defaultErrorHandler(err, req, res, next);
});

const port = process.env.PORT || 3000;

app.listen(port, () =>
  console.log(`Server Up and running | zupdeco.org ⚡️🚀 | ${port}`)
);
