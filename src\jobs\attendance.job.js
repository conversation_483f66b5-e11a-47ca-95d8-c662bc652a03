const StudentPreferencesModel = require("../models/Student.Preferences.Model.js");
const TutorPreferencesModel = require("../models/Tutor.Preferences.Model.js");
const crReportsModel = require("../models/Cr.Report.Model.js");
const cron = require('node-cron');

exports.updateStudentsAttendanceJob = async () => {
    try {
        const userIds = await StudentPreferencesModel.distinct('userId');

        if (!userIds || userIds.length === 0) {
            console.log('No userIds found');
            return;
        }

        const updatePromises = userIds.map(async (userId) => {
            const matchCriteria = {
                'students.userId': userId,
                'students.absence': false,
            };

            const hours = await crReportsModel.countDocuments(matchCriteria);
            await StudentPreferencesModel.findOneAndUpdate({ userId }, { totalSessionsHours: hours });

            return { userId, totalHours: hours };
        });
        console.log("Processing Students updatePromises ...")
        const updateResults = await Promise.all(updatePromises);

        console.log('Count of StudentPreferences updated:', updateResults.length);
        console.log("Students preferences successfully updated based on attendance")
    } catch (err) {
        console.error('Error retrieving or updating attendance:', err);
        throw err;
    }
};
exports.updateTutorsAttendanceJob = async () => {
    try {
        const userIds = await TutorPreferencesModel.distinct('userId');

        if (!userIds || userIds.length === 0) {
            console.log('No userIds found');
            return;
        }

        const updatePromises = userIds.map(async (userId) => {
            const matchCriteria = {
                'tutors.userId': userId,
                'tutors.absence': false,
            };

            const hours = await crReportsModel.countDocuments(matchCriteria);
            await TutorPreferencesModel.findOneAndUpdate({ userId }, { totalSessionsHours: hours });

            return { userId, totalHours: hours };
        });
        console.log("Processing Tutors updatePromises ...")
        const updateResults = await Promise.all(updatePromises);

        console.log('Count of TutorPreferences updated:', updateResults.length);
        console.log("Tutors preferences successfully updated based on attendance")
    } catch (err) {
        console.error('Error retrieving or updating attendance:', err);
        throw err;
    }
};

// Schedule the cron job to run at 2 AM every day
cron.schedule('0 2 * * *', () => {
    console.log('The processing Students Attendance Cron Job is setted up.');
    updateStudentsAttendanceJob();
    console.log('The processing Tutors Attendance Cron Job is setted up.');
    updateTutorsAttendanceJob();
});

console.log('Cron job scheduled to update tutors/students preferences based on attendence at 2 AM every day.');