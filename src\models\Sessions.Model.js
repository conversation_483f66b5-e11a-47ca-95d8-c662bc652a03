const mongoose = require("mongoose");
const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const sessionSchema = new mongoose.Schema(
  {
    sessionId: {
      type: String,
      required: true,
      index: {
        unique: true,
        sparse: false,
      },
    },
    parentSessionId: String,
    sessionType: String,
    program: String,
    status: String,
    sessionLink: String,
    note: String,
    tutors: [
      {
        fullName: String,
        userId: String,
        absence: {
          type: Boolean,
          default: false,
        },
        report: {
          reportId: String,
          status: String,
          reportLink: String,
          description: String,
          createdAt: Date,
        },
      },
    ],
    students: [
      {
        userId: String,
        fullName: String,
        absence: {
          type: Boolean,
          default: false,
        },
      },
    ],
    vsc: [
      {
        fullName: String,
        userId: String,
        absence: {
          type: Boolean,
          default: false,
        },
        report: {
          reportId: String,
          status: String,
          reportLink: String,
          description: String,
          createdAt: Date,
        },
      },
    ],
    school: String,
    placeOrRoom: String,
    sessionDate: {
      startDate: Date,
      endDate: Date,
      month: Number,
      dayOfTheWeek: Number,
      startHour: String, // 0 to 23
      endHour: String, // 0 to 23
    },
    // createdAt: {
    //   type: Date,
    //   default: dateTimeHelper.getCurrentDateTimeInParisZone(),
    // },
    // lastUpdatedAt: {
    //   type: Date,
    //   default: dateTimeHelper.getCurrentDateTimeInParisZone(),
    // },
    report: {
      reportId: String,
      status: String,
      reportLink: String,
      description: String,
      createdAt: Date,
    },
    cancelingDetails: {
      cancelingReason: String,
      cancelingDescription: String,
      cancelingDate: Date,
      userId: String,
      userRole: String,
      userFullName: String,
    },
    //make a object for a report from BigBlueButton webhooks
    reportFromBigBlueButton: {
      meetingID: String,
    },
    updatingFutureSessions: Boolean,
    //Weekly by default , Bi-monthly ,On time
    recurrence: {
      recurrenceNumber: Number,
      recurrenceName: String,
    },
    scheduleDuringHolidays: {
      type: Boolean,
      default: false,
    },
    meetingRoom: {
      sessionsUrl: String,
      meetingID: String,
    },
    createdBy: {
      userId: String,
      fullName: String,
      email: String,
      userRole: String,
    },
    origin: {
      type: String,
    },
    lastUpdatedBy: {
      userId: String,
      fullName: String,
      email: String,
      userRole: String,
    },
  },
  {
    timestamps: { createdAt: "createdAt", updatedAt: "lastUpdatedAt" },
  },
  {
    versionKey: false,
  }
);

module.exports = mongoose.model("sessions", sessionSchema);
