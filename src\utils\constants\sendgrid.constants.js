//All SendGrid constants are defined here
const SendGridConstants = {
  EMAIL_SEND_FROM: "L'équipe <PERSON>",
};

const EmailTemplates = {
  TEMPLATE_SEND_EMAIL_PARENT: "d-bdb1d5a85d9340ddbae9524c241ee008",
  TEMPLATE_SEND_EMAIL_TUTORS: "d-37d33d7b2d904c99aa39acfedab8b978",
  TEMPLATE_SEND_EMAIL_COORDINATOR: "d-8c16be883ac04151b6c384d2b00af1dd",
  TEMPLATE_CONTACT_ZUPDECO: "d-c06ec50a479d4b97b98e8c4db5d40bab",
  TEMPLATE_SEND_INVITATION_TO_STUDENT: "d-c89d2e5e7cb64fea90f75ebf32ebbd79",
  TEMPLATE_SEND_INVITATION_TO_TUTOR: "d-197bb7eb55a342ceaf647d44361ad044",
  TEMPLATE_SESSION_NOTIFICATION_ON_CREATE: "d-821cedc3755d4145ad7dc4f0582bdc12",
  TEMPLATE_SEND_USER_INVITATION: "d-472740df93924648926ffd36961a142a",
  TEMPLATE_SEND_SESSION_0_INVITATION: "d-9736758f5a1e4616b33c1b2b147b0b44",
  TEMPLATE_SEND_SESSION_0_CANCELATION: "d-1eeabdf04bfe43648f35197fe97e5149",
  TEMPLATE_TUTOR_EMAIL_NEW_STATUT: "d-b69fb73362a84ceca7affdff8b57ceee",
  TEMPLATE_TUTOR_EMAIL_COMPLETE_STATUT: "d-81b3cd1e3b3a4a5f817019ea551b4ee1",
  TEMPLATE_TUTOR_EMAIL_AWAITING_STUDENT_1_STATUT:
    "d-b611a18d184746f0a6d10500aeb862bf",
  TEMPLATE_TUTOR_EMAIL_STUDENT_1_FOUND_STATUT:
    "d-e3f6bdf93eb1482280e0215665afc484",
  TEMPLATE_TUTOR_REMINDER_INCOMPLET_1: "d-264dd752b96742bfa4f354c92453fa8f",
  TEMPLATE_TUTOR_REMINDER_INCOMPLET_2: "d-bccfb953bd4e4e4ea6736eacc12cc2d8",
  TEMPLATE_PARENT_WELCOMING: "d-1f7b5a456429417b8461271797df4e7f",
  TEMPLATE_SESSION_REPORT: "d-10dcf8c24c4b4e06bdbda9855491ad52",
  TEMPLATE_END_OF_SESSION_REPORT: "d-179d35666c574b1ead48f8454ff43a44",
  TEMPLATE_ACTIVE_PARENTS_INVITATION: "d-5b5779f0973941d6939afa2238a0c86b",
  TEMPLATE_ON_HOLD_PARENTS_INVITATION: "d-2a0eb369b69a45a6b074759509df9fd6",
  TEMPLATE_ON_HOLD_TUTORS_INVITATION: "d-810483d5d426468eb87bdb40441471dd",
  TEMPLATE_ACTIVE_TUTORS_INVITATION: "d-b4b0f3724f684461ac69f3bcfdacd10e",
  TEMPLATE_NEW_SESSION_FROM_MATCHING: "d-5d687d58d5ae418dbc2705c44da6dabf",
  TEMPLATE_NEW_SESSION_FROM_SESSIONS: "d-1dd27018318b4bf98358cc7b46932155",
};

const ZupDecoEmails = {
  EMAIL_CONTACT_ZUPDECO_TUTOR: "<EMAIL>",
  EMAIL_HELLO_ZUPDECO: "<EMAIL>",
  EMAIL_ZUPDECO_ORG: "<EMAIL>",
  EMAIL_TEAM_ZUPDECO: "<EMAIL>",
  EMAIL_HOME_ZUPDECO: "<EMAIL>",
};
const SESSION_TEMPLATES_MAPPINGS = {
  auto: EmailTemplates.TEMPLATE_NEW_SESSION_FROM_MATCHING,
  pro: EmailTemplates.TEMPLATE_NEW_SESSION_FROM_SESSIONS,
  mat: EmailTemplates.TEMPLATE_NEW_SESSION_FROM_MATCHING,
};
module.exports = {
  SendGridConstants,
  EmailTemplates,
  ZupDecoEmails,
  SESSION_TEMPLATES_MAPPINGS,
};
