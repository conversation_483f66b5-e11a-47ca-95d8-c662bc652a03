const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const scHelper = require("../controllers/reports/sc.helper.js");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const formidable = require("formidable");

const reportConstants = require("../utils/constants/reports.constants.js");

const { checkIfAuthenticated, checkIfAuthorized } = require("../middleware/apiAuth/verifyBearer.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");
const { SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC } = require("../utils/constants/userRolesConstants.js");

//create a new SC report
/**
 * @swagger
 * /api/v1/scReport:
 *   post:
 *     summary: Create a new SC report
 *     tags: [SC Reports]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               scReport:
 *                 type: string
 *                 description: The SC report data in JSON format
 *     responses:
 *       '200':
 *         description: Successfully created a new SC report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: object
 *                   description: The created SC report
 *       '400':
 *         description: Bad request, missing or invalid SC report data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.post("/scReport", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER])], async (req, res, next) => {
  try {
    const form = formidable({ multiples: true });

    form.parse(req, async (err, fields, files) => {
      try {
        let scReport = fields.scReport;

        //check if the scReport not empty or null
        if (!scReport) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `SC report is required. Please provide a valid SC report.`
          );
          return res.status(400).json(response);
        }

        //parse scReport
        scReport = JSON.parse(scReport);

        //save new SC report in database , use scHelper.createScReport(scReport) to save new SC report in database
        const newSCReport = await scHelper.createScReport(scReport);

        if (!newSCReport.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Failed to create a new SC report`,
            newSCReport.data
          );
          return res.status(400).json(response);
        }

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `New SC report created successfully`,
          newSCReport.data
        );
        return res.status(200).json(response);
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to create a new SC report`,
          error
        );
        return res.status(500).json(response);
      }
    });
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to create a new SC report`,
      error
    );
    return res.status(500).json(response);
  }
});

//update SC report
/**
 * @swagger
 * /api/v1/scReport:
 *   put:
 *     summary: Update an existing SC report
 *     tags: [SC Reports]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               scReport:
 *                 type: string
 *                 description: The updated SC report data in JSON format
 *     responses:
 *       '200':
 *         description: Successfully updated the SC report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: object
 *                   description: The updated SC report
 *       '400':
 *         description: Bad request, missing or invalid SC report data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.put("/scReport", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER])], async (req, res, next) => {
  try {
    const form = formidable({ multiples: true });

    form.parse(req, async (err, fields, files) => {
      try {
        let scReport = fields.scReport;

        //check if the scReport not empty or null
        if (!scReport) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `SC report is required. Please provide a valid SC report.`
          );
          return res.status(400).json(response);
        }

        //parse scReport
        scReport = JSON.parse(scReport);

        //update SC report in database , use scHelper.updateScReport(scReport) to update SC report in database
        const updatedSCReport = await scHelper.updateScReport(scReport);

        if (!updatedSCReport.status) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `Failed to update SC report`,
            updatedSCReport.data
          );
          return res.status(400).json(response);
        }

        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `SC report updated successfully`,
          updatedSCReport.data
        );
        return res.status(200).json(response);
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to update SC report`,
          error
        );
        return res.status(500).json(response);
      }
    });
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to update SC report`,
      error
    );
    return res.status(500).json(response);
  }
});

//get SC report by _id
/**
 * @swagger
 * /api/v1/scReport:
 *   get:
 *     summary: Get an SC report by ID
 *     tags: [SC Reports]
 *     parameters:
 *       - in: query
 *         name: scReportId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the SC report to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved the SC report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *                 data:
 *                   type: object
 *                   description: The SC report data
 *       '400':
 *         description: Bad request, missing or invalid SC report ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/scReport", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])], async (req, res, next) => {
  try {
    const scReportId = req.query.scReportId;

    //check if scReportId is provided
    if (!scReportId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `SC report id is required. Please provide a valid SC report id.`,
        `Please provide a valid SC report id.`
      );
      return res.status(400).json(response);
    }

    //get SC report by _id
    const scReport = await scHelper.getScReportById(scReportId);

    if (!scReport.status) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Failed to get SC report`,
        scReport.data
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `SC report found`,
      scReport.data
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get SC report`,
      error
    );
    return res.status(500).json(response);
  }
});

//delete SC report by _id
/**
 * @swagger
 * /api/v1/scReport:
 *   delete:
 *     summary: Delete an SC report by ID
 *     tags: [SC Reports]
 *     parameters:
 *       - in: query
 *         name: scReportId
 *         required: true
 *         schema:
 *           type: string
 *         description: The ID of the SC report to delete
 *     responses:
 *       '200':
 *         description: Successfully deleted the SC report
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: A success message
 *       '400':
 *         description: Bad request, missing or invalid SC report ID
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.delete("/scReport", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER])], async (req, res, next) => {
  try {
    const scReportId = req.query.scReportId;

    //check if scReportId is provided
    if (!scReportId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `SC report id is required. Please provide a valid SC report id.`,
        `Please provide a valid SC report id.`
      );
      return res.status(400).json(response);
    }

    //delete SC report by _id
    const deletedSCReport = await scHelper.deleteScReportById(scReportId);

    if (!deletedSCReport.status) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Failed to delete SC report`,
        deletedSCReport.data
      );
      return res.status(400).json(response);
    }

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `SC report deleted successfully`,
      `The SC report has been deleted successfully`
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to delete SC report`,
      error
    );
    return res.status(500).json(response);
  }
});

//Sc Dashboard
/**
 * @swagger
 * /api/v1/scReport/dashboard:
 *   get:
 *     summary: Get SC report dashboard
 *     tags: [SC Reports]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: 'The page number for pagination (default: 1)'
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *         description: 'The number of items per page for pagination (default: 10)'
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Optional filter parameter
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Optional sort parameter
 *     responses:
 *       '200':
 *         description: Successfully retrieved the SC report dashboard
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: List of SC reports
 *                   items:
 *                     type: object
 *                     properties:
 *                       creation_date:
 *                         type: string
 *                         format: date-time
 *                         description: The creation date of the SC report
 *                       entry_date:
 *                         type: string
 *                         format: date-time
 *                         description: The entry date of the SC report
 *                       status:
 *                         type: string
 *                         description: The status of the SC report
 *                       postponed_hours:
 *                         type: number
 *                         description: The postponed hours of the SC report
 *                       vsc:
 *                         type: object
 *                         properties:
 *                           vscFullName:
 *                             type: string
 *                             description: The full name of the VSC
 *                           userId:
 *                             type: string
 *                             description: The ID of the VSC user
 *                       coordinator:
 *                         type: object
 *                         properties:
 *                           coordinatorFullName:
 *                             type: string
 *                             description: The full name of the coordinator
 *                           userId:
 *                             type: string
 *                             description: The ID of the coordinator user
 *                       proof_delivered:
 *                         type: boolean
 *                         description: Indicates whether proof is delivered
 *                       appreciation:
 *                         type: string
 *                         description: The appreciation of the SC report
 *                       daysContent:
 *                         type: string
 *                         description: The content of the days
 *                 pagination:
 *                   type: object
 *                   properties:
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalCount:
 *                       type: integer
 *                       example: 100
 *       '400':
 *         description: Bad request or failed to get SC report dashboard
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 *       '500':
 *         description: Internal server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Error message
 */
router.get("/scReport/dashboard", [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR, MANAGER])], async (req, res, next) => {
  try {
    let page = apiResponse.buildPage(req.query.page);
    let pageSize = apiResponse.buildPageSize(req.query.pageSize);

    let filter = req.query.filter;
    let sortBy = req.query.sortBy;

    const userId = req.userId;
    const userRole = req.userRole;
    //get SC report dashboard
    await scHelper.generateWeeklyScReports();
    const scReportDashboard = await scHelper.dashboard({
      page,
      pageSize,
      filter,
      sortBy,
      userId,
      userRole
    });

    if (!scReportDashboard.status) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Failed to get SC report dashboard`,
        scReportDashboard.data
      );
      return res.status(400).json(response);
    }
    let response = apiResponse.responseWithPagination(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `SC report dashboard`,
      scReportDashboard.data,
      page,
      pageSize,
      scReportDashboard.total
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to get SC report dashboard`,
      error
    );
    return res.status(500).json(response);
  }
});

//export router
module.exports = router;
