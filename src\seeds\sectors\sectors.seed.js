const fs = require('fs');
const path = require('path');
const SectorsModel = require('../../models/Sectors.Model');

const seedDBSectors = async () => {
    try {
        // Read JSON data from the file
        const data = JSON.parse(fs.readFileSync(path.join(__dirname, 'sectors.data.json'), 'utf8'));

        for (let item of data) {
            const exists = await SectorsModel.exists({ _id: item._id });
            if (!exists) {
                await SectorsModel.create(item);
                console.log(`Added sector: ${item.name}`);
            } else {
                // console.log(`Sector already exists: ${item.name}`);
            }
        }

        console.log('Database seeding completed');
    } catch (err) {
        console.error('Error seeding database:', err);
    }
};

module.exports = seedDBSectors;
