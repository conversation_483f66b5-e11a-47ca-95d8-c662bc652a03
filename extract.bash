#!/bin/bash

protocol="http"
host="localhost"
port="3000"
prefix="api"
version="v1"
content_type="application/json"

# Function to extract routes from a given file
extract_routes() {
    local file="$1"
    # Use grep to extract route paths and methods (e.g., router.get('/path', ...) etc.)
    grep -oP "router\.(get|post|put|delete)\s*\([^)]+\)\s*=>" "$file" | while read -r line; do
        method=$(echo "$line" | awk -F'[ .]' '{print $2}')  # Extract method
        echo "$method"
    done | sed -e 's/[()]//g'
}

# Function to extract specific parts of the string
extract_parts() {
    local string="$1"
    method=$(echo "$string" | awk -F'"' '{print $1}')  # Extract method
    path=$(echo "$string" | awk -F'"' '{print $2}')  # Extract specific part
    name=$(echo "$path" | sed 's|^/||')
    env="$protocol://$host:$port/$prefix/$version"
    echo '{ "name": "'"$path"'", "request": { "method": "'"$method"'", "url": { "raw": "'"$env$path"'","protocol": "'$protocol'","host": ["'$host'"],"port": "'$port'","path": ["'$prefix'","'$version'","'$name'"]} , "header": [ { "key": "Content-Type", "value": "'"$content_type"'" } ], "body": {}, "description": "" }, "response": [] },' >> "$output_file"
}


output_file="postman_collection.json"


# Find all *.routes.js files recursively in the current directory and its subdirectories
echo '{ "info": { "name": "ZupdeCo API Collection", "description": "Collection of Express.js routes for testing in Postman", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json" }, "item": [' > "$output_file"
find . -type f -name '*.routes.js' | while read -r file; do
    extract_routes "$file" | while read -r line; do
        extract_parts "$line"
    done
done >> $output_file
sed -i '$ s/,$//' "$output_file"
echo ']}' >> "$output_file"



