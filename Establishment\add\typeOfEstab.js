function getTypeOfEstablishmentIdAndName(typeName) {
    let typeDetails = { id: null, name: "" };
    const targetType  = 'high-school'.toLowerCase().toLowerCase().normalize('NFC');
    // const targetType  = 'company'.toLowerCase().toLowerCase().normalize('NFC');
    switch (typeName.trim().toLowerCase().normalize('NFC')) {
      case targetType:
        typeDetails = { id: "3", name: "high-school" };
        // typeDetails = { id: "9", name: "company" };
        break;
      default:
          typeDetails = { id: "15", name: "Autre établissement" };
          break;
    }
  
  
    return typeDetails;
  }
  
  module.exports = getTypeOfEstablishmentIdAndName;