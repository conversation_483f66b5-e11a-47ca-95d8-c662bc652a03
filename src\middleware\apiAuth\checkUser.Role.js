//import user model
const User = require("../../models/User.Models");

//import userHelper
const userHelper = require("../../controllers/user/User.Helper.js");

const userAdministrationHelper = require("../../controllers/user/user.administration.helper.js");

const apiLogs = require("../../models/ApiLog.Model.js");

const apiLoggerS = require("../../services/logger/LoggerService.js");

const logger = new apiLoggerS("check User.Role .js");

const apiResponse = require("../../controllers/apiresponse/ApiResponseHelper.js");

//import userRoleConstants.js from path src/utils/constants/userRoleConstants.js
const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const userRoleModel = require("../../models/UserRoles.Model.js");

const userRoleHelper = require("../../controllers/user/userRole.Helper.js");

const userRoleAccessLevelConstants = require("../../utils/constants/userRoleAccessLevel.constants.js");

const userRoleStatusConstants = require("../../utils/constants/user.role.status.constants.js");

const tutorConstants = require("../../utils/constants/tutor.constants.js");

//check user role and access level in the database
exports.checkUserRoleAndAccessLevel = async (req, res, next, accessLevel) => {
  try {
    next();
    return;
    const userId = req.header("userId");

    //check if user id is provided
    if (!userId) {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User id is required to perform this action. Please provide a valid userId.`
      );
      return res.status(400).json(response);
    }

    //get user details from database
    const userDocument = await userHelper.getUserDetailsByUserId(userId);

    //check if user exists
    if (!userDocument) {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User does not exist. Please provide a valid userId.`
      );
      return res.status(400).json(response);
    }

    //get user role from user document
    const userRole = userDocument.userRole;

    //get user role details from database
    const userRoleDocument = await userRoleModel.findOne({
      userRole: userRole,
    });

    //check if user role exists
    if (!userRoleDocument) {
      let response = apiResponse.responseWithStatusCode(
        false,
        `User role does not exist. Please provide a valid user role.`
      );
      return res.status(400).json(response);
    }
    //get accesses list from user role document
    const accessesList = userRoleDocument.accessList;

    const apiMethod = req.method;

    let accessLevelExists = false;
    accessesList.some((access) => {
      if (access.name === accessLevel) {
        const baseOnApiMethodAndAccessPrivilege =
          checkAccessLevelBaseOnApiMethod(apiMethod, access.accessPrivilege);
        if (baseOnApiMethodAndAccessPrivilege) {
          accessLevelExists = true;
          return accessLevelExists;
        }
      }
    });

    if (accessLevelExists) {
      next();
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `This user does not have access to this resource. User Id: ${userId}`,
        `User role: ${userRole} does not have access to this resource. Access Level: ${accessLevel}`,
        401
      );
      return res.status(401).json(response);
    }
  } catch (error) {
    console.log("error", error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `This user does not have access to this resource. User Id: ${userId}`,
      `User role: ${userRole} does not have access to this resource. Access Level: ${accessLevel}`,
      401
    );
    return res.status(401).json(response);
  }
};

//base on accessLevel and api method check if user has access to the api. If level is R or RW or N
function checkAccessLevelBaseOnApiMethod(apiMethod, accessPrivilege) {
  //convert to uppercase apiMethod
  apiMethod = apiMethod.toUpperCase();

  //if accessPrivilege is R is only read access and get access to GET method only, if accessPrivilege is RW is read and write access and get access to GET, POST, PUT, DELETE method, if accessPrivilege is N is no access and get access to no method
  if (
    apiMethod === "GET" &&
    accessPrivilege === userRoleAccessLevelConstants.READ
  ) {
    return true;
  } else if (
    apiMethod === "GET" &&
    accessPrivilege === userRoleAccessLevelConstants.READ_AND_WRITE
  ) {
    return true;
  } else if (
    apiMethod === "POST" &&
    accessPrivilege === userRoleAccessLevelConstants.READ_AND_WRITE
  ) {
    return true;
  } else if (
    apiMethod === "PUT" &&
    accessPrivilege === userRoleAccessLevelConstants.READ_AND_WRITE
  ) {
    return true;
  } else if (
    apiMethod === "DELETE" &&
    accessPrivilege === userRoleAccessLevelConstants.READ_AND_WRITE
  ) {
    return true;
  } else if (accessPrivilege === userRoleAccessLevelConstants.NO_ACCESS) {
    return false;
  } else {
    return false;
  }
}

//check userRole just for Internal System signIn
exports.checkUserRoleInAdminSignIn = async (req, res, next) => {
  const email = req.header("email");
  console.log("email", email);
  //check if user id is provided
  if (!email) {
    let response = apiResponse.responseWithStatusCode(
      false,
      `Email is required to perform this action. Please provide a valid email.`
    );
    return res.status(400).json(response);
  }

  //get user details from database
  const userDocument =
    await userAdministrationHelper.getUserDetailsFromFirebaseIdentifier(email);
  console.log(
    "User document is found in the method checkUserRoleInAdminSignIn",
    userDocument?.userId
  );

  //check if user exists
  if (!userDocument) {
    let response = apiResponse.responseWithStatusCode(
      false,
      `User does not exist. Please provide a valid userId.`
    );
    return res.status(400).json(response);
  }

  //get user role from user document
  const userRole = userDocument.userRole;
  console.log("userRole", userRole);
  console.log("user status", userDocument.status);

  //FIRST CHECK IF USER STATUS IS ACTIVE
  if (userDocument.status !== userRoleStatusConstants.userAdminStatus.ACTIVE) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `This user is not active. Please contact admin to activate your account.`,
      `User with email: ${email} is not active. Their current role is ${userRole}`,
      apiResponse.apiConstants.USER_IS_NOT_ACTIVE
    );
    return res.status(401).json(response);
  }

  const userRoleList = await userRoleHelper.getAllUserRoles();

  let userRoleHasPermission = false;
  let isUserIsNotActive = false;

  userRoleList.some((role) => {
    console.log("role", role);
    if (role === userRole) {
      userRoleHasPermission = true;
      if (
        userDocument.status !== userRoleStatusConstants.userAdminStatus.ACTIVE
      ) {
        isUserIsNotActive = true;
      }
    }
  });

  if (userRoleHasPermission) {
    if (isUserIsNotActive) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `This user is not active. Please contact admin to activate your account.`,
        `User with email: ${email} is not active. Their current role is ${userRole}`,
        401
      );
      return res.status(401).json(response);
    }
    next();
  } else {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `This user does not have access to this resource.`,
      `User with email: ${email} does not have sufficient user role for accessing this resource. Their current role is ${userRole}`,
      401
    );
    return res.status(401).json(response);
  }
};

//Check if user is admin role
exports.checkAdminRole = async (req, res, next) => {
  const userId = req.header("userId");
  try {
    //check if user id is provided
    if (!userId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `UserId is required to perform this action. Please provide a valid userId.`
      );
      return res.status(400).json(response);
    }

    //get userDetails from userHelper
    const userDetails = await userHelper.getUserDetailsByUserId(userId);

    //check if user is admin
    if (
      userDetails &&
      userDetails.userRole === userRoleConstants.SUPER_ADMINISTRATOR
    ) {
      next();
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `This user is not an admin, and cannot perform this action. User Id: ${userId}`,
        "",
        401
      );
      logger
        .newLog(logger.getLoggerTypeConstants().checkUserRole)
        .info(
          `This user is not an admin, and cannot perform this action. User Id: ${userId}`
        );
      return res.status(401).json(response);
    }
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to check if user is admin. User Id: ${userId}`,
      JSON.stringify(error)
    );
    logger
      .newLog(logger.getLoggerTypeConstants().checkUserRole)
      .error(response);
    return res.status(500).json(response);
  }
};

exports.checkAdminUserRole = async (req, res, next) => {
  const userId = req.header("userId");
  try {
    //check if user id is provided
    if (!userId) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `UserId is required to perform this action. Please provide a valid userId.`
      );
      return res.status(400).json(response);
    }

    //get userDetails from userHelper
    const userDetails = await userHelper.getUserDetailsByUserId(userId);

    //check if user is admin
    if (
      userDetails &&
      userDetails.userRole === userRoleConstants.SUPER_ADMINISTRATOR
    ) {
      next();
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `This userId ${userId} is not an admin, and cannot perform this action. Please provide a valid admin userId.`,
        "",
        401
      );
      logger
        .newLog(logger.getLoggerTypeConstants().checkUserRole)
        .info(
          `This user is not an admin, and cannot perform this action. User Id: ${userId}`
        );
      return res.status(401).json(response);
    }
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      `Failed to check if user is admin. User Id: ${userId}`,
      JSON.stringify(error)
    );
    logger
      .newLog(logger.getLoggerTypeConstants().checkUserRole)
      .error(response);
    return res.status(500).json(response);
  }
};
