const userRoleConstants = require("../../utils/constants/userRolesConstants.js");
const TutorPreferencesModel = require("../../models/Tutor.Preferences.Model.js");
const StudentPreferencesModel = require("../../models/Student.Preferences.Model.js");
const SessionsModel = require("../../models/Sessions.Model.js");
const crReportsModel = require("../../models/Cr.Report.Model.js");
const UserAdministrationModel = require("../../models/User.Administration.Model.js")
/**
 * Vérifie si l'utilisateur est un coordinateur et valide la période
 * @param {Object} payload - Les données de la requête
 * @param {string} payload.userRole - Le rôle de l'utilisateur
 * @param {string} payload.period - La période demandée
 * @returns {Object|null} - Objet d'erreur ou null si validé
 */
exports.validateCoordinatorAccess = (payload) => {
  const { userRole, period } = payload;
  
  // Vérifier si l'utilisateur est un coordinateur
  if (userRole !== userRoleConstants.COORDINATOR) {
    return {
      status: false,
      message: "Cette fonctionnalité est réservée aux coordinateurs.",
      data: null,
    };
  }
  
  // Vérifier si la période est valide (si fournie)
  if (period && !['perWeek', 'perMonth', 'perTrimester', 'perYear'].includes(period)) {
    return {
      status: false,
      message: "La période doit être 'perWeek', 'perMonth', 'perTrimester' ou 'perYear'.",
      data: null,
    };
  }
  
  return null; // Validation réussie
};

/**
 * Récupère les IDs des tuteurs en fonction du filtre de programme
 * @param {string} program - ID du programme pour filtrer
 * @returns {Array} - Liste des IDs de tuteurs
 */
exports.getTutorIdsWithFilter = async (program, userId) => {
  const programFilter = program ? { "program.programId": program } : {};
   const userAdminDetails = await UserAdministrationModel.findOne({userId :  userId });

    // get the program preferences of the user
    const pref = userAdminDetails?.administrationPreferences;
    console.log("userIduserId :",userId, userAdminDetails)
    const departmentIds = pref.department.map((dep) => dep.departmentId);
    // Step 2
    let preference = {}
    // check if the user has departementIds
    if (!departmentIds?.length) {
      // in case of coordinator/vsc has no departementIds in his admin preferences model
      preference["_id"] = null;
    } else {
      // in case of coordinator/vsc has adress zipcode in his admin preferences model
      preference["assignment.department.departmentId"] = {
        $in: departmentIds,
      };
      preference["program.programId"] = program;
    }
  // Récupérer les IDs des tuteurs
  const tutorIds = await StudentPreferencesModel.find(preference)
    .select("userId")
    .lean()
    .then(tutors => tutors.map(tutor => tutor.userId));
    
  return tutorIds;
};

/**
 * Calcule le pourcentage d'évolution entre deux valeurs
 * @param {number} currentValue - Valeur actuelle
 * @param {number} previousValue - Valeur précédente
 * @param {boolean} [preventNegative=true] - Empêcher les valeurs négatives
 * @returns {number} - Pourcentage d'évolution
 */
exports.calculateEvolutionPercentage = (currentValue, previousValue, preventNegative = true) => {
  let evolution = 0;
  
  if (previousValue > 0) {
    evolution = Math.round(((currentValue - previousValue) / previousValue) * 100);
    if (preventNegative && evolution < 0) {
      evolution = 0;
    }
  } else if (currentValue > 0) {
    evolution = 100; // Si la valeur précédente était 0 et qu'on a maintenant une valeur
  }
  
  return evolution;
};

/**
 * Calcule les dates de début et fin pour une période donnée
 * @param {string} period - Type de période ('perWeek', 'perMonth', 'perTrimester', 'perYear')
 * @param {string} [timeslot] - Période spécifique au format approprié
 * @returns {Object} - Dates de début et fin pour les périodes courante et précédente
 */
exports.getPeriodDateRanges = (period, timeslot) => {
  const currentDate = new Date();
  let currentPeriodStart, currentPeriodEnd, previousPeriodStart, previousPeriodEnd;
  let periodLabel;
  let error = null;
  
  if (period === "perWeek") {
    if (timeslot) {
      // Format attendu: 'DD/MM/YYYY, DD/MM/YYYY'
      if (timeslot.includes(",")) {
        const dates = timeslot.split(",").map((d) => d.trim());
        if (dates.length !== 2) {
          return {
            isValid: false,
            error: {
              status: false,
              message: "Format de période invalide. Attendu: 'DD/MM/YYYY, DD/MM/YYYY'",
              data: null,
            }
          };
        }

        const [startDay, startMonth, startYear] = dates[0]
          .split("/")
          .map((n) => parseInt(n, 10));
        const [endDay, endMonth, endYear] = dates[1]
          .split("/")
          .map((n) => parseInt(n, 10));

        // Vérifier que les valeurs sont valides
        if (
          isNaN(startDay) ||
          isNaN(startMonth) ||
          isNaN(startYear) ||
          isNaN(endDay) ||
          isNaN(endMonth) ||
          isNaN(endYear) ||
          startDay < 1 ||
          startDay > 31 ||
          startMonth < 1 ||
          startMonth > 12 ||
          endMonth < 1 ||
          endMonth > 12 ||
          endDay < 1 ||
          endDay > 31
        ) {
          return {
            isValid: false,
            error: {
              status: false,
              message: "Format de période invalide. Le jour doit être entre 1 et 31, le mois doit être entre 1 et 12 et l'année doit être valide.",
              data: null,
            }
          };
        }

        currentPeriodStart = new Date(startYear, startMonth - 1, startDay, 0, 0, 0);
        currentPeriodEnd = new Date(endYear, endMonth - 1, endDay, 23, 59, 59);
        
        // Calculer la période précédente (même durée)
        const daysDiff = Math.round(
          (currentPeriodEnd - currentPeriodStart) / (24 * 60 * 60 * 1000)
        );
        previousPeriodStart = new Date(currentPeriodStart);
        previousPeriodStart.setDate(previousPeriodStart.getDate() - daysDiff - 1);
        previousPeriodEnd = new Date(currentPeriodStart);
        previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);
      } else {
        return {
          isValid: false,
          error: {
            status: false,
            message: "Format de période invalide. Attendu: 'DD/MM/YYYY, DD/MM/YYYY'",
            data: null,
          }
        };
      }
    } else {
      // Trouver le lundi de la semaine courante
      const day = currentDate.getDay();
      const diff = currentDate.getDate() - day + (day === 0 ? -6 : 1); // Ajustement pour dimanche
      currentPeriodStart = new Date(currentDate);
      currentPeriodStart.setDate(diff);
      currentPeriodStart.setHours(0, 0, 0, 0);

      // Fin de la semaine (dimanche)
      currentPeriodEnd = new Date(currentPeriodStart);
      currentPeriodEnd.setDate(currentPeriodStart.getDate() + 6);
      currentPeriodEnd.setHours(23, 59, 59, 999);
      
      // Semaine précédente
      previousPeriodStart = new Date(currentPeriodStart);
      previousPeriodStart.setDate(currentPeriodStart.getDate() - 7);
      
      previousPeriodEnd = new Date(currentPeriodEnd);
      previousPeriodEnd.setDate(currentPeriodEnd.getDate() - 7);
    }
    
    periodLabel = "Semaine";
  } 
  else if (period === "perMonth") {
    let targetMonth, targetYear, endMonth, endYear;
    
    if (timeslot) {
      if (timeslot.includes(",")) {
        // Format 'MM/YYYY, MM/YYYY'
        const dates = timeslot.split(",").map((d) => d.trim());
        if (dates.length !== 2) {
          return {
            isValid: false,
            error: {
              status: false,
              message: "Format de période invalide. Attendu: 'MM/YYYY, MM/YYYY' ou 'MM/YYYY'",
              data: null,
            }
          };
        }

        [targetMonth, targetYear] = dates[0].split("/").map((n) => parseInt(n, 10));
        [endMonth, endYear] = dates[1].split("/").map((n) => parseInt(n, 10));
      } else {
        // Format 'MM/YYYY' (un seul mois)
        [targetMonth, targetYear] = timeslot.split("/").map((n) => parseInt(n, 10));
        endMonth = targetMonth;
        endYear = targetYear;
      }

      if (
        isNaN(targetMonth) ||
        isNaN(targetYear) ||
        isNaN(endMonth) ||
        isNaN(endYear) ||
        targetMonth < 1 ||
        targetMonth > 12 ||
        endMonth < 1 ||
        endMonth > 12
      ) {
        return {
          isValid: false,
          error: {
            status: false,
            message: "Format de période invalide. Le mois doit être entre 1 et 12.",
            data: null,
          }
        };
      }

      // Initialiser les périodes courante et précédente
      currentPeriodStart = new Date(targetYear, targetMonth - 1, 1, 0, 0, 0);
      currentPeriodEnd = new Date(endYear, endMonth, 0, 23, 59, 59); // Dernier jour du mois

      // Calculer le mois précédent (même durée)
      const monthsDiff = (endYear - targetYear) * 12 + (endMonth - targetMonth) + 1;

      previousPeriodEnd = new Date(currentPeriodStart);
      previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);

      previousPeriodStart = new Date(previousPeriodEnd);
      previousPeriodStart.setMonth(previousPeriodStart.getMonth() - monthsDiff + 1);
      previousPeriodStart.setDate(1);
      previousPeriodStart.setHours(0, 0, 0, 0);
    } else {
      // Mois courant
      targetMonth = currentDate.getMonth() + 1; // +1 car getMonth() retourne 0-11
      targetYear = currentDate.getFullYear();
      
      currentPeriodStart = new Date(targetYear, targetMonth - 1, 1, 0, 0, 0);
      currentPeriodEnd = new Date(targetYear, targetMonth, 0, 23, 59, 59);
      
      // Mois précédent
      previousPeriodStart = new Date(targetYear, targetMonth - 2, 1, 0, 0, 0);
      if (targetMonth === 1) {
        // Si janvier, le mois précédent est décembre de l'année précédente
        previousPeriodStart = new Date(targetYear - 1, 11, 1, 0, 0, 0);
      }
      
      previousPeriodEnd = new Date(targetYear, targetMonth - 1, 0, 23, 59, 59);
    }
    
    periodLabel = "Mois";
  }
  else if (period === "perTrimester") {
    if (timeslot) {
      // Format attendu: 'MM/YYYY, MM/YYYY' pour un trimestre personnalisé
      const dates = timeslot.split(",").map((d) => d.trim());
      if (dates.length !== 2) {
        return {
          isValid: false,
          error: {
            status: false,
            message: "Format de période invalide pour le trimestre. Attendu: 'MM/YYYY, MM/YYYY'",
            data: null,
          }
        };
      }

      // Parser les dates
      const [startMonth, startYear] = dates[0].split("/").map((n) => parseInt(n, 10));
      const [endMonth, endYear] = dates[1].split("/").map((n) => parseInt(n, 10));

      if (
        isNaN(startMonth) ||
        isNaN(startYear) ||
        isNaN(endMonth) ||
        isNaN(endYear) ||
        startMonth < 1 ||
        startMonth > 12 ||
        endMonth < 1 ||
        endMonth > 12
      ) {
        return {
          isValid: false,
          error: {
            status: false,
            message: "Format de période invalide. Les mois doivent être entre 1 et 12.",
            data: null,
          }
        };
      }

      // Définir les périodes selon le timeslot
      currentPeriodStart = new Date(startYear, startMonth - 1, 1, 0, 0, 0);
      currentPeriodEnd = new Date(endYear, endMonth, 0, 23, 59, 59); // Dernier jour du mois de fin

      // Calculer la période précédente avec la même durée
      const monthsDiff = (endYear - startYear) * 12 + (endMonth - startMonth) + 1;

      previousPeriodEnd = new Date(currentPeriodStart);
      previousPeriodEnd.setDate(previousPeriodEnd.getDate() - 1);

      previousPeriodStart = new Date(previousPeriodEnd);
      previousPeriodStart.setMonth(previousPeriodStart.getMonth() - monthsDiff + 1);
      previousPeriodStart.setDate(1);
      previousPeriodStart.setHours(0, 0, 0, 0);
    } else {
      // Déterminer le trimestre courant
      const currentMonth = currentDate.getMonth();
      const currentQuarter = Math.floor(currentMonth / 3);

      // Début du trimestre courant
      currentPeriodStart = new Date(currentDate.getFullYear(), currentQuarter * 3, 1);

      // Fin du trimestre courant
      currentPeriodEnd = new Date(currentDate.getFullYear(), (currentQuarter + 1) * 3, 0);
      currentPeriodEnd.setHours(23, 59, 59, 999);

      // Début du trimestre précédent
      previousPeriodStart = new Date(currentDate.getFullYear(), (currentQuarter - 1) * 3, 1);
      if (currentQuarter === 0) {
        previousPeriodStart.setFullYear(previousPeriodStart.getFullYear() - 1);
        previousPeriodStart.setMonth(9); // Octobre de l'année précédente
      }

      // Fin du trimestre précédent
      previousPeriodEnd = new Date(currentDate.getFullYear(), currentQuarter * 3, 0);
      previousPeriodEnd.setHours(23, 59, 59, 999);
      if (currentQuarter === 0) {
        previousPeriodEnd.setFullYear(previousPeriodEnd.getFullYear() - 1);
        previousPeriodEnd.setMonth(11); // Décembre de l'année précédente
        previousPeriodEnd.setDate(31);
      }
    }

    periodLabel = "Trimestre";
  }
  else if (period === "perYear") {
    let startYear, endYear;

    if (timeslot) {
      if (timeslot.includes(",")) {
        // Format 'MM/YYYY, MM/YYYY' pour une année personnalisée
        const dates = timeslot.split(",").map((d) => d.trim());
        if (dates.length !== 2) {
          return {
            isValid: false,
            error: {
              status: false,
              message: "Format de période invalide pour l'année. Attendu: 'MM/YYYY, MM/YYYY' ou 'YYYY' ou 'YYYY, YYYY'",
              data: null,
            }
          };
        }

        // Vérifier si c'est au format MM/YYYY ou YYYY
        if (dates[0].includes("/")) {
          // Format MM/YYYY
          const [startMonth, startYearStr] = dates[0].split("/").map((n) => n.trim());
          const [endMonth, endYearStr] = dates[1].split("/").map((n) => n.trim());
          
          startYear = parseInt(startYearStr, 10);
          endYear = parseInt(endYearStr, 10);
          
          if (isNaN(startYear) || isNaN(endYear)) {
            return {
              isValid: false,
              error: {
                status: false,
                message: "Format d'année invalide.",
                data: null,
              }
            };
          }
          
          // Définir les périodes selon le timeslot
          const month1 = parseInt(startMonth, 10);
          const month2 = parseInt(endMonth, 10);
          if (isNaN(month1) || isNaN(month2) || month1 < 1 || month1 > 12 || month2 < 1 || month2 > 12) {
            return {
              isValid: false,
              error: {
                status: false,
                message: "Format de mois invalide. Le mois doit être entre 1 et 12.",
                data: null,
              }
            };
          }
          
          currentPeriodStart = new Date(startYear, month1 - 1, 1, 0, 0, 0);
          currentPeriodEnd = new Date(endYear, month2, 0, 23, 59, 59);
        } else {
          // Format YYYY, YYYY
          startYear = parseInt(dates[0], 10);
          endYear = parseInt(dates[1], 10);
          
          if (isNaN(startYear) || isNaN(endYear)) {
            return {
              isValid: false,
              error: {
                status: false,
                message: "Format d'année invalide.",
                data: null,
              }
            };
          }
          
          // Période de l'année scolaire: septembre de startYear à août de endYear
          currentPeriodStart = new Date(startYear, 8, 1, 0, 0, 0); // Septembre (0-indexé)
          currentPeriodEnd = new Date(endYear, 7, 31, 23, 59, 59); // Août (0-indexé)
        }
      } else {
        // Format 'YYYY' (une seule année scolaire)
        startYear = parseInt(timeslot.trim(), 10);
        if (isNaN(startYear)) {
          return {
            isValid: false,
            error: {
              status: false,
              message: "Format de période invalide. Attendu: 'YYYY' ou 'YYYY, YYYY'",
              data: null,
            }
          };
        }
        endYear = startYear + 1;
        
        // Période de l'année scolaire: septembre de startYear à août de endYear
        currentPeriodStart = new Date(startYear, 8, 1, 0, 0, 0); // Septembre = 8 (0-indexé)
        currentPeriodEnd = new Date(endYear, 7, 31, 23, 59, 59); // Août = 7 (0-indexé)
      }
      
      // Calculer l'année précédente (même durée)
      const yearsDiff = endYear - startYear;
      previousPeriodStart = new Date(startYear - yearsDiff, currentPeriodStart.getMonth(), currentPeriodStart.getDate());
      previousPeriodEnd = new Date(endYear - yearsDiff, currentPeriodEnd.getMonth(), currentPeriodEnd.getDate());
    } else {
      // Année scolaire courante
      const currentMonth = currentDate.getMonth();
      startYear = currentDate.getFullYear();
      
      // Si on est avant septembre, l'année scolaire a commencé l'année précédente
      if (currentMonth < 8) { // Avant septembre
        startYear = startYear - 1;
        endYear = currentDate.getFullYear();
      } else { // À partir de septembre
        endYear = startYear + 1;
      }
      
      // Début de l'année scolaire courante (1er septembre)
      currentPeriodStart = new Date(startYear, 8, 1); // Septembre = 8 (0-indexé)
      currentPeriodStart.setHours(0, 0, 0, 0);
      
      // Fin de l'année scolaire courante (31 août ou date actuelle si avant)
      if (currentDate < new Date(endYear, 7, 31)) {
        currentPeriodEnd = new Date(currentDate); // On est en cours d'année
      } else {
        currentPeriodEnd = new Date(endYear, 7, 31); // 31 août
      }
      currentPeriodEnd.setHours(23, 59, 59, 999);
      
      // Année scolaire précédente
      previousPeriodStart = new Date(startYear - 1, 8, 1); // 1er septembre année précédente
      previousPeriodStart.setHours(0, 0, 0, 0);
      
      previousPeriodEnd = new Date(startYear, 7, 31); // 31 août année courante
      previousPeriodEnd.setHours(23, 59, 59, 999);
    }
    
    periodLabel = "Année scolaire";
  }
  
  return {
    currentPeriod: { start: currentPeriodStart, end: currentPeriodEnd },
    previousPeriod: { start: previousPeriodStart, end: previousPeriodEnd },
    label: periodLabel,
    isValid: true
  };
};

/**
 * Récupère les statistiques des sessions pour une période et des tuteurs donnés
 * @param {Array} tutorIds - IDs des tuteurs
 * @param {Date} startDate - Date de début de la période
 * @param {Date} endDate - Date de fin de la période
 * @returns {Object} - Statistiques des sessions
 */
exports.getSessionsStatistics = async (tutorIds, startDate, endDate) => {
  console.log("params", tutorIds, startDate, endDate)
  const result = await SessionsModel.aggregate([
    {
      $match: {
        "students.userId": { $in: tutorIds },
        parentSessionId : {$ne : null},
        "sessionDate.startDate": {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $project: {
        sessionId: 1,
        status: 1,
        "sessionDate.startDate": 1,
        "sessionDate.endDate": 1,
        "students.userId": 1,
        _id: 0,
      },
    },
    {
      $facet: {
        // Séances planifiées (hors abandonnées)
        plannedSessions: [
          { $match: { status: { $ne: "session-0-abandoned" } } },
          { $count: "count" },
        ],
        // Séances annulées
        canceledSessions: [
          {
            $match: {
              status: { $in: ["canceled", "session-0-abandoned"] },
            },
          },
          { $count: "count" },
        ],
        // Séances passées (pour taux de réalisation)
        pastSessions: [
          { $match: { "sessionDate.endDate": { $lt: new Date() } } },
          { $count: "count" },
        ],
        // Séances passées et réalisées
        completedPastSessions: [
          {
            $match: {
              "sessionDate.endDate": { $lt: new Date() },
              status: "session-0-to-be-scheduled",
            },
          },
          { $count: "count" },
        ],
        // Collecter les IDs des séances pour compter celles avec absence
        sessionIds: [
          { $match: { status: { $ne: "session-0-abandoned" } } },
          {
            $project: {
              sessionId: 1,
              _id: 0,
            },
          },
        ],
      },
    },
  ]);
  
  return result[0];
};

/**
 * Récupère les sessions IDs pour une période donnée
 * @param {Array} studentIds - Liste des IDs des étudiants
 * @param {Date} startDate - Date de début
 * @param {Date} endDate - Date de fin
 * @returns {Array} - Liste des IDs de sessions
 */
exports.getSessionsIdsForPeriod = async (studentIds, startDate, endDate) => {
  const sessions = await SessionsModel.aggregate([
    {
      $match: {
        "students.userId": { $in: studentIds },
        status: { $ne: "session-0-abandoned" },
        "sessionDate.startDate": {
          $gte: startDate,
          $lte: endDate,
        },
      },
    },
    {
      $project: {
        sessionId: 1,
        _id: 0,
      },
    },
  ]);
  
  return sessions.map(s => s.sessionId);
};

/**
 * Traite les rapports par lots pour optimiser les performances avec de grands volumes de données
 * @param {Array} sessionIds - Liste des IDs de sessions à traiter
 * @param {Array} statusFilter - Filtres de statut pour les rapports
 * @returns {Object} - Statistiques agrégées des rapports
 */
exports.processReportsInBatches = async (sessionIds, statusFilter) => {
  // Si pas de sessions, retourner des valeurs par défaut
  if (!sessionIds || !sessionIds.length) {
    return {
      completedCount: 0,
      gradeTotal: 0,
      gradeCount: 0,
    };
  }

  // Configurer la taille des lots pour le traitement parallèle
  const BATCH_SIZE = 1000; // Ajuster selon les performances de votre système
  const batches = [];

  // Diviser les sessionIds en lots de taille BATCH_SIZE
  for (let i = 0; i < sessionIds.length; i += BATCH_SIZE) {
    batches.push(sessionIds.slice(i, i + BATCH_SIZE));
  }

  // Fonction pour traiter un lot de sessions
  const processBatch = async (batch) => {
    // Pipeline d'agrégation pour un lot
    return crReportsModel.aggregate([
      {
        $match: {
          sessionId: { $in: batch },
          status: { $in: statusFilter },
        },
      },
      // Ajout d'une projection plus tôt dans le pipeline pour minimiser les données traitées
      {
        $project: {
          sessionId: 1,
          _id: 0,
          hasValidGrade: {
            $cond: [{ $and: [{ $ne: ["$grade", null] }] }, 1, 0],
          },
          // Conserver uniquement la valeur de grade quand elle existe
          gradeValue: {
            $cond: [{ $ne: ["$grade", null] }, "$grade", 0],
          },
        },
      },
      {
        $group: {
          _id: null,
          completedCount: { $sum: 1 },
          gradesCount: { $sum: "$hasValidGrade" },
          gradesTotal: { $sum: "$gradeValue" },
        },
      },
    ]);
  };

  // Traiter tous les lots en parallèle
  const batchResults = await Promise.all(batches.map(processBatch));

  // Initialiser les compteurs pour combiner les résultats
  let totalCompletedCount = 0;
  let totalGradeSum = 0;
  let totalGradeCount = 0;

  // Combiner les résultats de tous les lots
  batchResults.forEach((results) => {
    if (results && results.length > 0) {
      const result = results[0]; // Premier élément du résultat d'agrégation

      // Ajouter les compteurs complétés si présents
      if (result.completedCount) {
        totalCompletedCount += result.completedCount;
      }

      // Ajouter les notes totales si présentes
      if (result.gradesTotal) {
        totalGradeSum += result.gradesTotal;
      }

      // Ajouter le nombre de rapports notés si présents
      if (result.gradesCount) {
        totalGradeCount += result.gradesCount;
      }
    }
  });

  // Retourner les statistiques agrégées
  return {
    completedCount: totalCompletedCount,
    gradeTotal: totalGradeSum,
    gradeCount: totalGradeCount,
  };
}; 