// mongo model containing userId and user type student or tutor Id and timestamps

const mongoose = require("mongoose");
const { ROLE_TUTOR, ROLE_STUDENT } = require("../utils/constants/userRolesConstants");
//usertype enum 
const userType = [ROLE_STUDENT, ROLE_TUTOR];

const MaxiCoursConnexionsSchema = new mongoose.Schema({
  userId: {
    type: String,
    required: true,
  },
  userType: {
    type: String,
    required: true,
    enum: userType,
  },
}, { timestamps: true });

module.exports = mongoose.model("MaxiCoursConnexions", MaxiCoursConnexionsSchema);
