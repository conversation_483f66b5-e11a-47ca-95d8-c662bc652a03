const { createLogger, format, transports } = require("winston");

const myFormat = () => {
  return new Date(Date.now()).toString();
};

const loggerTypeConstants = require("../logger/loggerType.js");

const cvitaeLoggerLevels = require("../logger/loggerLevels.js");

class LoggerService {
  constructor(route) {
    this.route = route;
    const newLog = (loggerType) =>
      createLogger({
        levels: cvitaeLoggerLevels,
        transports: [
          new transports.File({
            filename: `./logs/api.log`,
          }),
          // new transports.MongoDB({
          //   db: mongodbUrl,
          //   options: { useUnifiedTopology: true },
          //   collection: "api-logs",
          //   capped: true,
          //   label: loggerType ? loggerType : loggerTypeConstants.general,
          //   format: format.combine(
          //     format.timestamp(),
          //     format.metadata({
          //       fillExcept: ["message", "level", "timestamp", "label"],
          //     })
          //   ),
          // }),
        ],
        meta: true,
        format: format.printf((info) => {
          let message = `${myFormat()} | ${info.level.toUpperCase()} | ${route} | LoggerType:${
            loggerType ? loggerType : loggerTypeConstants.general
          } | ${info.message} | `;
          message = info.obj
            ? message + `data:${JSON.stringify(info.obj)} | `
            : info.request
            ? message + `data:${JSON.stringify(info.request)}| `
            : message;
          return message;
        }),
      });

    this.newLog = newLog;
  }

  setLoggerType(logger) {
    this.loggerType = logger;
  }

  //generate a method to return logger type constants
  getLoggerTypeConstants() {
    return loggerTypeConstants;
  }
}

module.exports = LoggerService;
