const mongoose = require("mongoose");

const dateTimeHelper = require("../utils/aarifiDateTimeHelper/zupDeco.dateTime.helper.js");

const establishmentSchema = new mongoose.Schema(
     {
          typeOfEstablishment: {
               typeOfEstablishmentId: String,
               typeOfEstablishmentName: String,
          },
          establishmentName: String,
          createdAt: {
               type: Date,
               default: dateTimeHelper.getCurrentDateTimeInParisZone(),
          },
          generalInformation: {
               category: String,
               sector: {
                    sectorId: String,
                    sectorName: String,
               },
               status: String,
               groupOfEstablishment: String,
               fieldOfActivity: String,
               telePhone: String,
               address: {
                    address: String,
                    postalCode: String,
                    city: String,
                    country: String,
               },
          },
          //partnership
          partnership: {
               startOfPartnership: Date,
               participationInTheClassCouncil: Boolean,
               zupDeCoLineInTheNewsLetter: Boolean,
               appreciationOfThePartnership: String,
               comments: String,
               appraisalOfThePartnership: String,
          },

          //Session
          session: {
               //Display the number of students tutored in this establishment =0 by default, then automatically incremented
               numberOfActiveTutoredStudentsWhole: Number,
               //Display the number of students waiting for support =0 by default, then automatically calculated
               pupilsWaiting: Number,
               accessToTheComputerRoom: Boolean,
               //Display the number of active tutors
               numberOfActiveTutors: Number,
          },

          //class
          class: [
               {
                    level: String,
                    levelId: String,
                    className: String,
               },
          ],
          //detail information
          detailInformationSchool: {
               //Total number of pupils in the establishment in year N (to be entered)
               number: Number,
               //Active number of students in the school in year N-1, 0 by default and to be entered
               numberActiveN1: Number,
               //Active number of students in the school in year N-1, 0 by default and to be entered
               numberActiveN2: Number,
               brevetResult: Number,
               underPrivilegedCspS: Number,
               percentage: Number,
               boarding: Boolean,
               enCategory: [String],
          },
          detailInformationHigherSchool: {
               //Total number of students in the establishment
               number: Number,
               //Total number of tutors in the establishment in year N-1, 0 by default
               activeHeadCountN1: Number,
               //Total headcount of tutors in the establishment in year N-2 .0 by default
               activeHeadCountN2: Number,
          },

          //Contact of the establishment is the same for 3 types of establishments (school, higher school, structure)
          contact: [
               {
                    firstName: String,
                    lastName: String,
                    email: String,
                    phoneNumber: String,
                    function: String,
               },
          ],
     },
     { versionKey: false }
);

module.exports = Establishment = mongoose.model("establishment", establishmentSchema);
