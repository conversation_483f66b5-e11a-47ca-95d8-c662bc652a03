const admin = require("firebase-admin");

//import bucket
const bucket = admin.storage().bucket();

//import uuid
const uuid = require("uuid");

//import constants
const firebaseConstants = require("../../utils/constants/FirebaseConstants");

// upload file to firebase storage, @params: file, fileName and folderName
exports.uploadFile = async (file, folderName) => {
     try {
          var fileId = generateRandomGuid();

          var response = await bucket.upload(file.filepath, {
               destination: folderName + "/" + fileId,
               public: true,
               metadata: {
                    contentType: file.mimetype,
                    cacheControl: "public, max-age=31536000",
                    firebaseStorageDownloadTokens: uuid,
               },
          });

          var file = response[0];
          var download_url = response[0].metadata.mediaLink;

          var preview_url = "https://firebasestorage.googleapis.com/v0/b/" + bucket.name + "/o/" + encodeURIComponent(file.name) + "?alt=media&token=";
          var file_id = response[0].metadata.id;

          var data = {
               download_url: download_url,
               preview_url: preview_url,
               file_id: fileId,
          };

          return { status: true, data: data };
     } catch (error) {
          console.log(error);

          return { status: false, data: error };
     }
};

//delete file from firebase storage, @params: file
exports.deleteFile = async (folder, fileId) => {
     try {
          var response = await bucket.file(folder + "/" + fileId).delete();
          return { status: true, data: `File ${fileId} deleted successfully` };
     } catch (error) {
          console.log(error);
          return { status: false, data: error };
     }
};

function generateRandomGuid() {
     return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
          var r = (Math.random() * 16) | 0,
               v = c == "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
     });
}
