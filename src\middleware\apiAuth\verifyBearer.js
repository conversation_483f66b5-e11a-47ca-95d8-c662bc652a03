const userRolesConstants = require("../../utils/constants/userRolesConstants");
const { admin } = require("../firebase/Init.Firebase");
const jwt = require("jsonwebtoken");
const secretKey = process.env.ROLE_TOKEN_SECRET;
const PUBLIC_ROUTES = [
  "signIn",
  "register",
  "levels",
  "/refresh_token",
  "/user/tutor",
  "/user/parent",
  "/user/student",
  "/tutor/update-pass",
  "/parent/update-pass",
  "/externalUser/register",
  "/userAdministrationRole",
  "/user/savePhoneNumber",
  "/user/checkIfTheUserExistsInDB",
  "user/password-status",
  "/metrics",
  "/test",
  "/session/live",
  "/session/close"
];
require("dotenv").config();
const getAuthToken = (req, res, next) => {
  if (
    req?.headers?.authorization &&
    req?.headers?.authorization.split(" ")[0] === "Bearer"
  ) {
    req.authToken = req.headers.authorization.split(" ")[1];
  } else {
    req.authToken = null;
  }
  next();
};
const generateAccessToken = (userId) => {
  console.log("userId", userId);
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: "24h",
  });
};
const generateRefreshToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: "7d",
  });
};
const checkIfAuthenticated = (req, res, next) => {
  //get the route and check if it is a sign in route
  const isPublicRoute = PUBLIC_ROUTES.some((route) => req.path.includes(route));
  if (isPublicRoute) {
    return next();
  }
  getAuthToken(req, res, async () => {
    try {
      const { authToken } = req;
      if (!authToken) throw new Error("Unauthenticated.");
      jwt.verify(authToken, process.env.JWT_SECRET, (err, userInfo) => {
        if (err) {
          console.log("Error verifying auth token:", err);
          return res.status(401).send({ error: "Unauthenticated." });
        }
        req.userId = userInfo.userId;
        return next();
      });
    } catch (e) {
      console.log("Error verifying auth token:", e);
      return res.status(401).send({ error: "Unauthenticated." });
    }
  });
};
const allowRequestToPass = (req, res, next) => {
  return next();
};
const getRoleToken = (req, res, next) => {
  if (req.headers["x-role-token"]) {
    req.roleToken = req.headers["x-role-token"];
  } else {
    req.roleToken = null;
  }
  next();
};

const checkIfAuthorized = (permittedRoles) => {
  return (req, res, next) => {
    const isPublicRoute = PUBLIC_ROUTES.some((route) =>
      req.path.includes(route)
    );
    if (isPublicRoute) {
      return next();
    }
    getRoleToken(req, res, async () => {
      try {
        if (req?.path?.includes("signIn")) return next();
        const { roleToken } = req;
        if (!roleToken) {
          return res.status(403).send({ error: "No role token provided." });
        }

        const { decoded, isValid } = await validateRoleToken(
          roleToken,
          permittedRoles
        );
        if (!isValid) {
          return res.status(403).send({ error: "Forbidden." });
        }
        if (decoded) {
          // console.log('decoded', decoded)
          req.userId = decoded.userId;
          req.userRole = decoded.role;
        }

        return next();
      } catch (e) {
        console.error("Error verifying role token:", e);
        return res.status(403).send({ error: "Unauthorized." });
      }
    });
  };
};

const validateRoleToken = async (roleToken, permittedRoles) => {
  try {
    // Verify the role token
    const decoded = jwt.verify(roleToken, secretKey);
    // Check if the user has the Super Admin role
    const isAdmin = decoded.role === userRolesConstants.SUPER_ADMINISTRATOR;
    // Check if the user has the required role
    return {
      isValid:
        !permittedRoles?.length ||
        isAdmin ||
        permittedRoles.includes(decoded.role),
      decoded,
    };
  } catch (e) {
    console.error("Error verifying role token:", e);
    return false;
  }
};

module.exports = {
  checkIfAuthenticated,
  checkIfAuthorized,
  generateAccessToken,
  generateRefreshToken,
  allowRequestToPass,
};
