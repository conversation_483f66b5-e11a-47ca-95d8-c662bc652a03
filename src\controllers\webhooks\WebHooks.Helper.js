//import webhook model
const webhookModel = require("../../models/BigBlueWebHooks.Model.js");

//setup function to create new webHook in mongodb
exports.createNewWebHook = async (webHookId, callbackURL, permanentHook, isUpAndRunning) => {
     try {
          const newWebHook = new webhookModel({
               webHookId: webHookId,
               callbackURL: callbackURL,
               permanentHook: permanentHook,
               isUpAndRunning: isUpAndRunning,
          });

          let savedWebHook = await newWebHook.save();
          let response = { status: true, message: `Webhook created successfully`, data: savedWebHook };
          return response;
     } catch (error) {
          let response = { status: false, message: "Error in creating new webHook", data: error };
          return response;
     }
};

exports.updateWebhook = async (documentId, webHookId, callbackURL) => {
     //update webHook in mongodb
     const updatedWebHook = await webhookModel.findOne({ _id: documentId }).exec();

     updatedWebHook.webHookId = webHookId;
     updatedWebHook.callbackURL = callbackURL;
     updatedWebHook.isUpAndRunning = true;
     updatedWebHook.permanentHook = false;

     //save updated webHook
     await updatedWebHook.save();

     return updatedWebHook;
};

//check if webHook is created in mongodb
exports.checkIfWebHookIsCreatedInMongoDb = async (callbackURL) => {
     const listOfHooks = await webhookModel.find({ callbackURL: callbackURL });
     if (listOfHooks.length > 0) {
          return true;
     } else {
          return false;
     }
};

//get all webhooks
exports.getAllWebHooks = async () => {
     const listOfHooks = await webhookModel.find({});
     return listOfHooks;
};

//delete webHook from mongodb
exports.deleteWebHook = async (webHookId) => {
     const webHook = webhookModel.findOneAndRemove({ webHookId: webHookId }).exec();

     //check if webHook is deleted and return response true or false
     if (webHook) {
          return true;
     } else {
          return false;
     }
};
