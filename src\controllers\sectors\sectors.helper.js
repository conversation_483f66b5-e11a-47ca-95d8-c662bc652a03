const apiResponse = require("../../controllers/apiresponse/ApiResponseHelper.js");

const userHelper = require("../user/User.Helper.js");

const userAdministrationHelper = require("../user/user.administration.helper.js");

const sectorsModel = require("../../models/Sectors.Model.js");

const mongoose = require("mongoose");

//get Coordinators for sector based on  sectorId
exports.getCoordinatorsPerSector = async (sectorIds) => {
  try {
    const sectorDocuments = await sectorsModel.find({
      code: { $in: [sectorIds] },
    });
    if (sectorDocuments.length === 0) {
      return { status: false, message: "No sector found" };
    }

    const coordinatorsList = await sectorDocuments.flatMap(
      (sector) => sector.coordinators
    );
    if (coordinatorsList.length === 0) {
      return { status: false, message: "No coordinators found" };
    }
    // remove duplicate coordinators
    const coordinators = coordinatorsList.filter(
      (coordinator, index, self) =>
        index === self.findIndex((t) => t.userId === coordinator.userId)
    );
    return { status: true, data: coordinators };
  } catch (error) {
    console.error(error);
    return { status: false, message: error.message };
  }
};

exports.getCoordinatorsProfilePerSector = async (sectorIds) => {
  try {
    let sectorDocuments = [];
    for (let i = 0; i < sectorIds.length; i++) {
      const sectorId = sectorIds[i];

      //convert sectorId to ObjectId
      const sectorObjectId = mongoose.Types.ObjectId(sectorId);

      const sectorDocument = await sectorsModel.findOne({
        _id: sectorObjectId,
      });
      if (sectorDocument) {
        sectorDocuments.push(sectorDocument);
      }
    }
    //if there is no sector
    if (!sectorDocuments.length > 0)
      return { status: false, message: "No sector found" };
    let coordinatorsList = [];
    for (let i = 0; i < sectorDocuments.length; i++) {
      const sector = sectorDocuments[i];
      const coordinators = sector.coordinators;
      for (let j = 0; j < coordinators.length; j++) {
        const coordinator = coordinators[j];
        const coordinatorId = coordinator.userId;
        const coordinatorProfile =
          await userAdministrationHelper.getAdminProfile(coordinatorId);
        if (coordinatorProfile) {
          coordinatorsList.push(coordinatorProfile.data);
        }
      }
    }
    return { status: true, data: coordinatorsList };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get sector name by sectorId
exports.getSectorNameById = async (sectorId) => {
  try {
    //convert sectorId to ObjectId
    const sectorObjectId = mongoose.Types.ObjectId(sectorId);

    const sectorDocument = await sectorsModel.findOne({ _id: sectorObjectId });

    if (!sectorDocument) return { status: false, message: "No sector found" };

    return { status: true, data: sectorDocument.name };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get SectorId by coordinatorId
exports.getSectorIdByCoordinatorId = async (coordinatorId) => {
  try {
    //get sectorId from coordinatorId
    const sectorId = await sectorsModel
      .findOne({ "coordinators.userId": coordinatorId })
      .exec();

    if (!sectorId) return { status: false, message: "No sector found" };

    const sectorObjectId = sectorId._id;

    return { status: true, data: sectorObjectId.toString() };
  } catch (error) {
    s;
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
exports.getSectorIdsByCoordinatorIds = async (coordinatorIds) => {
  try {
    //get sectorId from coordinatorId
    const sectors = await sectorsModel
      .find({ "coordinators.userId": { $in: coordinatorIds } })
      .exec();

    if (!sectors?.length) return { status: false, message: "No sector found" };

    const sectorsObjectIds = sectors.map((sector) => sector._id.toString());

    return { status: true, data: sectorsObjectIds };
  } catch (error) {
    s;
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get all schoolZoneId by array sectorIds
exports.getSchoolZoneIdsBySectorIds = async (sectorIds) => {
  try {
    let schoolZoneIds = [];
    for (let i = 0; i < sectorIds.length; i++) {
      const sectorId = sectorIds[i];

      //convert sectorId to ObjectId
      const sectorObjectId = mongoose.Types.ObjectId(sectorId);

      const sectorDocument = await sectorsModel.findOne({
        _id: sectorObjectId,
      });
      if (sectorDocument) {
        schoolZoneIds.push(sectorDocument?.schoolZone?.schoolZoneId);
      }
    }
    //if there is no sector
    if (!schoolZoneIds.length > 0)
      return { status: false, message: "No sector found" };
    return { status: true, data: schoolZoneIds };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
