const express = require("express");

const router = express.Router();

const apiLogs = require("../models/ApiLog.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("apiLogger.js");

//import establishment model
const EducationAnnuaire = require("../models/EducationAnnuaire.Model.js");

//import Sector model
const Sector = require("../models/Sectors.Model.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const typeOfEstablishmentConstants = require("../utils/constants/typeofestablishments.constants.js");

const getDataWithPaginationAndFilterAndOrder = require("../controllers/mongoDb/get.Data.Filter.Order.js");

const coordinatorHelper = require("../controllers/user/coordinator.helper.js");
const studentPreferencesModel = require("../models/Student.Preferences.Model.js");
const establishmentModel = require("../models/Establishment.Model.js");
const typeOfEstablishments = require("../utils/constants/typeofestablishments.constants.js");

const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  SUPER_ADMINISTRATOR,
  COORDINATOR,
  MANAGER,
  VSC,
  ROLE_TUTOR,
} = require("../utils/constants/userRolesConstants.js");

// router.use((req, res, next) => {
//      verifyUserRole.checkUserRoleAndAccessLevel(req, res, next, mongodbModelConstants.modelName.ESTABLISHMENTS);
// });

//Add new establishment
/**
 * @swagger
 * /api/v1/establishment:
 *   post:
 *     summary: Create a new establishment
 *     description: Create a new establishment by providing necessary details.
 *     tags: [Establishments]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               establishment:
 *                 type: string
 *                 description: JSON string representing the establishment object.
 *     responses:
 *       '200':
 *         description: New establishment created successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "New establishment saved successfully"
 *                 data:
 *                   type: object
 *                   example: {}
 *       '400':
 *         description: Invalid request or establishment data not provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "establishment is required to perform this action."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to save new establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 *                 data:
 *                   type: string
 *                   example: "{}"
 */
router.post(
  "/establishment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const establishment = JSON.parse(fields.establishment);

        //check if establishment is provided

        if (!establishment) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `establishment is required to perform this action. Please provide a valid establishment.`
          );
          return res.status(400).json(response);
        }

        const newEstablishment = new EducationAnnuaire(establishment);
        //update student preferences sectors
        await studentPreferencesModel.updateMany(
          {
            "assignment.establishments": {
              $elemMatch: { establishmentId: establishment._id },
            },
            "assignment.sectors": {
              $not: {
                $elemMatch: {
                  sectorId: establishment.generalInformation.sector?.sectorId,
                },
              },
            },
          },
          {
            $addToSet: {
              "assignment.sectors": {
                sectorId: establishment.generalInformation.sector?.sectorId,
                sectorName: establishment.generalInformation.sector?.sectorName,
                sectorId: establishment.generalInformation.sector?.sectorId,
              },
            },
          }
        );

        let savedEstablishment = await newEstablishment.save();

        if (!savedEstablishment) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            "Failed to save new establishment"
          );
          return res.status(500).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "New establishment saved successfully",
            savedEstablishment
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "Failed to save new establishment",
          JSON.stringify(error)
        );
        return res.status(500).json(response);
      }
    });
  }
);
// add new tutor establishment
router.post(
  "/establishment/tutor",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, ROLE_TUTOR]),
  ],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      console.log("fields=>", fields);
      try {
        let establishment = JSON.parse(fields.establishment);
        console.log("esttttt=>>>>>", establishment);
        establishment = {
          establishmentName: establishment.establishmentName,
          typeOfEstablishment: {
            typeOfEstablishmentId:
              establishment.typeOfEstablishment.typeOfEstablishmentId,
            typeOfEstablishmentName:
              establishment.typeOfEstablishment.typeOfEstablishmentName,
          },
          generalInformation: {
            sector: { sectorId: null, sectorName: null },
            telephone: "",
            address: { address: "", postalCode: null, city: "", country: "" },
          },
          partnership: {},
          session: {},
          detailInformationHigherSchool: {
            activeHeadCountN1: 0,
            activeHeadCountN2: 0,
          },
          contact: [{ email: "", phoneNumber: "" }],
        };
        console.log("establishment =>", establishment);
        //check if establishment is provided

        if (!establishment) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `establishment is required to perform this action. Please provide a valid establishment.`
          );
          return res.status(400).json(response);
        }

        const newEstablishment = new EducationAnnuaire(establishment);
        console.log("newEstablishment =>", newEstablishment);

        let savedEstablishment = await newEstablishment.save();

        if (!savedEstablishment) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            "Failed to save new establishment"
          );
          return res.status(500).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "New establishment saved successfully",
            savedEstablishment
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "Failed to save new establishment",
          JSON.stringify(error)
        );
        return res.status(500).json(response);
      }
    });
  }
);
//get establishment _id
/**
 * @swagger
 * /api/v1/establishment:
 *   get:
 *     summary: Get establishment details
 *     description: Get details of an establishment by establishmentId.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: establishmentId
 *         required: true
 *         description: The ID of the establishment to retrieve details for.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Establishment details retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment found"
 *                 data:
 *                   type: object
 *                   properties:
 *                     establishment:
 *                       type: object
 *                       example: {}
 *                     sectors:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           sectorId:
 *                             type: string
 *                           sectorName:
 *                             type: string
 *       '400':
 *         description: Invalid request or establishmentId not provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "EstablishmentId is required"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/establishment", async (req, res) => {
  try {
    const establishmentId = req.query.establishmentId;

    // if (!establishmentId) {
    //      let response = apiResponse.responseWithStatusCode(
    //           apiResponse.apiConstants.API_REQUEST_SUCCESS,
    //           `EstablishmentId is required `,
    //           `EstablishmentId is required to perform this action. Please provide a valid establishmentId.`
    //      );
    //      return res.status(400).json(response);
    // }

    let establishment = await EducationAnnuaire.findOne({
      _id: establishmentId,
    });

    // if (!establishment) {
    //      let response = apiResponse.responseWithStatusCode(apiResponse.apiConstants.API_REQUEST_SUCCESS, "Establishment not found", `Establishment not found with id ${establishmentId}`);
    //      return res.status(200).json(response);
    // }

    //Get all sectors
    let sectorsDocuments = await Sector.find();

    //get _id of sectors and name of sectors
    let sectors = sectorsDocuments.map((sector) => {
      return { sectorId: sector._id, sectorName: sector.name };
    });

    //combine sectors and establishment
    let combineData = {
      establishment: establishment,
      sectors: sectors,
    };

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Establishment found",
      combineData
    );
    return res.status(200).json(response);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get establishment",
      error
    );
    return res.status(500).json(response);
  }
});

//establishment update
/**
 * @swagger
 * /api/v1/establishment:
 *   put:
 *     summary: Update establishment details
 *     description: Update details of an establishment by providing the updated establishment object.
 *     tags: [Establishments]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               establishment:
 *                 type: string
 *                 description: JSON string representing the updated establishment object.
 *     responses:
 *       '200':
 *         description: Establishment details updated successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment updated successfully"
 *                 data:
 *                   type: object
 *                   example: {}
 *       '400':
 *         description: Invalid request or establishmentId not provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "EstablishmentId is required"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to update establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.put(
  "/establishment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    const form = formidable({ multiples: true });
    form.parse(req, async (err, fields, files) => {
      try {
        const establishment = JSON.parse(fields.establishment);
        const establishmentId = establishment._id;

        //check if establishmentId is provided
        if (!establishmentId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `EstablishmentId is required to perform this action. Please provide a valid establishmentId.`
          );
          return res.status(400).json(response);
        }

        //update establishment
        let updatedEstablishment = await EducationAnnuaire.findOneAndUpdate(
          { _id: establishmentId },
          establishment,
          { new: true }
        );
        //update student preferences sectors
        await studentPreferencesModel.updateMany(
          {
            "assignment.establishments": {
              $elemMatch: { establishmentId: establishment._id },
            },
            "assignment.sectors": {
              $not: {
                $elemMatch: {
                  sectorId: establishment.generalInformation.sector?.sectorId,
                },
              },
            },
          },
          {
            $addToSet: {
              "assignment.sectors": {
                sectorId: establishment.generalInformation.sector?.sectorId,
                sectorName: establishment.generalInformation.sector?.sectorName,
                sectorId: establishment.generalInformation.sector?.sectorId,
              },
            },
          }
        );

        if (!updatedEstablishment) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            "Failed to update establishment"
          );
          return res.status(500).json(response);
        } else {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            "Establishment updated successfully",
            updatedEstablishment
          );
          return res.status(200).json(response);
        }
      } catch (error) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          "Failed to update establishment",
          error
        );
        logger
          .newLog(logger.getLoggerTypeConstants().admin)
          .error(response + error);
        return res.status(500).json(response);
      }
    });
  }
);

//delete establishment
/**
 * @swagger
 * /api/v1/establishment:
 *   delete:
 *     summary: Delete establishment
 *     description: Delete an establishment by establishmentId.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: establishmentId
 *         required: true
 *         description: The ID of the establishment to delete.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Establishment deleted successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment deleted successfully"
 *                 data:
 *                   type: string
 *                   example: "Establishment deleted successfully with id 123456789"
 *       '400':
 *         description: Invalid request or establishmentId not provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "EstablishmentId is required"
 *       '500':
 *         description: Internal server error or establishment not found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to delete establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.delete(
  "/establishment",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER]),
  ],
  async (req, res) => {
    try {
      let establishmentId = req.query.establishmentId;

      //check if establishmentId is provided
      if (!establishmentId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `EstablishmentId is required to perform this action. Please provide a valid establishmentId.`
        );
        return res.status(400).json(response);
      }

      //delete establishment
      let deletedEstablishment = await EducationAnnuaire.findOneAndDelete({
        _id: establishmentId,
      });

      if (!deletedEstablishment) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `Failed to delete establishment with id ${establishmentId}`,
          `Establishment not found with id ${establishmentId} or it may have been deleted already.`
        );
        return res.status(500).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "Establishment deleted successfully",
          `Establishment deleted successfully with id ${establishmentId}`
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to delete establishment",
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().admin)
        .error(response + error);
      return res.status(500).json(response);
    }
  }
);
//Education-annuaire establishments
/**
 * @swagger
 * /api/v1/establishment/dashboard/phonebook:
 *   get:
 *     summary: Get phonebook for establishment dashboard
 *     description: Retrieve phonebook entries for the establishment dashboard based on search criteria and types of establishments.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: search
 *         required: false
 *         description: The search term to filter establishments by name or city.
 *         schema:
 *           type: string
 *       - in: query
 *         name: type
 *         required: true
 *         description: The types of establishments to filter by.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Phonebook entries retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/EducationAnnuaire'
 *       '400':
 *         description: Invalid request or invalid array of types provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Please provide a valid array of types"
 *       '404':
 *         description: Establishment not found based on the provided search criteria and types.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Establishment not found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/establishment/dashboard/phonebook", async (req, res) => {
  try {
    const { search, city } = req.query;
    if (!city && !search) {
      return res
        .status(400)
        .json({ message: "Please provide a valid search or city value" });
    }
    // Initialize query
    let query = {};
    // Add search functionality
    if (search) {
      const formattedSearch = `"${search}"`;
      query.$text = {
        $search: formattedSearch,
        $caseSensitive: false, // Ignore case
        $diacriticSensitive: false, // Ignore diacritics
      };
    }
    if (city) {
      // Handle empty city
      if (city === "00") {
        query["$or"] = [
          { "generalInformation.address.city": "" },
          { "generalInformation.address.city": null },
          { "generalInformation.address.city": { $exists: false } },
        ];
      } else {
        // Handle postal code search
        query["generalInformation.address.postalCode"] = {
          $regex: `^${city}`, // Match postal codes that start with the city value
          $options: "i", // Case-insensitive match
        };
      }
    }
    const existingEstablishments = await EducationAnnuaire.find(query)
      .select("establishmentName generalInformation.address.city")
      .sort({ "generalInformation.address.city": 1 }); // Sort alphabetically in ascending order (1 for ascending, -1 for descending)

    // Handle no results
    if (!existingEstablishments.length) {
      return res.status(404).json({ message: "Establishment not found" });
    }
    // Return results
    return res.status(200).json(existingEstablishments);
  } catch (error) {
    console.error("Error fetching establishments:", error);
    return res.status(500).json({
      message: "Failed to get establishments",
      error: error.message,
    });
  }
});

//get establishment for tutor
router.get("/establishment/tutorEstablishment", async (req, res) => {
  try {
    const { search, type } = req.query;
    let parsedType = JSON.parse(type);
    //  parsedType = [
    //   'entreprise',
    //   // 'École',
    //   // 'university',
    //   // 'Grand établissement',
    //   // 'Autre établissement'
    // ]
    parsedType.push("Autre");
    let query = {};
    if (!Array.isArray(parsedType) || parsedType.length === 0) {
      return res
        .status(400)
        .json({ message: "Please provide a valid array of types" });
    }
    if (parsedType[0] !== "all") {
      query = {
        "typeOfEstablishment.typeOfEstablishmentName": { $in: parsedType },
      };
    }

    if (search && search.trim().length > 0) {
      query.$or = [
        { establishmentName: { $regex: search, $options: "i" } },
        { establishmentName: "Autre" },
        {
          "generalInformation.address.city": { $regex: search, $options: "i" },
        },
      ];
    }
    let existingEstablishments = await EducationAnnuaire.find(query);
    existingEstablishments.sort((a, b) => {
      if (a.establishmentName === "Autre") return 1;
      if (b.establishmentName === "Autre") return -1;
      return 0;
    });

    if (!existingEstablishments.length) {
      return res.status(404).json({ message: "Establishment not found" });
    }
    return res.status(200).json(existingEstablishments);
  } catch (error) {
    console.log(error);
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get establishment",
      error
    );
    // logger
    //   .newLog(logger.getLoggerTypeConstants().admin)
    //   .error(response + error);
    return res.status(500).json(response);
  }
});

//establishment  dashboard
/**
 * @swagger
 * /api/v1/establishment/dashboard/annuaire:
 *   get:
 *     summary: Get establishment dashboard data
 *     description: Retrieve establishment dashboard data with pagination, filtering, and sorting options.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: filter
 *         required: false
 *         description: The filter to apply to the data.
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         required: false
 *         description: The field to sort the data by.
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         required: false
 *         description: The search term to filter establishments by name or city.
 *         schema:
 *           type: string
 *       - in: query
 *         name: pageSize
 *         required: false
 *         description: The number of items to return per page.
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         required: false
 *         description: The page number to retrieve.
 *         schema:
 *           type: integer
 *     responses:
 *       '200':
 *         description: Establishment dashboard data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/EducationAnnuaireDashboardEntry'
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalCount:
 *                       type: integer
 *                       example: 100
 *       '400':
 *         description: Invalid request or invalid query parameters provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid query parameters"
 *       '404':
 *         description: Establishment dashboard data not found based on the provided criteria.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data not found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to retrieve establishment dashboard data"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/establishment/dashboard/annuaire",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, MANAGER, VSC, COORDINATOR]),
  ],
  async (req, res, next) => {
    try {
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const search = req.query.search;
      const pageSize = req.query.pageSize;
      const page = req.query.page;

      const userId = req.userId;
      const userRole = req.userRole;

      const establishmentList =
        await getDataWithPaginationAndFilterAndOrder.getDataWithPaginationAndFilterAndOrder(
          {
            mongooseModel: EducationAnnuaire,
            filter,
            sortBy,
            search,
            pageSize,
            page,
            userId,
            userRole,
          }
        );
      //return only establishment name and _id, type, coordinates and numberActiveN1
      //print length of establishmentList
      let EducationEstablishmentDashboardList = [];
      const establishmentListOfData = establishmentList.data.listOfData;
      for (const establishment of establishmentListOfData) {
        //check establishment type
        let activeStudents = 0;
        let activeTutors = 0;
        if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.school.id
        ) {
          activeStudents = establishment.detailInformationSchool.number;
        } else if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.higherSchool.id
        ) {
          activeStudents = establishment.detailInformationHigherSchool.number;
          activeTutors =
            establishment.detailInformationHigherSchool.numberActiveN1;
        }
        const sectorId = establishment.generalInformation?.sector?.sectorId;
        const coordinates =
          await coordinatorHelper.getCoordinatorListByEstablishmentId(sectorId);

        let coordinatesList = [];
        if (coordinates?.length > 0) {
          //get only name from coordinates
          for (const coordinate of coordinates) {
            const coordinateName = coordinate.fullName;

            //put name in array of string
            coordinatesList.push(coordinateName);
          }
        }
        let establishmentDashboard = {
          establishmentId: establishment._id,
          establishmentName: establishment.establishmentName,
          establishmentType:
            establishment.typeOfEstablishment.typeOfEstablishmentName,
          establishmentCoordinates: coordinatesList,
          nbrActive: {
            activeStudents: activeStudents,
            activeTutors: activeTutors,
          },
        };
        EducationEstablishmentDashboardList.push(establishmentDashboard);
      }
      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Establishment dashboard data`,
        EducationEstablishmentDashboardList,
        establishmentList.data.page,
        establishmentList.data.pageSize,
        establishmentList.data.totalCount
      );

      return res.status(200).json(response);

      //
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get establishment dashboard",
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().admin)
        .error(response + error);
      return res.status(500).json(response);
    }
  }
);
//establishment  dashboard
/**
 * @swagger
 * /api/v1/establishment/dashboard:
 *   get:
 *     summary: Get establishment dashboard data
 *     description: Retrieve establishment dashboard data with pagination, filtering, and sorting options.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: filter
 *         required: false
 *         description: The filter to apply to the data.
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         required: false
 *         description: The field to sort the data by.
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         required: false
 *         description: The search term to filter establishments by name or city.
 *         schema:
 *           type: string
 *       - in: query
 *         name: pageSize
 *         required: false
 *         description: The number of items to return per page.
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         required: false
 *         description: The page number to retrieve.
 *         schema:
 *           type: integer
 *     responses:
 *       '200':
 *         description: Establishment dashboard data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/EducationAnnuaireDashboardEntry'
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalCount:
 *                       type: integer
 *                       example: 100
 *       '400':
 *         description: Invalid request or invalid query parameters provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid query parameters"
 *       '404':
 *         description: Establishment dashboard data not found based on the provided criteria.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data not found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to retrieve establishment dashboard data"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/establishment/dashboard",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      MANAGER,
      VSC,
      COORDINATOR,
      ROLE_TUTOR,
    ]),
  ],
  async (req, res, next) => {
    try {
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const search = req.query.search;
      const pageSize = req.query.pageSize;
      const page = req.query.page;
      const userId = req.userId;
      const userRole = req.userRole;
      const establishmentList =
        await getDataWithPaginationAndFilterAndOrder.getDataWithPaginationAndFilterAndOrder(
          {
            mongooseModel: EducationAnnuaire,
            filter,
            sortBy,
            search,
            pageSize,
            page,
            userId,
            userRole,
          }
        );
      //return only establishment name and _id, type, coordinates and numberActiveN1

      //print length of establishmentList
      let establishmentDashboardList = [];
      const establishmentListOfData = establishmentList.data.listOfData;
      for (const establishment of establishmentListOfData) {
        //check establishment type
        let activeStudents = 0;
        let activeTutors = 0;
        if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.school.id
        ) {
          activeStudents = establishment.detailInformationSchool.number;
        } else if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.higherSchool.id
        ) {
          activeStudents =
            establishment?.detailInformationHigherSchool?.number ?? 0;
          activeTutors =
            establishment?.detailInformationHigherSchool?.numberActiveN1 ?? 0;
        }
        const sectorId = establishment.generalInformation?.sector?.sectorId;
        // const coordinates = [];
        // await coordinatorHelper.getCoordinatorListByEstablishmentId(sectorId);

        // let coordinatesList = [];
        // if (coordinates.length > 0) {
        //get only name from coordinates
        // for (const coordinate of coordinates) {
        //   const coordinateName = coordinate.fullName;

        //put name in array of string
        //     coordinatesList.push(coordinateName);
        //   }
        // }
        let establishmentDashboard = {
          establishmentId: establishment._id,
          establishmentName: establishment.establishmentName,
          establishmentType:
            establishment.typeOfEstablishment.typeOfEstablishmentName,
          // establishmentCoordinates: coordinatesList,
          nbrActive: {
            activeStudents: activeStudents,
            activeTutors: activeTutors,
          },
          city: establishment.generalInformation.address.city,
          coordinator: establishment?.coordinator?.fullName,
        };
        establishmentDashboardList.push(establishmentDashboard);
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Establishment dashboard data`,
        establishmentDashboardList,
        establishmentList.data.page,
        establishmentList.data.pageSize,
        establishmentList.data.totalCount
      );

      return res.status(200).json(response);

      //
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get establishment dashboard",
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().admin)
        .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//check if establishment name already exists
/**
 * @swagger
 * /api/v1/establishment/checkEstablishmentName:
 *   get:
 *     summary: Check if establishment name already exists
 *     description: Check if the provided establishment name already exists in the database.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: establishmentName
 *         required: true
 *         description: The name of the establishment to check.
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Establishment name check successful.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment name already exists"
 *                 data:
 *                   type: object
 *                   properties:
 *                     alreadyExists:
 *                       type: boolean
 *                       example: true
 *                     establishmentId:
 *                       type: string
 *                       example: "610ef3d27e0fda0654616d3e"
 *       '400':
 *         description: Invalid request or missing establishmentName parameter.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "EstablishmentName is required"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to check establishment name"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/establishment/checkEstablishmentName", async (req, res) => {
  try {
    const establishmentName = req.query.establishmentName;

    //check if establishmentName is provided
    if (!establishmentName) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `EstablishmentName is required to perform this action. Please provide a valid establishmentName.`
      );
      return res.status(400).json(response);
    }

    //check if establishment name already exists
    const establishment = await EducationAnnuaire.findOne({
      establishmentName: establishmentName,
    });

    if (establishment) {
      let data = {
        alreadyExists: true,
        establishmentId: establishment._id,
      };
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Establishment name already exists`,
        data,
        apiResponse.apiConstants.ESTABLISHMENT_NAME_ALREADY_EXISTS
      );
      return res.status(200).json(response);
    } else {
      let data = {
        alreadyExists: false,
      };
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Establishment name does not exist`,
        data
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to check establishment name",
      error
    );
    return res.status(500).json(response);
  }
});

//export data for CSV file
/**
 * @swagger
 * /api/v1/establishment/dashboard/csv:
 *   get:
 *     summary: Get establishment dashboard data in csv format
 *     description: Retrieve establishment dashboard data with pagination, filtering, and sorting options.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: filter
 *         required: false
 *         description: The filter to apply to the data.
 *         schema:
 *           type: string
 *       - in: query
 *         name: sortBy
 *         required: false
 *         description: The field to sort the data by.
 *         schema:
 *           type: string
 *       - in: query
 *         name: search
 *         required: false
 *         description: The search term to filter establishments by name or city.
 *         schema:
 *           type: string
 *       - in: query
 *         name: pageSize
 *         required: false
 *         description: The number of items to return per page.
 *         schema:
 *           type: integer
 *       - in: query
 *         name: page
 *         required: false
 *         description: The page number to retrieve.
 *         schema:
 *           type: integer
 *     responses:
 *       '200':
 *         description: Establishment dashboard data retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data"
 *                 data:
 *                   type: object
 *                   properties:
 *                     list:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/EducationAnnuaireDashboardEntry'
 *                     page:
 *                       type: integer
 *                       example: 1
 *                     pageSize:
 *                       type: integer
 *                       example: 10
 *                     totalCount:
 *                       type: integer
 *                       example: 100
 *       '400':
 *         description: Invalid request or invalid query parameters provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid query parameters"
 *       '404':
 *         description: Establishment dashboard data not found based on the provided criteria.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Establishment dashboard data not found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to retrieve establishment dashboard data"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/establishment/dashboard/csv",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res, next) => {
    try {
      const filter = req.query.filter;
      const sortBy = req.query.sortBy;
      const search = req.query.search;
      const pageSize = req.query.pageSize;
      const page = req.query.page;
      const userId = req.userId;
      const userRole = req.userRole;
      console.log("PAGE", page);
      const establishmentList =
        await getDataWithPaginationAndFilterAndOrder.getDataWithPaginationAndFilterAndOrder(
          {
            mongooseModel: EducationAnnuaire,
            filter,
            sortBy,
            search,
            pageSize,
            page,
            userId,
            userRole,
          }
        );
      //return only establishment name and _id, type, coordinates and numberActiveN1
      //print length of establishmentList
      let establishmentDashboardList = [];
      const establishmentListOfData = establishmentList.data.listOfData;
      for (const establishment of establishmentListOfData) {
        //check establishment type
        let activeStudents = 0;
        let activeTutors = 0;
        if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.school.id
        ) {
          activeStudents = establishment.detailInformationSchool?.number;
        } else if (
          establishment.typeOfEstablishment.typeOfEstablishmentId ==
          typeOfEstablishmentConstants.higherSchool.id
        ) {
          activeStudents = establishment.detailInformationHigherSchool?.number;
          activeTutors =
            establishment.detailInformationHigherSchool?.numberActiveN1;
        }
        const sectorId = establishment.generalInformation?.sector?.sectorId;
        const coordinates = [];
        // await coordinatorHelper.getCoordinatorListByEstablishmentId(sectorId);

        let coordinatesList = [];
        if (coordinates.length > 0) {
          //get only name from coordinates
          for (const coordinate of coordinates) {
            const coordinateName = coordinate.fullName;

            //put name in array of string
            coordinatesList.push(coordinateName);
          }
        }
        let establishmentDashboard = {
          establishmentId: establishment._id,
          establishmentName: establishment.establishmentName,
          establishmentType:
            establishment.typeOfEstablishment.typeOfEstablishmentName,
          establishmentCoordinates: coordinatesList,
          nbrActive: {
            activeStudents: activeStudents,
            activeTutors: activeTutors,
          },
        };
        establishmentDashboardList.push(establishmentDashboard);
      }

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Establishment dashboard data`,
        establishmentDashboardList,
        establishmentList.data.page,
        establishmentList.data.pageSize,
        establishmentList.data.totalCount
      );

      return res.status(200).json(response);

      //
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get establishment dashboard",
        error
      );
      // logger
      //   .newLog(logger.getLoggerTypeConstants().admin)
      //   .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//get all names of establishments establishmentName for dropdown list autocomplete
/**
 * @swagger
 * /api/v1/establishment/autoComplete:
 *   get:
 *     summary: Auto-complete establishment names
 *     description: Retrieve a list of establishment names that match the provided filter.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: filter
 *         description: The filter object to apply for auto-completion.
 *         schema:
 *           type: object
 *           properties:
 *             establishmentName:
 *               type: string
 *               description: The establishment name to search for auto-completion.
 *     responses:
 *       '200':
 *         description: Establishment name list retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment name list retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       establishmentId:
 *                         type: string
 *                         example: "610ef3d27e0fda0654616d3e"
 *                       establishmentName:
 *                         type: string
 *                         example: "Example Establishment"
 *       '400':
 *         description: Invalid filter provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Invalid filter. Please provide a valid filter."
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get establishment names for auto-completion"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/establishment/autoComplete", async (req, res) => {
  try {
    let applyFilter;
    const filter = req.query.filter;
    if (filter) {
      try {
        applyFilter = JSON.parse(filter);
      } catch (error) {
        console.log(error);
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Invalid filter. Please provide a valid filter.`
        );
        return res.status(400).json(response);
      }
    } else {
      applyFilter = {};
    }

    let establishmentName = [];

    let query = {};
    if (applyFilter.establishmentName) {
      query = {
        establishmentName: {
          $regex: applyFilter.establishmentName,
          $options: "i",
        },
      };
      let establishmentList = await EducationAnnuaire.find(query).exec();

      establishmentList.forEach((establishment) => {
        let establishmentNameObject = {
          establishmentId: establishment._id,
          establishmentName: establishment.establishmentName,
        };
        establishmentName.push(establishmentNameObject);
      });

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Establishment name list",
        establishmentName
      );
      return res.status(200).json(response);
    } else {
      let establishmentList = await EducationAnnuaire.find(query).exec();

      establishmentList.forEach((establishment) => {
        let establishmentNameObject = {
          establishmentId: establishment._id,
          establishmentName: establishment.establishmentName,
        };
        establishmentName.push(establishmentNameObject);
      });

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Establishment name list",
        establishmentName
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get Sectors",
      error
    );
    // logger
    //   .newLog(logger.getLoggerTypeConstants().admin)
    //   .error(response + error);
    return res.status(500).json(response);
  }
});

//get establishment base on sector id's
/**
 * @swagger
 * /api/v1/establishment/baseOnSectors:
 *   get:
 *     summary: Get establishments based on sector IDs
 *     description: Retrieve establishments based on the provided sector IDs.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: sectorIds
 *         description: The array of sector IDs to filter establishments.
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *     responses:
 *       '200':
 *         description: Establishment names retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment names retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       establishmentId:
 *                         type: string
 *                         example: "610ef3d27e0fda0654616d3e"
 *                       establishmentName:
 *                         type: string
 *                         example: "Example Establishment"
 *       '400':
 *         description: No establishment found or invalid sector IDs provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "No establishment found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get establishment names based on sectors"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/establishment/baseOnSectors", async (req, res, next) => {
  try {
    let sectorIds = req.query.sectorIds;
    sectorIds = JSON.parse(sectorIds);

    //the path of data is generalInformation.sector.sectorId, so we need to return the establishment name and _id renamed as establishmentId
    const establishment =
      //  await Establishment
      await EducationAnnuaire.find({
        $and: [
          { "generalInformation.sector.sectorId": { $in: sectorIds } },
          // {
          //   $or: [
          //     {
          //       "typeOfEstablishment.typeOfEstablishmentName":
          //         typeOfEstablishments.school.name,
          //     },
          //     {
          //       "typeOfEstablishment.typeOfEstablishmentName":
          //         typeOfEstablishments.structure.name,
          //     },
          //   ],
          // },
        ],
      }).exec();
    //return only establishment name and _id, _id renamed as establishmentId
    let establishmentName = [];
    establishment.forEach((establishment) => {
      establishmentName.push({
        establishmentId: establishment._id,
        establishmentName: establishment.establishmentName,
      });
    });

    if (!establishment) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No establishment found`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    } else {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        "Establishment name list",
        establishmentName
      );
      return res.status(200).json(response);
    }
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get establishment",
      error
    );
    // logger
    //   .newLog(logger.getLoggerTypeConstants().admin)
    //   .error(response + error);
    return res.status(500).json(response);
  }
});

//get list of class's name for dropdown list  base on level id
/**
 * @swagger
 * /api/v1/class/baseOnLevel:
 *   get:
 *     summary: Get classes based on level IDs
 *     description: Retrieve classes based on the provided level IDs.
 *     tags: [Classes]
 *     parameters:
 *       - in: query
 *         name: levelIds
 *         description: The array of level IDs to filter classes.
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *     responses:
 *       '200':
 *         description: Class names retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Class names retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       className:
 *                         type: string
 *                         example: "Class A"
 *                       levelId:
 *                         type: string
 *                         example: "610ef3d27e0fda0654616d3e"
 *                       _id:
 *                         type: string
 *                         example: "610ef3d27e0fda0654616d3e"
 *       '400':
 *         description: No class found or invalid level IDs provided.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "No class found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get class names based on levels"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get("/class/baseOnLevel", async (req, res, next) => {
  try {
    const levelIds = JSON.parse(req.query.levelIds);

    //the path of class's is class array, so we need to return the class name
    const classList = await establishmentModel
      .find({ "class.levelId": { $in: levelIds } })
      .select("class")
      .exec();

    //return only className
    let className = [];
    classList.forEach((classItem) => {
      classItem.class.forEach((classItem) => {
        className.push({
          className: classItem.className,
          levelId: classItem.levelId,
          _id: classItem._id,
        });
      });
    });

    if (!classList) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `No class found`,
        `Please try again later.`
      );
      return res.status(400).json(response);
    }

    //return only class name that match with levelIds, only return class name
    className = className.filter((classItem) => {
      return levelIds.includes(classItem.levelId);
    });

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      "Class name list",
      className
    );
    return res.status(200).json(response);
  } catch (error) {
    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_FAILED,
      "Failed to get class",
      error
    );
    // logger
    //   .newLog(logger.getLoggerTypeConstants().admin)
    //   .error(response + error);
    return res.status(500).json(response);
  }
});

//get list of establishment's name for dropdown list  base on typeOfEstablishment
/**
 * @swagger
 * /api/v1/establishment/baseOnTypeOfEstablishment:
 *   get:
 *     summary: Get establishments based on type of establishment IDs
 *     description: Retrieve establishments based on the provided type of establishment IDs.
 *     tags: [Establishments]
 *     parameters:
 *       - in: query
 *         name: typeOfEstablishmentIds
 *         description: The array of type of establishment IDs to filter establishments.
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *         style: form
 *         explode: true
 *     responses:
 *       '200':
 *         description: Establishment names retrieved successfully.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "Establishment names retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       establishmentId:
 *                         type: string
 *                         example: "610ef3d27e0fda0654616d3e"
 *                       establishmentName:
 *                         type: string
 *                         example: "ABC School (New York)"
 *                       typeOfEstablishment:
 *                         type: object
 *                         properties:
 *                           typeOfEstablishmentId:
 *                             type: string
 *                             example: "610ef3d27e0fda0654616d3e"
 *                           typeOfEstablishmentName:
 *                             type: string
 *                             example: "School"
 *       '404':
 *         description: No establishment found.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "success"
 *                 message:
 *                   type: string
 *                   example: "No establishment found"
 *       '500':
 *         description: Internal server error.
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   example: "error"
 *                 message:
 *                   type: string
 *                   example: "Failed to get establishment names based on type of establishment"
 *                 error:
 *                   type: string
 *                   example: "Internal Server Error"
 */
router.get(
  "/establishment/baseOnTypeOfEstablishment",
  async (req, res, next) => {
    try {
      const typeOfEstablishmentIds = req.query.typeOfEstablishmentIds
        ? JSON.parse(req.query.typeOfEstablishmentIds)
        : [];

      //the path of data is typeOfEstablishment.typeOfEstablishmentId, so we need to return the establishment name and _id renamed as establishmentId

      const establishment = await //  Establishment
      EducationAnnuaire.find({
        "typeOfEstablishment.typeOfEstablishmentId": {
          $in: typeOfEstablishmentIds,
        },
      }).exec();

      //return only establishment name and _id, _id renamed as establishmentId
      let establishmentName = [];
      establishment.forEach((establishment) => {
        establishmentName.push({
          establishmentId: establishment._id,
          establishmentName: `${establishment.establishmentName}  (${establishment.generalInformation.address.city})`,
          typeOfEstablishment: establishment.typeOfEstablishment,
        });
      });

      if (!establishment) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `No establishment found`,
          `Please try again later.`
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          "Establishment name list",
          establishmentName
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      console.log(error);
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        "Failed to get establishment",
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().admin)
        .error(response + error);
      return res.status(500).json(response);
    }
  }
);

//export router
module.exports = router;
