module.exports = function (req, res, next) {
  const isSwaggerRequest = [
    "localhost:3000",
    "localhost:3001",
    "localhost:5173",
    "localhost:400",
    "https://zupdeco-development-jh3pg.ondigitalocean.app",
    "https://zupdeco-preprod-5qvv9.ondigitalocean.app",
  ].includes(req.get("host"));
  // Check if the request is coming from Swagger
  if (isSwaggerRequest) {
    return next();
  }
  // const visioRoutes = [
  //    "/session/live",
  //    "/session/close"
  // ]
  // const isVisioRoutes = visioRoutes.some((route) => req.path.includes(route));
  
  const apiKey = req.header("api-key");
  // if (isVisioRoutes  && apiKey !== process.env.ZUP_VISIO_API_KEY){
  //   return res.status(401).send("Access denied. Please check the API key...");
  // }
  // else if (!apiKey || apiKey !== process.env.API_KEY && !isVisioRoutes ) {
  if (!apiKey || apiKey !== process.env.API_KEY) {
    return res.status(401).send("Access denied. Please check the API key.");
  }
  return next();
};
