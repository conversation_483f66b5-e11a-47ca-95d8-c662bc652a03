//import checksum module
const hash = require("hash-converter");
const { get } = require("lodash");

//import crypto-js
const crypto = require("crypto");
// const { param } = require("../../routes/bigbluebutton.routes");

//import dotenv
require("dotenv").config();

//Generate checksum for bigBlueButton
exports.generateChecksum = (params) => {
  const secret = process.env.BIGBLUEBUTTON_SECRET;

  const checksumData = `${params}${secret}`;
  // return crypto.createHash("sha1").update(checksumData).digest("hex");

  //generate checksum with md5 from secret and paramString
  const checksum = crypto.createHash("sha1").update(checksumData).digest("hex");

  return checksum;
};

//Verify if request is valid from webhooks
exports.verifyRequest = (payLoad, signatureFromRequest) => {
  const secret = process.env.BIGBLUEBUTTON_SECRET;

  const checksumData = `${payLoad}${secret}`;

  //generate checksum with md5 from secret and paramString
  const checksum = crypto
    .createHash(`sha1`, secret)
    .update(checksumData)
    .digest("hex");

  //compare checksum with signature from request
  if (checksum === signatureFromRequest) {
    return true;
  } else {
    return false;
  }
};
