const express = require("express");

const router = express.Router();

const sessionModel = require("../models/Sessions.Model.js");

const apiLogs = require("../models/ApiLog.Model.js");

const tutorPreferences = require("../models/Tutor.Preferences.Model.js");

const studentPreferences = require("../models/Student.Preferences.Model.js");

const apiLoggerS = require("../services/logger/LoggerService.js");

const logger = new apiLoggerS("sessions.routes.js");

const formidable = require("formidable");

const apiResponse = require("../controllers/apiresponse/ApiResponseHelper.js");

const verifyApiKey = require("../middleware/apiAuth/verifyApiKey.js");

const verifyUserRole = require("../middleware/apiAuth/checkUser.Role.js");

const mongodbModelConstants = require("../utils/constants/mongodb.model.constants.js");

const bigBlueButtonHelper = require("../controllers/bigBlueButton/bigBlueButton.Helper.js");

const sessionsHelper = require("../controllers/sessions/sessions.helper");

const userHelper = require("../controllers/user/User.Helper.js");

const userAdminHelper = require("../controllers/user/user.administration.helper.js");

const tutorHelper = require("../controllers/tutor/tutor.helper.js");

const tutorConstant = require("../utils/constants/tutor.constants.js");

const sessionReporting = require("../models/SessionReporting.Model.js");
const programConstants = require("../utils/constants/program.constants.js");
const {
  checkIfAuthenticated,
  checkIfAuthorized,
} = require("../middleware/apiAuth/verifyBearer.js");
const {
  SUPER_ADMINISTRATOR,
  VSC,
  COORDINATOR,
  MANAGER,
  ROLE_TUTOR,
  ROLE_STUDENT,
  ROLE_PARENT,
} = require("../utils/constants/userRolesConstants.js");
const {
  sendReportForTodaySessions,
} = require("../services/aaJobs/sessions.job.js");
const SessionsModel = require("../models/Sessions.Model.js");

router.use((req, res, next) => {
  verifyUserRole.checkUserRoleAndAccessLevel(
    req,
    res,
    next,
    mongodbModelConstants.modelName.SESSIONS
  );
});

//Sessions Dashboard
/**
 * @swagger
 * /api/v1/sessions/dashboard:
 *   get:
 *     summary: Get sessions dashboard
 *     tags: [Sessions]
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: The page number for pagination
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 10
 *         description: The number of sessions per page
 *       - in: query
 *         name: filter
 *         schema:
 *           type: string
 *         description: Filter criteria for sessions
 *       - in: query
 *         name: sortBy
 *         schema:
 *           type: string
 *         description: Sort criteria for sessions
 *       - in: query
 *         name: sessionStatus
 *         schema:
 *           type: string
 *         description: The status of the session to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved the sessions dashboard
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: array
 *                   description: The list of sessions
 *                   items:
 *                     type: object
 *                     properties:
 *                       sessionId:
 *                         type: string
 *                       parentSessionId:
 *                         type: string
 *                       sessionType:
 *                         type: string
 *                       program:
 *                         type: string
 *                       status:
 *                         type: string
 *                       sessionLink:
 *                         type: string
 *                       tutors:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             fullName:
 *                               type: string
 *                             userId:
 *                               type: string
 *                             absence:
 *                               type: boolean
 *                             report:
 *                               type: object
 *                               properties:
 *                                 reportId:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *                                 reportLink:
 *                                   type: string
 *                                 description:
 *                                   type: string
 *                                 createdAt:
 *                                   type: string
 *                                   format: date-time
 *                       students:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             userId:
 *                               type: string
 *                             fullName:
 *                               type: string
 *                             absence:
 *                               type: boolean
 *                       vsc:
 *                         type: array
 *                         items:
 *                           type: object
 *                           properties:
 *                             fullName:
 *                               type: string
 *                             userId:
 *                               type: string
 *                             absence:
 *                               type: boolean
 *                             report:
 *                               type: object
 *                               properties:
 *                                 reportId:
 *                                   type: string
 *                                 status:
 *                                   type: string
 *                                 reportLink:
 *                                   type: string
 *                                 description:
 *                                   type: string
 *                                 createdAt:
 *                                   type: string
 *                                   format: date-time
 *                       school:
 *                         type: string
 *                       placeOrRoom:
 *                         type: string
 *                       sessionDate:
 *                         type: object
 *                         properties:
 *                           startDate:
 *                             type: string
 *                             format: date-time
 *                           endDate:
 *                             type: string
 *                             format: date-time
 *                           month:
 *                             type: integer
 *                           dayOfTheWeek:
 *                             type: integer
 *                           startHour:
 *                             type: string
 *                           endHour:
 *                             type: string
 *                       lastUpdatedAt:
 *                         type: string
 *                         format: date-time
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                       reportFromBigBlueButton:
 *                         type: object
 *                         properties:
 *                           meetingID:
 *                             type: string
 *                       updatingFutureSessions:
 *                         type: boolean
 *                       recurrence:
 *                         type: object
 *                         properties:
 *                           recurrenceNumber:
 *                             type: integer
 *                           recurrenceName:
 *                             type: string
 *                       scheduleDuringHolidays:
 *                         type: boolean
 *                       meetingRoom:
 *                         type: object
 *                         properties:
 *                           sessionsUrl:
 *                             type: string
 *                           meetingID:
 *                             type: string
 *                       createdBy:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                           fullName:
 *                             type: string
 *                           email:
 *                             type: string
 *                           userRole:
 *                             type: string
 *                       lastUpdatedBy:
 *                         type: object
 *                         properties:
 *                           userId:
 *                             type: string
 *                           fullName:
 *                             type: string
 *                           email:
 *                             type: string
 *                           userRole:
 *                             type: string
 *                 page:
 *                   type: integer
 *                   description: The current page number
 *                 pageSize:
 *                   type: integer
 *                   description: The number of sessions per page
 *                 total:
 *                   type: integer
 *                   description: The total number of sessions
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/sessions/dashboard",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      let page = req.query.page;
      let pageSize = req.query.pageSize;
      let filter = req.query.filter;
      let sortBy = req.query.sortBy;
      let tutorUserId = req.query.tutorUserId;
      let studentUserId = req.query.studentUserId;
      let coordinatorId = req.query.coordinatorId;
      page = apiResponse.buildPage(page);
      pageSize = apiResponse.buildPageSize(pageSize);

      const sessions = await sessionsHelper.sessionsDashboard({
        page,
        pageSize,
        filter,
        sortBy,
        userId: req.userId,
        userRole: req.userRole,
        tutorUserId,
        coordinatorId,
      });

      let response = apiResponse.responseWithPagination(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Sessions Dashboard`,
        sessions.data,
        page,
        pageSize,
        sessions.total
      );

      logger.newLog(logger.getLoggerTypeConstants().sessions).info({
        message: `Sessions Dashboard`,
        request: {
          requestUrl: req.originalUrl,
          requestMethod: req.method,
          requestParams: req.params,
          requestQuery: req.query,
        },
        createdBy: req.userId,
      });

      return res.status(200).json(response);
    } catch (error) {
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error getting sessions dashboard`,
        error
      );
      logger.newLog(logger.getLoggerTypeConstants().sessions).error({
        message: `Error getting sessions dashboard`,
        error: error,
        request: {
          requestUrl: req.originalUrl,
          requestMethod: req.method,
          requestParams: req.params,
          requestQuery: req.query,
        },
      });
      return res.status(500).json(newError);
    }
  }
);

router.get(
  "/sessions/coordinatorCalendar",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([COORDINATOR])],
  async (req, res) => {
    try {
      const userId = req.query.userId;
      const userRole = COORDINATOR;

      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is required.`,
          `Please provide UserId.`
        );
        return res.status(400).json(response);
      }

      let sessions = await sessionsHelper.getAllSessionsForCoordinator(
        userId,
        userRole
      );

      if (!sessions.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_FAILED,
          `No sessions found for coordinator.`,
          sessions.message
        );
        return res.status(404).json(response);
      }

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `All sessions for coordinator.`,
        sessions.data
      );
      return res.status(200).json(response);
    } catch (error) {
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error in getting coordinator sessions.`,
        "Please try again later."
      );
      console.log("error", error);
      return res.status(500).json(response);
    }
  }
);

//get sessions by sessionId
/**
 * @swagger
 * /api/v1/session:
 *   get:
 *     summary: Get session by sessionId
 *     tags: [Sessions]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         schema:
 *           type: string
 *         required: true
 *         description: The ID of the session to retrieve
 *     responses:
 *       '200':
 *         description: Successfully retrieved the session
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessionId:
 *                   type: string
 *                 parentSessionId:
 *                   type: string
 *                 sessionType:
 *                   type: string
 *                 program:
 *                   type: string
 *                 status:
 *                   type: string
 *                 sessionLink:
 *                   type: string
 *                 tutors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 students:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       fullName:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                 vsc:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 school:
 *                   type: string
 *                 placeOrRoom:
 *                   type: string
 *                 sessionDate:
 *                   type: object
 *                   properties:
 *                     startDate:
 *                       type: string
 *                       format: date-time
 *                     endDate:
 *                       type: string
 *                       format: date-time
 *                     month:
 *                       type: integer
 *                     dayOfTheWeek:
 *                       type: integer
 *                     startHour:
 *                       type: string
 *                     endHour:
 *                       type: string
 *                 lastUpdatedAt:
 *                   type: string
 *                   format: date-time
 *                 report:
 *                   type: object
 *                   properties:
 *                     reportId:
 *                       type: string
 *                     status:
 *                       type: string
 *                     reportLink:
 *                       type: string
 *                     description:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                 reportFromBigBlueButton:
 *                   type: object
 *                   properties:
 *                     meetingID:
 *                       type: string
 *                 updatingFutureSessions:
 *                   type: boolean
 *                 recurrence:
 *                   type: object
 *                   properties:
 *                     recurrenceNumber:
 *                       type: integer
 *                     recurrenceName:
 *                       type: string
 *                 scheduleDuringHolidays:
 *                   type: boolean
 *                 meetingRoom:
 *                   type: object
 *                   properties:
 *                     sessionsUrl:
 *                       type: string
 *                     meetingID:
 *                       type: string
 *                 createdBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *                 lastUpdatedBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/session",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const sessionId = req.query.sessionId;

      //check if sessionId is provided
      if (!sessionId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `sessionId is required`,
          `Please provide a sessionId`
        );
        return res.status(200).json(response);
      }

      const session = await sessionsHelper.getSessionById(sessionId);
      if (session.data) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Session found`,
          session.data,
          200
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Session not found`,
          `No session found with this sessionId, please check the sessionId`
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error getting sessions dashboard`,
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().sessions)
        .error(response + newError);
      return res.status(500).json(newError);
    }
  }
);
//generate a new session 0
/**
 * @swagger
 * /api/v1/sessions/sessions-zero:
 *   post:
 *     summary: Create a new session zero
 *     tags: [Sessions]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               sessionData:
 *                 type: string
 *                 description: JSON string containing session data
 *     responses:
 *       '200':
 *         description: Successfully created a new session 0
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessionId:
 *                   type: string
 *                   description: The ID of the newly created session zero
 *                 parentSessionId:
 *                   type: string
 *                   description: The parent session ID
 *                 sessionType:
 *                   type: string
 *                   description: The type of the session
 *                 program:
 *                   type: string
 *                   description: The program associated with the session
 *                 status:
 *                   type: string
 *                   description: The status of the session
 *                 sessionLink:
 *                   type: string
 *                   description: The link to the session
 *                 tutors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 students:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       fullName:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                 vsc:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 school:
 *                   type: string
 *                 placeOrRoom:
 *                   type: string
 *                 sessionDate:
 *                   type: object
 *                   properties:
 *                     startDate:
 *                       type: string
 *                       format: date-time
 *                     endDate:
 *                       type: string
 *                       format: date-time
 *                     month:
 *                       type: integer
 *                     dayOfTheWeek:
 *                       type: integer
 *                     startHour:
 *                       type: string
 *                     endHour:
 *                       type: string
 *                 lastUpdatedAt:
 *                   type: string
 *                   format: date-time
 *                 report:
 *                   type: object
 *                   properties:
 *                     reportId:
 *                       type: string
 *                     status:
 *                       type: string
 *                     reportLink:
 *                       type: string
 *                     description:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                 reportFromBigBlueButton:
 *                   type: object
 *                   properties:
 *                     meetingID:
 *                       type: string
 *                 updatingFutureSessions:
 *                   type: boolean
 *                 recurrence:
 *                   type: object
 *                   properties:
 *                     recurrenceNumber:
 *                       type: integer
 *                     recurrenceName:
 *                       type: string
 *                 scheduleDuringHolidays:
 *                   type: boolean
 *                 meetingRoom:
 *                   type: object
 *                   properties:
 *                     sessionsUrl:
 *                       type: string
 *                     meetingID:
 *                       type: string
 *                 createdBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *                 lastUpdatedBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *       '401':
 *         description: User not authenticated
 *       '500':
 *         description: Internal server error
 */
router.post(
  "/sessions/sessions-zero",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([
      SUPER_ADMINISTRATOR,
      COORDINATOR,
      MANAGER,
      ROLE_PARENT,
      VSC,
    ]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        const authUserId = req.header("userId");
        const origin = req.query.origin;
        const sessionData = JSON.parse(fields.sessionData);
        logger.newLog(logger.getLoggerTypeConstants().sessions).info({
          message: `create session`,
          request: {
            requestUrl: req.originalUrl,
            requestMethod: req.method,
            requestBody: sessionData,
          },
          createdBy: req.userId,
        });

        const authUser =
          sessionData.createdBy === "parent"
            ? await userHelper.getUserDetailsByUserId(authUserId)
            : await userAdminHelper.getUserAdministrationByUserId(authUserId);

        if (authUser && !authUser?.status) {
            return res.status(401).json(
            apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_FAILED,
              `User not authenticated`,
              `Authentication failed. Please try again.`,
              401
            )
            );
        }

        //call sessionHelper function generateNewSessionZero
        checkAvailability(sessionData, sessionData.tutors[0].userId, false)
          .then(async (result) => {
            logger
              .newLog(logger.getLoggerTypeConstants().sessions)
              .info(
                `Disponibilité du tuteur ${sessionData.tutors[0].userId} : ${result.tutorAvailable}, Disponibilité de  l'elèves ${result.studentavailable}`
              );

            if (result.tutorAvailable && result.studentavailable) {
              try {
                const newSessionZero = await sessionsHelper.createNewSession(
                  sessionData,
                  false,
                  {
                    email: authUser?.data?.userData?.contactDetails?.email,
                    fullName: `${authUser?.data?.userData?.contactDetails?.firstName} ${authUser?.data?.userData?.contactDetails?.lastName}`,
                    userId: authUser?.data?.userData?.userId,
                    userRole: authUser?.data?.userData?.userRole,
                  },
                  origin
                );
                if (newSessionZero.status ) {
                  if(newSessionZero?.data?.status != "canceled") {
                    const userId = sessionData.tutors[0].userId;
                    await sessionsHelper.updateMatchingTuntorStudent(
                      userId,
                      sessionData
                    );
                  }
                  let response = apiResponse.responseWithStatusCode(
                    newSessionZero.resp,
                    newSessionZero.message,
                    newSessionZero.data,
                    200
                  );
                  logger.newLog(logger.getLoggerTypeConstants().sessions).info(response);
                  return res.status(200).json(response);
                } else {
                  let newError = apiResponse.responseWithStatusCode(
                    apiResponse.apiConstants.API_REQUEST_FAILED,
                    `Error creating a new session 0`,
                    newSessionZero.message,
                    500
                  );
                  // logger.newLog(logger.getLoggerTypeConstants().sessions).error(newError);
                  return res.status(500).json(newError);
                }
              } catch (error) {
                let newError = apiResponse.responseWithStatusCode(
                  apiResponse.apiConstants.API_REQUEST_FAILED,
                  `Error creating a new session 0`,
                  error,
                  500
                );
                return res.status(500).json(newError);
              }
            } else {
              const newError = apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_FAILED,
                `tutor or student(s) is not available`,
                `tutor or student(s) is not available, please choise an other date`,
                401
              );
              return res.status(401).json(newError);
            }
          })
          .catch((error) => {
            console.error("Erreur :", error);
          });
      } catch (error) {
        console.log(error);
        return res
          .status(500)
          .json(
            apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_FAILED,
              `Error generating a new session 0`,
              error,
              500
            )
          );
      }
    });
  }
);
const checkAvailability = async (sessionData, tutorId, update) => {
  try {
    const { tutors, students, sessionDate } = sessionData;

    // Convertir les dates de session en objets Date
    const startDate = new Date(sessionDate.startDate);
    const endDate = new Date(sessionDate.endDate);
    const excludedStatuses = [
      "session-0-finish-stop",
      "session-0-abandoned",
      "failed",
      "canceled",
      "session-0-on-hold",
    ];
    // Rechercher des sessions qui se chevauchent avec la nouvelle session
    let query = {};
    console.log("update", update);
    // Ajouter la condition sur sessionId si on met à jour une session
    if (update) {
      query = {
        status: { $nin: excludedStatuses },
        parentSessionId: { $ne: null },
        $and: [
          {
            $or: [
              {
                "sessionDate.startDate": { $lt: endDate },
                "sessionDate.endDate": { $gt: startDate },
              },
              {
                "sessionDate.endDate": { $gt: startDate },
                "sessionDate.startDate": { $lt: endDate },
              },
            ],
          },
          {
            $or: [
              { "tutors.userId": tutors[0].userId },
              { "students.userId": students[0].userId },
            ],
          },
        ],
        sessionId: { $ne: sessionData.sessionId },
        parentSessionId: { $ne: null },
      };
    } else {
      query = {
        status: { $nin: excludedStatuses },
        parentSessionId: { $ne: null },
        $and: [
          {
            $or: [
              {
                "sessionDate.startDate": { $lt: endDate },
                "sessionDate.endDate": { $gt: startDate },
              },
              {
                "sessionDate.endDate": { $gt: startDate },
                "sessionDate.startDate": { $lt: endDate },
              },
            ],
          },
          {
            $or: [
              { "tutors.userId": tutors[0].userId },
              { "students.userId": students[0].userId },
            ],
          },
        ],
      };
    }

    // Rechercher les sessions qui se chevauchent
    const overlappingSessions = await sessionModel.find(query).exec();
    // Créer des ensembles pour vérifier les conflits
    // const occupiedTutors = new Set();
    // const occupiedStudents = new Set();

    // overlappingSessions.forEach((session) => {
    //   session.tutors.forEach((tutor) => occupiedTutors.add(tutor.userId));
    //   session.students.forEach((student) =>
    //     occupiedStudents.add(student.userId)
    //   );
    // });

    // Vérifier la disponibilité du tuteur
    const tutorAvailable = overlappingSessions.length > 0 ? false : true;

    // Vérifier la disponibilité des élèves
    // const unavailableStudents = students.filter((student) =>
    //   occupiedStudents.has(student.userId)
    // );
    let studentavailable = overlappingSessions.length > 0 ? false : true;
    return {
      tutorAvailable,
      studentavailable,
    };
  } catch (error) {
    console.error("Erreur lors de la vérification de disponibilité :", error);
    throw new Error("Erreur lors de la vérification de disponibilité");
  }
};

function filterObjects(sourceArray, filterArray, property) {
  return sourceArray.filter(
    (object1) =>
      !filterArray.some((object2) => object1[property] === object2[property])
  );
}

//update session
/**
 * @swagger
 * /api/v1/session:
 *   put:
 *     summary: Update a session
 *     tags: [Sessions]
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             properties:
 *               sessionData:
 *                 type: string
 *                 description: JSON string containing updated session data
 *     responses:
 *       '200':
 *         description: Successfully updated the session
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 sessionId:
 *                   type: string
 *                   description: The ID of the updated session
 *                 parentSessionId:
 *                   type: string
 *                   description: The parent session ID
 *                 sessionType:
 *                   type: string
 *                   description: The type of the session
 *                 program:
 *                   type: string
 *                   description: The program associated with the session
 *                 status:
 *                   type: string
 *                   description: The status of the session
 *                 sessionLink:
 *                   type: string
 *                   description: The link to the session
 *                 tutors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 students:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       userId:
 *                         type: string
 *                       fullName:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                 vsc:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       fullName:
 *                         type: string
 *                       userId:
 *                         type: string
 *                       absence:
 *                         type: boolean
 *                       report:
 *                         type: object
 *                         properties:
 *                           reportId:
 *                             type: string
 *                           status:
 *                             type: string
 *                           reportLink:
 *                             type: string
 *                           description:
 *                             type: string
 *                           createdAt:
 *                             type: string
 *                             format: date-time
 *                 school:
 *                   type: string
 *                 placeOrRoom:
 *                   type: string
 *                 sessionDate:
 *                   type: object
 *                   properties:
 *                     startDate:
 *                       type: string
 *                       format: date-time
 *                     endDate:
 *                       type: string
 *                       format: date-time
 *                     month:
 *                       type: integer
 *                     dayOfTheWeek:
 *                       type: integer
 *                     startHour:
 *                       type: string
 *                     endHour:
 *                       type: string
 *                 lastUpdatedAt:
 *                   type: string
 *                   format: date-time
 *                 report:
 *                   type: object
 *                   properties:
 *                     reportId:
 *                       type: string
 *                     status:
 *                       type: string
 *                     reportLink:
 *                       type: string
 *                     description:
 *                       type: string
 *                     createdAt:
 *                       type: string
 *                       format: date-time
 *                 reportFromBigBlueButton:
 *                   type: object
 *                   properties:
 *                     meetingID:
 *                       type: string
 *                 updatingFutureSessions:
 *                   type: boolean
 *                 recurrence:
 *                   type: object
 *                   properties:
 *                     recurrenceNumber:
 *                       type: integer
 *                     recurrenceName:
 *                       type: string
 *                 scheduleDuringHolidays:
 *                   type: boolean
 *                 meetingRoom:
 *                   type: object
 *                   properties:
 *                     sessionsUrl:
 *                       type: string
 *                     meetingID:
 *                       type: string
 *                 createdBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *                 lastUpdatedBy:
 *                   type: object
 *                   properties:
 *                     userId:
 *                       type: string
 *                     fullName:
 *                       type: string
 *                     email:
 *                       type: string
 *                     userRole:
 *                       type: string
 *       '401':
 *         description: User not authenticated
 *       '500':
 *         description: Internal server error
 */
router.put(
  "/session",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    const form = new formidable.IncomingForm();
    form.parse(req, async (err, fields, files) => {
      try {
        let sessionData = fields.sessionData;
        logger.newLog(logger.getLoggerTypeConstants().sessions).info({
          message: `update session`,
          request: {
            requestUrl: req.originalUrl,
            requestMethod: req.method,
            requestBody: sessionData,
          },
          createdBy: req.userId,
        });
        const authUserId = req.header("userId");
        const authUser = await userAdminHelper.getUserAdministrationByUserId(
          authUserId
        );
        if (authUser && !authUser.message) {
          const newError = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_FAILED,
            `User not authenticated`,
            `Authentication failed. Please try again.`,
            401
          );
          return res.status(401).json(newError);
        }

        // check if sessionData is provided
        if (!sessionData) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `sessionData is required`,
            `Please provide a sessionData`,
            404
          );
          return res.status(404).json(response);
        }

        sessionData = JSON.parse(sessionData);
        const sessionId = sessionData.sessionId;
        // check if sessionId is provided
        if (!sessionId) {
          let response = apiResponse.responseWithStatusCode(
            apiResponse.apiConstants.API_REQUEST_SUCCESS,
            `sessionId is required`,
            `Please provide a sessionId`,
            404
          );
          return res.status(404).json(response);
        }
        checkAvailability(sessionData, sessionData.tutors[0].userId, true).then(
          async (result) => {
            logger
              .newLog(logger.getLoggerTypeConstants().sessions)
              .info(
                `Disponibilité du tuteur ${sessionData.tutors[0].userId} : ${result.tutorAvailable}, Disponibilité de  l'elèves ${result.studentavailable}`
              );
            // console.log("Disponibilité du tuteur :", result.tutorAvailable);
            // console.log("Élèves non disponibles :", result.studentavailable);
            if (result.tutorAvailable && result.studentavailable) {
              //call sessionHelper function to update session
              const updatedSession = await sessionsHelper.updateSession(
                sessionId,
                sessionData,
                {
                  email: authUser.data.userData.contactDetails.email,
                  fullName: `${authUser.data.userData.contactDetails.firstName} ${authUser.data.userData.contactDetails.lastName}`,
                  userId: authUser.data.userData.userId,
                  userRole: authUser.data.userData.userRole,
                }
              );
              // console.log("updatedSession ==>", updatedSession)
              if (sessionData.updatingFutureSessions ||  sessionData.status == "canceled") {
                await sessionsHelper.updateMatchingPreferences(sessionData);
              }

              if (updatedSession.status) {
                let response = apiResponse.responseWithStatusCode(
                  apiResponse.apiConstants.API_REQUEST_SUCCESS,
                  `Session updated successfully`,
                  updatedSession.data,
                  200
                );
                return res.status(200).json(response);
              } else {
                let response = apiResponse.responseWithStatusCode(
                  apiResponse.apiConstants.API_REQUEST_FAILED,
                  `Error updating session`,
                  updatedSession.message,
                  401
                );
                return res.status(401).json(response);
              }
            } else {
              const newError = apiResponse.responseWithStatusCode(
                apiResponse.apiConstants.API_REQUEST_FAILED,
                `tutor or student(s) is not available`,
                `Disponibilité du tuteur ${sessionData.tutors[0].userId} : ${result.tutorAvailable}, Disponibilité de  l'elèves ${result.studentavailable}`,
                404
              );
              return res.status(401).json(newError);
            }
          }
        );
      } catch (error) {
        console.log(error);
        return res
          .status(500)
          .json(
            apiResponse.responseWithStatusCode(
              apiResponse.apiConstants.API_REQUEST_FAILED,
              `Error updating session`,
              error,
              500
            )
          );
      }
    });
  }
);
//delete session
/**
 * @swagger
 * /api/v1/session:
 *   delete:
 *     summary: Cancel a session
 *     tags: [Sessions]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         required: true
 *         description: The ID of the session to cancel
 *         schema:
 *           type: string
 *       - in: query
 *         name: cancelingReason
 *         required: false
 *         description: The canceling Reason of session to cancel
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Session canceled successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Confirmation message
 *       '400':
 *         description: Session ID is required
 *       '500':
 *         description: Internal server error
 */
router.delete(
  "/session",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    const request = {
      requestUrl: req.originalUrl,
      requestMethod: req.method,
      requestParams: req.params,
      requestQuery: req.query,
    };
    try {
      const data = {
        sessionId: req.query.sessionId,
        cancelingReason: req.query.cancelingReason,
        cancelingDescription: req.query.cancelingDescription,
        userId: req.userId,
        userRole: req.userRole,
      };
      //check if sessionId is provided
      if (!data.sessionId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `sessionId is required`,
          `Please provide a sessionId`
        );
        return res.status(200).json(response);
      }

      //call sessionHelper function to delete session
      const deletedSession = await sessionsHelper.deleteSession(data);

      if (deletedSession.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Session deleted successfully`,
          `Don't worry, you can always create a new session :)`
        );
        logger.newLog(logger.getLoggerTypeConstants().sessions).info({
          message: `Sessions Deletion`,
          request,
          payload: data,
        });

        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error deleting session`,
          deletedSession.message
        );
        logger.newLog(logger.getLoggerTypeConstants().sessions).error({
          message: `Error deleting session`,
          request,
          payload: data,
        });
        return res.status(200).json(response);
      }
    } catch (error) {
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting session`,
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().sessions)
        .error(response + newError);
      return res.status(500).json(newError);
    }
  }
);

//delete session in batch
/**
 * @swagger
 * /api/v1/session/batchDelete:
 *   delete:
 *     summary: Batch delete sessions
 *     tags: [Sessions]
 *     parameters:
 *       - in: query
 *         name: sessionIds
 *         required: true
 *         description: An array of session IDs to delete
 *         schema:
 *           type: array
 *           items:
 *             type: string
 *     responses:
 *       '200':
 *         description: Sessions deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   description: Confirmation message
 *       '400':
 *         description: At least one session ID is required
 *       '500':
 *         description: Internal server error
 */
router.delete(
  "/session/batchDelete",
  [
    verifyApiKey,
    checkIfAuthenticated,
    checkIfAuthorized([SUPER_ADMINISTRATOR, COORDINATOR, MANAGER, VSC]),
  ],
  async (req, res) => {
    const request = {
      requestUrl: req.originalUrl,
      requestMethod: req.method,
      requestParams: req.params,
      requestQuery: req.query,
    };
    try {
      const sessionIds = JSON.parse(req.query.sessionIds);
      //check if sessionId is provided
      if (!sessionIds.length) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `sessionId is required`,
          `Please provide at least sessionId`
        );
        return res.status(200).json(response);
      }
      //get the parent session id to delete it with the child sessions
      const { parentSessionId } = await SessionsModel.findOne({
        sessionId: sessionIds[0],
      });
      //call sessionHelper function to delete session
      const deletedSessions = await sessionsHelper.batchDeleteSessions(
        [...sessionIds, parentSessionId],
        true
      );

      if (deletedSessions.status) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Session deleted successfully`,
          `Don't worry, you can always create a new session :)`
        );
        logger.newLog(logger.getLoggerTypeConstants().sessions).info({
          message: `Sessions Deletion`,
          request,
          payload: {
            createdBy: req.userId,
            sessionIds,
          },
        });
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Error deleting session`,
          deletedSessions.message
        );
        logger.newLog(logger.getLoggerTypeConstants().sessions).error({
          message: `Error deleting session`,
          request,
          payload: {
            createdBy: req.userId,
            sessionIds,
          },
        });
        return res.status(200).json(response);
      }
    } catch (error) {
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error deleting session`,
        error
      );
      logger.newLog(logger.getLoggerTypeConstants().sessions).error({
        message: `Error deleting session`,
        request,
        error: error,
        payload: {
          createdBy: req.userId,
          sessionIds,
        },
      });
      return res.status(500).json(newError);
    }
  }
);

//generate Join URL for a session based on sessionId and userId
/**
 * @swagger
 * /api/v1/session/join-url:
 *   get:
 *     summary: Get the join URL for a session
 *     tags: [Sessions]
 *     parameters:
 *       - in: query
 *         name: sessionId
 *         required: true
 *         description: The ID of the session
 *         schema:
 *           type: string
 *       - in: query
 *         name: userId
 *         required: true
 *         description: The ID of the user joining the session
 *         schema:
 *           type: string
 *     responses:
 *       '200':
 *         description: Join URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 joinURL:
 *                   type: string
 *                   description: The generated join URL for the session
 *       '400':
 *         description: Bad request, either sessionId or userId is missing
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/session/join-url",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const sessionId = req.query.sessionId;
      const userId = req.query.userId;

      //check if sessionId is provided
      if (!sessionId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `SessionId is required`,
          `Please provide a sessionId`
        );
        return res.status(200).json(response);
      }
      //check if userId is provided
      if (!userId) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `UserId is required`,
          `Please provide a userId`
        );
        return res.status(200).json(response);
      }

      //get User details
      const userDocument = await userHelper.getUserDetailsByUserId(userId);
      //check if user exists
      if (!userDocument) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `User does not exist`,
          `Please check the userId and try again`,
          apiResponse.apiConstants.USER_DOES_NOT_EXISTS
        );
        return res.status(200).json(response);
      }

      //call sessionHelper function to check if session exists and verify if user is a participant, if Yes get Session details
      let sessionDetails = await sessionsHelper.verifySessionAccess(
        userDocument,
        sessionId
      );

      //check if session exists
      if (
        sessionDetails.status ===
        apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS
      ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Session or user does not exist`,
          `Please check the sessionId and userId and try again`,
          apiResponse.apiConstants.SESSIONS_DOES_NOT_EXISTS
        );
        return res.status(200).json(response);
      }

      //get BigBlueButton room details
      const meetingID = sessionDetails.data.meetingRoom.meetingID;
      //check if meeting room exists in BigBlueButton Server
      const meetingRoomExists = await bigBlueButtonHelper.checkIfRoomIsRunning(
        meetingID
      );

      //check if meeting room exists in BigBlueButton Server and match with the meeting room in the database
      if (meetingRoomExists?.meetingID !== meetingID) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Meeting room does not exist`,
          `Please check the meeting room details and try again`
        );
        return res.status(400).json(response);
      }

      const meetingRoomData = await bigBlueButtonHelper.getRoomByMeetingID(
        meetingID
      );
      //get meeting room details from bbb server

      setTimeout(async () => {
        try {
          const meetingInfos = await bigBlueButtonHelper.getMeetingInfo(
            meetingID
          );
          if (meetingInfos.returncode === "SUCCESS") {
            const meetingInfoExists = await sessionReporting.findOne({
              meetingID: meetingID,
            });
            if (!meetingInfoExists) {
              const newMeetingInfo = new sessionReporting({
                meetingID: meetingInfos.meetingID,
                createDate: meetingInfos.createDate,
                startTime: meetingInfos.startTime,
                participantCount: meetingInfos.participantCount,
                attendees: meetingInfos.attendees.attendee,
              });
              await newMeetingInfo.save();
            } else {
              await sessionReporting.findOneAndUpdate(
                { meetingID: meetingID },
                {
                  $set: {
                    participantCount: meetingInfos.participantCount,
                    attendees: meetingInfos.attendees.attendee,
                  },
                },
                { new: true }
              );
            }
          }
        } catch (error) {
          console.error("Error fetching meeting info:", error);
        }
      }, 10000);
      //if sessionDetails.userDocument is null fill value with userDocument
      const userDetails = sessionDetails.userDocument
        ? sessionDetails.userDocument
        : userDocument;

      //generate join URL for the session based on sessionId and userId, call bigBlueButtonHelper function
      const joinURL = bigBlueButtonHelper.generateJoinURL(
        meetingRoomData,
        userDetails
      );

      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Join URL generated successfully`,
        { joinURL }
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error generating a new session 0`,
        JSON.stringify(error)
      );
      logger.newLog(logger.getLoggerTypeConstants().sessions).error(newError);
      return res.status(500).json(newError);
    }
  }
);

//test route to test recuring sessions
/**
 * @swagger
 * /api/v1/session/test:
 *   get:
 *     summary: Test endpoint to generate array of dates for recurring sessions
 *     tags: [Sessions]
 *     responses:
 *       '200':
 *         description: Array of dates generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 arrayOf:
 *                   type: array
 *                   description: The generated array of dates
 *                   items:
 *                     type: string
 *                     format: date-time
 *       '500':
 *         description: Internal server error
 */
router.get(
  "/session/test",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    const tutorUserId = "zu-c762bead-c9d3-4ab1-941b-b5a46f2d8af8";
    const sessionData = {
      startDate: "2023-04-19T15:00:00.000Z",
      endDate: "2023-04-19T16:00:00.000Z",
      month: 4,
      dayOfTheWeek: 0,
      startHour: 17,
      endHour: 18,
    };
    const recurringType = "bi-monthly";
    const arrayOf =
      await sessionsHelper.getArrayOfDatesBetweenTwoDatesForRecurringSession(
        tutorUserId,
        sessionData,
        recurringType
      );

    let response = apiResponse.responseWithStatusCode(
      apiResponse.apiConstants.API_REQUEST_SUCCESS,
      `Array of dates generated successfully`,
      arrayOf
    );
    return res.status(200).json(response);
  }
);
router.get(
  "/session/generate-google-meet-url",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const joinURL = await sessionsHelper.createMeetEvent();
      let response = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_SUCCESS,
        `Join URL generated successfully`,
        { joinURL }
      );
      return res.status(200).json(response);
    } catch (error) {
      console.log(error);
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error generating a new session 0`,
        JSON.stringify(error)
      );
      return res.status(500).json(newError);
    }
  }
);
router.get(
  "/session/byTutor",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([])],
  async (req, res) => {
    try {
      const tutorId = req.query.tutorId;
      const studentId = req.query.studentId;

      //check if sessionId is provided
      if (!tutorId && !studentId ) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `tutorId and studentId is required`,
          `Please provide a tutorId and studentId `
        );
        return res.status(200).json(response);
      }

      const session = await sessionsHelper.getSessionByTuorIdStudentId({tutorId, studentId});
      console.log("session", session)
      if (session.data) {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Sessions found`,
          session.data,
          200
        );
        return res.status(200).json(response);
      } else {
        let response = apiResponse.responseWithStatusCode(
          apiResponse.apiConstants.API_REQUEST_SUCCESS,
          `Sessions not found`,
          `No session found with this studentId and tutorId, please check tutorId and studentId`
        );
        return res.status(200).json(response);
      }
    } catch (error) {
      let newError = apiResponse.responseWithStatusCode(
        apiResponse.apiConstants.API_REQUEST_FAILED,
        `Error getting sessions dashboard`,
        error
      );
      logger
        .newLog(logger.getLoggerTypeConstants().sessions)
        .error(response + newError);
      return res.status(500).json(newError);
    }
  }
);

// Route pour les statistiques des séances
router.get(
  "/session/stats",
  [verifyApiKey, checkIfAuthenticated, checkIfAuthorized([SUPER_ADMINISTRATOR, VSC, COORDINATOR])],
  async (req, res) => {
    try {
      const { coordinatorId } = req.query;
      const payload = {
        userId: req.user.userId,
        userRole: req.user.userRole,
        coordinatorId
      };

      const result = await sessionsHelper.getSessionsStats(payload);
      res.status(result.status ? 200 : 400).json(result);
    } catch (error) {
      console.log(error);
      res.status(500).json({
        status: false,
        message: "Erreur lors de la récupération des statistiques",
        data: error
      });
    }
  }
);

//export router
module.exports = router;
