const { v4: uuidv4 } = require("uuid");

const userConstants = require("../../utils/constants/user.const.js");

//import user model
const User = require("../../models/User.Models.js");

//import UserAdministration.Preferences.Model.js
const userAdministrationModel = require("../../models/User.Administration.Model.js");
const { empty } = require("uuidv4");

//import Establishment.Model.js
const establishmentModel = require("../../models/Establishment.Model.js");

//import Sector.Model.js
const sectorModel = require("../../models/Sectors.Model.js");

const programConstants = require("../../utils/constants/program.constants.js");

const userRoleConstants = require("../../utils/constants/userRolesConstants.js");

const studentPreferenceModel = require("../../models/Student.Preferences.Model.js");

const parentPreferenceModel = require("../../models/Parent.Preferences.Model.js");

const tutorPreferenceModel = require("../../models/Tutor.Preferences.Model.js");
const jwt = require("jsonwebtoken");
const axios = require("axios");
// URL pour récupérer la clé publique de Firebase (exemple pour Firebase)
const firebaseCertsUrl =
  "https://www.googleapis.com/robot/v1/metadata/x509/<EMAIL>";

//Generate guid userID for user registration
exports.generateNewUserId = () => {
  let newUserId = userConstants.USER_ID_GUID_DEFAULT_THREE_CHAR + uuidv4();
  return newUserId;
};

//check if user exists in database by identifier in firebaseIds
exports.checkIfUserExists = async (identifier) => {
  try {
    if (identifier === undefined || identifier === null || identifier === "") {
      return null;
    }
    let user = await User.findOne({
      "firebaseIds.identifier": { $eq: identifier },
    }).exec();
    return user;
  } catch (err) {
    console.log(err);
    return null;
  }
};

//get userDetails object by userId
exports.getUserDetailsByUserId = async (userId) => {
  try {
    let user = await User.findOne({ userId: { $eq: userId } }).exec();
    let userAdmin = await userAdministrationModel
      .findOne({ userId: { $eq: userId } })
      .exec();
    if (user) {
      return user;
    }
    if (userAdmin) {
      return userAdmin;
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

//check if parentUserId is matching with one of the children
exports.checkIfParentUserIdIsMatchingWithChildren = async (parentUserId) => {
  try {
    let childrenUserData = await studentPreferenceModel
      .find({ parentUserId: { $eq: parentUserId } })
      .exec();

    //return list of children user Ids
    let childrenUserIds = [];
    childrenUserData.forEach((element) => {
      childrenUserIds.push(element.userId);
    });
    return childrenUserIds;
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message };
  }
};

//get all user  based on userRole
exports.getAllUsersByUserRole = async (userRole) => {
  try {
    let user = await User.find({ userRole: { $eq: userRole } }).exec();
    return { status: true, data: user };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//get all user  based on userRole and status: active or inactive
exports.getAllUsersByUserRoleAndStatus = async (userRole, status) => {
  try {
    let user = await User.find({
      userRole: { $eq: userRole },
      status: { $eq: status },
    }).exec();
    return { status: true, data: user };
  } catch (error) {
    return { status: false, message: error.message };
  }
};

//get All users based on userRole, and get details from UserAdministration.Preferences.Model.js
exports.getAllAdminUsersByUserRole = async (userRole) => {
  try {
    let user = await User.find({ userRole: { $eq: userRole } }).exec();
    let adminUserDetails = [];
    for (let i = 0; i < user.length; i++) {
      const userDocument = user[i]._doc;
      const userId = userDocument.userId;
      let userAdminPref = await userAdministrationModel
        .findOne({ userId: { $eq: userId } })
        .exec();

      //merge user and userDetails
      let combinedUserDetails;
      if (userAdminPref) {
        combinedUserDetails = { ...userDocument, ...userAdminPref._doc };
      } else {
        combinedUserDetails = { ...userDocument };
      }
      adminUserDetails.push(combinedUserDetails);
    }
    return { status: true, data: adminUserDetails };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

// Get user details from FirebaseUserId
exports.getUserDetailsFromFirebaseIdentifier = async (
  firebaseIdentifier,
  role
) => {
  try {
    const emailRegex = new RegExp(
      `^${firebaseIdentifier.replace(/[-\/\\^$.*+?()[\]{}|]/g, "\\$&")}$`,
      "i"
    ); // 'i' pour insensibilité à la casse
    let user = await User.findOne({
      $or: [
        { "firebaseIds.identifier": { $regex: emailRegex }, userRole: role },
        { "contactDetails.phoneNumber": firebaseIdentifier, userRole: role },
      ],
    }).exec();
    return user;
  } catch (error) {
    console.log(error);
    return null;
  }
};

//checkIfTheUserExistsInDB based on firebaseIdentifier
exports.checkIfTheFirebaseIdentifierExist = async (
  firebaseIdentifier,
  userRole
) => {
  let user;
  try {
    if (userRole === userRoleConstants.ROLE_STUDENT) {
      user = await User.findOne({
        userRole: { $eq: userRole },
        "firebaseIds.identifier": { $eq: firebaseIdentifier },
      }).exec();
    } else {
      //first check if user exists in external user
      user = await User.findOne({
        "firebaseIds.identifier": { $eq: firebaseIdentifier },
      }).exec();
    }

    if (user) {
      return { status: true, message: "User exists", data: user };
    }

    //if user does not exist in external user, check if user exists in internal user
    let internalUser = await userAdministrationModel
      .findOne({ "firebaseIds.identifier": { $eq: firebaseIdentifier } })
      .exec();
    if (internalUser) {
      return { status: true, message: "User exists", data: internalUser };
    }

    return { status: false, message: "User does not exist", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
exports.checkIfStudentEmailIsAlreadyRegistered = async (email) => {
  try {
    if (!email) {
      return { status: false, message: "Email is not registered", data: null };
    }

    let user = await User.findOne({
      userRole: userRoleConstants.ROLE_STUDENT,
      $or: [
        { "contactDetails.email": email },
        { "firebaseIds.identifier": email },
      ],
    }).exec();

    // //check if email is already exist or not in firebaseIds.identifier
    // let emailInFirebaseIds = await User.findOne({
    //   "firebaseIds.identifier": { $eq: email },
    // }).exec();

    if (user) {
      return {
        status: true,
        message: "Email is already registered",
        data: null,
      };
    }
    return { status: false, message: "Email is not registered", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
exports.checkIfStudentPhoneAlreadyRegistered = async (phone) => {
  try {
    console.log("phone", phone);
    if (!phone) {
      return { status: false, message: "phone is not registered", data: null };
    }
    let user = await User.findOne({
      userRole: userRoleConstants.ROLE_STUDENT,
      $or: [{ "contactDetails.phone": phone }],
    }).exec();
    if (user) {
      return {
        status: true,
        message: "phone is already registered",
        data: null,
      };
    }
    return { status: false, message: "phone is not registered", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};
//check if the email and first name and last name exists in the database
exports.checkIfTheEmailAndFirstAndLastNameExist = async (
  email,
  firstName,
  lastName,
  userRole,
  phoneNumber
) => {
  try {
    let isUserExists = false;

    if (email) {
      let user = await User.findOne({
        "contactDetails.email": { $eq: email },
        userRole: { $eq: userRole },
      }).exec();
      if (user) {
        isUserExists = true;
      }

      let internalUser = await userAdministrationModel
        .findOne({
          "contactDetails.email": { $eq: email },
          userRole: { $eq: userRole },
        })
        .exec();
      if (internalUser) {
        isUserExists = true;
      }
    }

    if (firstName && lastName) {
      let user = await User.findOne({
        "contactDetails.firstName": { $eq: firstName },
        "contactDetails.lastName": { $eq: lastName },
        userRole: { $eq: userRole },
      }).exec();
      if (user) {
        isUserExists = true;
      }

      let internalUser = await userAdministrationModel
        .findOne({
          "contactDetails.firstName": { $eq: firstName },
          "contactDetails.lastName": { $eq: lastName },
          userRole: { $eq: userRole },
        })
        .exec();
      if (internalUser) {
        isUserExists = true;
      }
    }

    if (phoneNumber) {
      phoneNumber = encodeURIComponent(phoneNumber);

      //replace %20 with + in firebaseIdentifier
      phoneNumber = phoneNumber.replace(/%20/g, "+");

      //remove + from phoneNumber
      phoneNumber = phoneNumber.replace(/\+/g, "");
      console.log(phoneNumber);
      let user = await User.findOne({
        "contactDetails.phoneNumber": { $eq: phoneNumber },
        userRole: { $eq: userRole },
      }).exec();
      if (user) {
        isUserExists = true;
      }

      let internalUser = await userAdministrationModel
        .findOne({
          "contactDetails.phoneNumber": { $eq: phoneNumber },
          userRole: { $eq: userRole },
        })
        .exec();
      if (internalUser) {
        isUserExists = true;
      }
    }
    console.log(isUserExists);

    return { status: true, message: "User exists", data: isUserExists };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//get user details from email in contactDetails.email
exports.getUserDetailsFromEmail = async (email) => {
  try {
    const userDocument = await User.findOne({
      "contactDetails.email": email,
    }).exec();
    return userDocument;
  } catch (error) {
    return null;
  }
};

//get fullNames from userId
exports.getFullNamesFromUserId = async (userId) => {
  try {
    const contactDetails = await User.findOne({ userId: { $eq: userId } })
      .select("contactDetails profilePic")
      .exec();
    if (contactDetails) {
      return (
        contactDetails.contactDetails.firstName +
        " " +
        contactDetails.contactDetails.lastName
      );
    } else {
      return null;
    }
  } catch (error) {
    console.log(error);
    return null;
  }
};

//get fullNames from userId and profilePic
exports.getStudentProfile = async (userId) => {
  try {
    const contactDetails = await User.findOne({ userId: { $eq: userId } })
      .select("contactDetails profilePic")
      .exec();

    if (contactDetails) {
      const studentProfile = {
        fullName:
          contactDetails.contactDetails.firstName +
          " " +
          contactDetails.contactDetails.lastName,
        profilePic: contactDetails.profilePic ? contactDetails.profilePic : "",
        userId: userId,
        phoneNumber: contactDetails.contactDetails.phoneNumber,
        email: contactDetails.contactDetails.email,
      };
      return { status: true, data: studentProfile };
    } else {
      return { status: false, message: "Student not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

exports.getStudentProfileAndLevel = async (userId) => {
  try {
    const studentDocument = await User.findOne({
      userId: { $eq: userId },
      userRole: { $eq: userRoleConstants.ROLE_STUDENT },
    }).exec();

    if (!studentDocument) {
      return { status: false, message: "Student not found", data: null };
    }

    //get student level from studentPreferenceModel
    const studentPreferenceDocument = await studentPreferenceModel
      .findOne({ userId: { $eq: userId } })
      .exec();

    if (!studentPreferenceDocument) {
      return {
        status: false,
        message: "Student preference not found",
        data: null,
      };
    }
    let levelNames = [];
    const levelsList = studentPreferenceDocument.assignment.level;
    const establishment = studentPreferenceDocument.assignment.establishments;
    if (levelsList.length > 0) {
      levelNames = levelsList.map((level) => {
        return level.levelName;
      });
    } else {
      levelNames = [];
    }

    const studentProfile = {
      createdAt: studentDocument.createdAt,
      fullName:
        studentDocument.contactDetails.firstName +
        " " +
        studentDocument.contactDetails.lastName,
      department: studentPreferenceDocument.assignment.department,
      profilePic: studentDocument.profilePic ? studentDocument.profilePic : "",
      userId: userId,
      email: studentDocument.contactDetails.email,
      phoneNumber: studentDocument.contactDetails.phoneNumber
        ? studentDocument.contactDetails.phoneNumber
        : "",
      levelNames: levelNames,
      parentUserId: studentPreferenceDocument.parentUserId,
      studentEstablishment: establishment || [],
    };
    return { status: true, data: studentProfile };
  } catch (error) {
    console.log(error);
    return null;
  }
};

exports.getStudentParentDetails = async (userId) => {
  try {
    const parent = await User.findOne({ userId: { $eq: userId } }).exec();
    if (parent) {
      return { status: true, data: parent.contactDetails };
    } else {
      return { status: false, message: "Parent not found", data: null };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//check if identifier is email or phone number
exports.checkIdentifierType = (identifier) => {
  let identifierType = "";
  if (identifier.includes("@")) {
    return userConstants.USER_IDENTIFIER_EMAIL;
  } else {
    return userConstants.USER_IDENTIFIER_PHONE_NUMBER;
  }
};

//check identifier is email or phone number and return boolean
exports.checkIdentifierIsEmail = (identifier) => {
  if (identifier.includes("@")) {
    return true;
  } else {
    return false;
  }
};

//check if identifier exists in database
exports.checkIfIdentifierExistsInDb = async (identifier, firebaseIds) => {
  try {
    if (firebaseIds !== null) {
      let statusOfIdentifier = false;
      firebaseIds.forEach((element) => {
        if (element.identifier == identifier) {
          statusOfIdentifier = true;
        }
      });
      return statusOfIdentifier;
    } else {
      return false;
    }
  } catch (error) {
    console.log(error);
    return false;
  }
};

// return contact details object
exports.getContactDetailsObject = async (identifier, contactDetails) => {
  let identifierType = this.checkIdentifierType(identifier);

  if (identifierType === userConstants.USER_IDENTIFIER_EMAIL) {
    //check if contactDetails object is empty, if no return phone number
    let phoneNumber = "";
    if (contactDetails !== null) {
      phoneNumber =
        contactDetails.phoneNumber === undefined
          ? ""
          : contactDetails.phoneNumber;
    }

    let newContactDetails = {
      email: identifier,
      phoneNumber: phoneNumber,
    };
    return newContactDetails;
  } else {
    //check if contactDetails object is empty, if no return email
    let email = "";
    if (contactDetails !== null) {
      email = contactDetails.email === undefined ? "" : contactDetails.email;
    }
    let newContactDetails = {
      email: email,
      phoneNumber: identifier,
    };
    return newContactDetails;
  }
};

//get userId from firebaseIdentifier
exports.getUserIdFromFirebaseIdentifier = async (firebaseIdentifier) => {
  try {
    let user = await User.findOne({
      "firebaseIds.identifier": { $eq: firebaseIdentifier },
    }).exec();
    return user.userId;
  } catch (error) {
    console.log(error);
    return null;
  }
};

//function addNewUserAdministration
exports.addNewUserAdministration = async (userAdministrationData) => {
  try {
    userAdministrationData = JSON.parse(userAdministrationData);
    //create new user in database
    const userId = this.generateNewUserId();
    const contactDetails = {
      firstName: userAdministrationData.firstName,
      lastName: userAdministrationData.lastName,
      email: userAdministrationData.email ? userAdministrationData.email : "",
      phoneNumber: userAdministrationData.phoneNumber
        ? userAdministrationData.phoneNumber
        : "",
    };
    let user = await User.create({
      userId: userId,
      contactDetails: contactDetails,
      dateOfBirth: userAdministrationData.dateOfBirth,
      gender: userAdministrationData.gender,
      userRole: userAdministrationData.userRole,
    });
    let response = await user.save();

    let administrationPreferences =
      userAdministrationData.userAdministrationPreferences;

    //create new userAdministration Preferences in database
    let userAdministration = await userAdministrationModel.create({
      userId: userId,
      status: administrationPreferences.status
        ? administrationPreferences.status
        : "active",
      program: administrationPreferences.program
        ? administrationPreferences.program
        : "",
      sector: administrationPreferences.sector
        ? administrationPreferences.sector
        : "",
      establishment: administrationPreferences.establishment
        ? administrationPreferences.establishment
        : "",
      comments: administrationPreferences.comments
        ? administrationPreferences.comments
        : "",
      studyLevel: administrationPreferences.studyLevel
        ? administrationPreferences.studyLevel
        : "",
      availability: administrationPreferences.availability
        ? administrationPreferences.availability
        : null,
      commitmentStartDate: administrationPreferences.commitmentStartDate
        ? administrationPreferences.commitmentStartDate
        : "",
      commitmentEndDate: administrationPreferences.commitmentEndDate
        ? administrationPreferences.commitmentEndDate
        : "",
      preemptiveEndDate: administrationPreferences.preemptiveEndDate
        ? administrationPreferences.preemptiveEndDate
        : "",
      motiveForPreemptiveEnd: administrationPreferences.motiveForPreemptiveEnd
        ? administrationPreferences.motiveForPreemptiveEnd
        : "",
      coordinators: administrationPreferences.coordinators
        ? administrationPreferences.coordinators
        : [],
    });
    let responseAdministration = await userAdministration.save();

    //combine student preferences and user data merged
    let updateAdminUser = { ...response._doc, ...responseAdministration._doc };

    return {
      status: true,
      message: "User created successfully",
      data: updateAdminUser,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//update user administration
exports.updateUserAdministration = async (userAdministrationData) => {
  try {
    //get old data from database
    userAdministrationData = JSON.parse(userAdministrationData);

    const userAdministrationOldData = await User.findOne({
      userId: userAdministrationData.userId,
    }).exec();

    //update user in database
    const contactDetails = {
      firstName: userAdministrationData.firstName
        ? userAdministrationData.firstName
        : userAdministrationOldData.contactDetails.firstName,
      lastName: userAdministrationData.lastName
        ? userAdministrationData.lastName
        : userAdministrationOldData.contactDetails.lastName,
      email: userAdministrationData.email
        ? userAdministrationData.email
        : userAdministrationOldData.contactDetails.email,
      phoneNumber: userAdministrationData.phoneNumber
        ? userAdministrationData.phoneNumber
        : userAdministrationOldData.contactDetails.phoneNumber,
    };

    userAdministrationOldData.contactDetails = contactDetails;
    userAdministrationOldData.dateOfBirth = userAdministrationData.dateOfBirth
      ? userAdministrationData.dateOfBirth
      : userAdministrationOldData.dateOfBirth;
    userAdministrationOldData.gender = userAdministrationData.gender
      ? userAdministrationData.gender
      : userAdministrationOldData.gender;
    userAdministrationOldData.userRole = userAdministrationData.userRole
      ? userAdministrationData.userRole
      : userAdministrationOldData.userRole;

    //save user in database
    const updatedUser = await userAdministrationOldData.save();

    let newUserAdministrationData =
      userAdministrationData.userAdministrationPreferences;

    //update userAdministration Preferences in database
    let administrationPreferences = await userAdministrationModel
      .findOneAndUpdate(
        { userId: userAdministrationData.userId },
        newUserAdministrationData,
        { new: true }
      )
      .exec();

    // administrationPreferences.status = newUserAdministrationData.status ? newUserAdministrationData.status : administrationPreferences.status;
    // administrationPreferences.program = newUserAdministrationData.program ? newUserAdministrationData.program : administrationPreferences.program;
    // administrationPreferences.sector = newUserAdministrationData.sector ? newUserAdministrationData.sector : administrationPreferences.sector;
    // administrationPreferences.establishment = newUserAdministrationData.establishment ? newUserAdministrationData.establishment : administrationPreferences.establishment;
    // administrationPreferences.comments = newUserAdministrationData.comments ? newUserAdministrationData.comments : administrationPreferences.comments;
    // administrationPreferences.studyLevel = newUserAdministrationData.studyLevel ? newUserAdministrationData.studyLevel : administrationPreferences.studyLevel;

    //save userAdministration Preferences in database
    // const updatedUserAdministration = await administrationPreferences.save();

    //combine student preferences and user data merged
    let adminData = { ...updatedUser._doc, ...administrationPreferences._doc };

    return {
      status: true,
      message: "User created successfully",
      data: adminData,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//get userAdministration by userId
exports.getUserAdministrationByUserId = async (userId) => {
  try {
    //get User Details
    const userData = await User.findOne({ userId: userId }).exec();

    //get User Administration Preferences Details
    const userAdministrationPreferences = await userAdministrationModel
      .findOne({ userId: userId })
      .exec();

    //get Establishment list, establishmentId, establishmentName
    const establishmentList = await establishmentModel.find({}).exec();
    const modifiedEstablishmentList = establishmentList.map((establishment) => {
      return {
        establishmentId: establishment._id,
        establishmentName: establishment.establishmentName,
      };
    });

    //get Sector list, sectorId, sectorName
    const sectorList = await sectorModel.find({}).exec();
    const modifiedSectorList = sectorList.map((sector) => {
      return {
        sectorId: sector._id,
        sectorName: sector.name,
      };
    });

    //get Coordinator list, coordinatorId, coordinatorName
    const coordinatorList = await User.find({
      userRole: userRoleConstants.COORDINATOR,
    }).exec();

    //return just userId and name
    const modifiedCoordinatorList = coordinatorList.map((coordinator) => {
      return {
        userId: coordinator.userId,
        coordinatorName:
          coordinator.contactDetails.firstName +
          " " +
          coordinator.contactDetails.lastName,
      };
    });

    //get Program list, programId, programName
    const programList = programConstants;

    let userDetailsData = {
      ...(userData && userData._doc ? userData._doc : {}),
      ...(userAdministrationPreferences && userAdministrationPreferences._doc
        ? userAdministrationPreferences._doc
        : {}),
    };

    let listOfOptions = {
      establishmentList: modifiedEstablishmentList,
      sectorList: modifiedSectorList,
      programList: programList,
      coordinatorList: modifiedCoordinatorList,
    };

    //let combinedData = { ...userDataCombined, ...listOfOptions };
    let combinedData = { userData: userDetailsData, ...listOfOptions };

    return {
      status: true,
      message: "User created successfully",
      data: combinedData,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//check if exists user with the same first name and last name and userRole
exports.ifUserExistOnCreateWithSameNameLastName = async (
  userRole,
  firstName,
  lastName
) => {
  try {
    const userDocument = await User.findOne({
      "contactDetails.firstName": firstName,
      "contactDetails.lastName": lastName,
    }).exec();

    if (userDocument && userDocument.userRole === userRole) {
      console.log("User with the same name and last name and userRole exists");
      return {
        status: false,
        message: `User with the same name and last name and userRole ${userRole} exists`,
      };
    } else {
      console.log(
        "User with the same name and last name and userRole does not exists"
      );
      return {
        status: true,
        message: `User with the same name and last name and userRole ${userRole} exists`,
      };
    }
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, proceed: false };
  }
};
 exports.generatePassword=() => {
  const length = 12; // Longueur du mot de passe
  const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!-/:-@[-`{-~$£²€§¤µ";
  let password = "";
  let hasLower = false, hasUpper = false, hasDigit = false, hasSpecial = false;

  while (!hasLower || !hasUpper || !hasDigit || !hasSpecial || password.length < length) {
    const char = charset[Math.floor(Math.random() * charset.length)];
    password += char;
    if (/[a-z]/.test(char)) hasLower = true;
    if (/[A-Z]/.test(char)) hasUpper = true;
    if (/\d/.test(char)) hasDigit = true;
    if (/[^A-Za-z\d]/.test(char)) hasSpecial = true;
  }


  return password;
}



//check if email is already registered or not
exports.checkIfEmailIsAlreadyRegistered = async (email) => {
  try {
    if (!email) {
      return { status: false, message: "Email is not registered", data: null };
    }

    let user = await User.findOne({
      $or: [
        { "contactDetails.email": email },
        { "firebaseIds.identifier": email },
      ],
    }).exec();

    //check if email is already exist or not in firebaseIds.identifier
    let emailInFirebaseIds = await User.findOne({
      "firebaseIds.identifier": { $eq: email },
    }).exec();

    if (user || emailInFirebaseIds) {
      return {
        status: true,
        message: "Email is already registered",
        data: null,
      };
    }
    return { status: false, message: "Email is not registered", data: null };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: null };
  }
};

//check if exists user with the same first name and last name and userRole and userId
exports.ifUserExistOnUpdateWithSameNameLastName = async (
  userRole,
  firstName,
  lastName,
  userId
) => {
  try {
    const userDocument = await User.findOne({
      "contactDetails.firstName": firstName,
      "contactDetails.lastName": lastName,
      userRole: userRole,
    }).exec();
    const proceed =
      userDocument && userDocument.userId !== userId ? false : true;
    return {
      status: true,
      message: `User with the same name and last name and userRole ${userRole} exists`,
      proceed: proceed,
    };
  } catch (error) {
    console.log("Error in ifUserExistOnUpdateWithSameNameLastName", error);
    return { status: false, message: error.message, proceed: false };
  }
};

//update userStatus base on userId
exports.updateUserStatus = async (userId, status) => {
  try {
    const userDocument = await User.findOneAndUpdate(
      { userId: userId },
      { status: status },
      { new: true }
    ).exec();
    return {
      status: true,
      message: `User status updated successfully`,
      data: userDocument,
    };
  } catch (error) {
    console.log(error);
    return { status: false, message: error.message, data: error.message };
  }
};

//Check if invitation code is valid
exports.checkInvitationCodeValidity = async (invitationCode, email) => {
  try {
    const userDocument = await this.getUserDetailsFromEmail(email);

    if (!userDocument) {
      return { status: false, message: "User not found", data: null };
    }

    const userRole = userDocument.userRole;
    const userId = userDocument.userId;
    let isExistInvitationCode;
    if (userRole == userRoleConstants.ROLE_PARENT) {
      isExistInvitationCode = await parentPreferenceModel
        .findOne({
          userId: userId,
          "invitation.invitationCode": invitationCode,
        })
        .exec();
    } else if (userRole == userRoleConstants.ROLE_TUTOR) {
      isExistInvitationCode = await tutorPreferenceModel
        .findOne({
          userId: userId,
          "invitation.invitationCode": invitationCode,
        })
        .exec();
    }

    if (!isExistInvitationCode) {
      return {
        status: false,
        message: "Invitation code is not valid",
        data: null,
      };
    }

    return {
      status: true,
      message: "Invitation code is valid",
      data: invitationCode,
    };
  } catch (error) {
    console.log(error);
    return {
      status: false,
      message: "Error in checkInvitationCodeValidity",
      data: error.message,
    };
  }
};

//update firebaseIds and profilePicture
exports.updateUserAdministrationFirebaseIdsAndProfilePicture = async (
  userId,
  newFirebaseIds,
  profilePicture
) => {
  console.log("userId=>", userId);
  console.log("newFirebaseIds=>", newFirebaseIds);
  console.log("profilePicture=>", profilePicture);
  const updatedDocument = await User.findOneAndUpdate(
    { userId: userId },
    { $push: { firebaseIds: newFirebaseIds, profilePic: profilePicture } },
    { new: true }
  );
  console.log("updatedDoc", updatedDocument);
  if (!updatedDocument) {
    return { status: false, message: "User not found", data: null };
  } else {
    return {
      status: true,
      message: "User updated successfully",
      data: updatedDocument,
    };
  }
};

const getPublicKey = async () => {
  try {
    const response = await axios.get(firebaseCertsUrl);
    return response.data; // Retourne un objet avec les clés publiques
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des clés publiques Firebase:",
      error
    );
    throw new Error("Erreur lors de la récupération des clés publiques");
  }
};
exports.verifyToken = async (token) => {
  try {
    const publicKeys = await getPublicKey(); // Récupérer les clés publiques

    // Décoder l'en-tête du token pour obtenir l'ID de la clé (kid)
    const decodedHeader = jwt.decode(token, { complete: true });
    const kid = decodedHeader?.header?.kid; // ID de la clé (Key ID)

    // Vérifier si le token est expiré
    if (decodedHeader && decodedHeader.payload.exp < Date.now() / 1000) {
      console.log("Le token est expiré.");
      throw new Error("Le token est expiré", "Le token est expiré");
    } else {
      console.log("Le token n'est pas expiré.");
    }
    if (!kid) {
      throw new Error("Le token ne contient pas de kid valide.");
    }

    const publicKey = publicKeys?.[kid]; // Récupérer la clé publique correspondant au kid

    if (!publicKey) {
      throw new Error(`Clé publique non trouvée pour le kid: ${kid}`);
    }

    // Vérifier la signature du token avec la clé publique
    return new Promise((resolve, reject) => {
      jwt.verify(token, publicKey, (err, decoded) => {
        if (err) {
          reject(new Error("Erreur de vérification du token: " + err.message));
        } else {
          resolve(decoded); // Retourner le payload du token
        }
      });
    });
  } catch (error) {
    console.error("Erreur lors de la vérification du token:", error);
    throw error;
  }
};

exports.updatePasswordUserStatus = async (
  userId,
  passwordStatus,
  userRole,
  email
) => {
  try {
    const isStandardUser =
      userRole === userRoleConstants.ROLE_STUDENT ||
      userRole === userRoleConstants.ROLE_PARENT ||
      userRole === userRoleConstants.ROLE_TUTOR;
    const model = isStandardUser ? User : userAdministrationModel;

    let userDocument;

    if (userId && userId.trim() !== "") {
      userDocument = await model
        .findOneAndUpdate(
          { userId: userId },
          { hasChangedPassword: passwordStatus },
          { new: true }
        )
        .exec();
    }

    if (!userDocument) {
      userDocument = await model
        .findOneAndUpdate(
          {
            "firebaseIds.identifier": email,
          },
          { hasChangedPassword: passwordStatus }
        )
        .exec();
    }
    console.log("userDocument", userDocument);
    if (userDocument) {
      return {
        status: true,
        message: userId
          ? "User password status updated successfully"
          : `User found with email in firebaseIds: ${email}`,
        data: userDocument,
      };
    } else {
      return {
        status: false,
        message: `No user found with userId: ${
          userId || "N/A"
        } or email: ${email}`,
        data: null,
      };
    }
  } catch (error) {
    console.error(error);
    return { status: false, message: error.message, data: error };
  }
};

// module.exports = {
//      generateNewUserId,
//      checkIfUserExists,
//      checkIdentifierType,
//      getContactDetailsObject,
//      getUserDetailsByUserId,
//      checkIfIdentifierExistsInDb,
//      getUserDetailsFromFirebaseIdentifier,
//      checkIdentifierIsEmail,
//      getUserIdFromFirebaseIdentifier,
//      getAllUsersByUserRole,
//      addNewUserAdministration,
//      updateUserAdministration,
//      getUserAdministrationByUserId,
//      getAllAdminUsersByUserRole,
//      ifUserExistOnCreateWithSameNameLastName,
// };
