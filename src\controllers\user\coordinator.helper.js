const userConstants = require("../../utils/constants/user.const.js");

//import userRolesConstants.js
const userRolesConstants = require("../../utils/constants/userRolesConstants.js");

const sectorModel = require("../../models/Sectors.Model.js");

//import user model
const User = require("../../models/User.Models.js");
const { $where } = require("../../models/ApiLog.Model.js");

const userAdministrationHelper = require("./user.administration.helper.js");

const mongoose = require("mongoose");

//get list of user with userRole = coordinator
exports.getCoordinatorList = async () => {
  const coordinatorList = await User.find({
    userRole: userRolesConstants.COORDINATOR,
  });

  return coordinatorList;
};

//get list of user with userRole = coordinator and establishmentId = establishmentId
exports.getCoordinatorListByEstablishmentId = async (sectorId) => {
  try {
    if (!sectorId) {
      return { status: false, message: "No sector found", data: [] };
      // throw new Error("No sectorId found")
    }
    const sectorObjectId = mongoose.Types.ObjectId(sectorId);
    console.log("sectorObjectId", sectorObjectId);
    const sectorsDocument = await sectorModel
      .findOne({
        _id: sectorObjectId,
      })
      .exec();

    if (!sectorsDocument)
      return { status: false, message: "No sector found", data: [] };
    const coordinatorListInSector = sectorsDocument.coordinators;

    let coordinatorList = [];
    for (let i = 0; i < coordinatorListInSector.length; i++) {
      const coordinator = coordinatorListInSector[i];
      const coordinatorId = coordinator.userId;
      const coordinatorProfile = await userAdministrationHelper.getAdminProfile(
        coordinatorId
      );
      if (coordinatorProfile.status) {
        coordinatorList.push(coordinatorProfile.data);
      }
    }
    return coordinatorList;
  } catch (error) {
    console.log(error);
    throw new Error(error.message);
  }
};
